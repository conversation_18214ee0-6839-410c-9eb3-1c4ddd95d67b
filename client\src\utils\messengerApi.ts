/**
 * Messenger API utilities with retry logic and error handling
 */

interface RetryOptions {
  maxRetries?: number;
  baseDelay?: number;
  maxDelay?: number;
  backoffFactor?: number;
}

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

class MessengerApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public code?: string,
    public retryable: boolean = false
  ) {
    super(message);
    this.name = 'MessengerApiError';
  }
}

/**
 * Sleep utility for delays
 */
const sleep = (ms: number): Promise<void> => 
  new Promise(resolve => setTimeout(resolve, ms));

/**
 * Check if an error is retryable
 */
const isRetryableError = (error: any, status?: number): boolean => {

  if (error.name === 'TypeError' && error.message.includes('fetch')) {
    return true;
  }


  if (status && status >= 500) {
    return true;
  }


  if (status === 429) {
    return true;
  }


  if (error.name === 'AbortError') {
    return true;
  }

  return false;
};

/**
 * Make API request with retry logic
 */
export const apiRequest = async <T = any>(
  url: string,
  options: RequestInit = {},
  retryOptions: RetryOptions = {}
): Promise<ApiResponse<T>> => {
  const {
    maxRetries = 3,
    baseDelay = 1000,
    maxDelay = 30000,
    backoffFactor = 2
  } = retryOptions;

  let lastError: Error | null = null;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

      const response = await fetch(url, {
        ...options,
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
      });

      clearTimeout(timeoutId);


      let data: any;
      try {
        data = await response.json();
      } catch (parseError) {

        data = {
          success: false,
          message: response.ok ? 'Invalid response format' : `HTTP ${response.status}: ${response.statusText}`
        };
      }


      if (response.ok) {
        return {
          success: true,
          data: data.success !== false ? (data.data || data) : undefined,
          message: data.message
        };
      }


      const error = new MessengerApiError(
        data.message || `HTTP ${response.status}: ${response.statusText}`,
        response.status,
        data.code,
        isRetryableError(data, response.status)
      );


      if (response.status >= 400 && response.status < 500 && response.status !== 429) {
        throw error;
      }

      lastError = error;

    } catch (error: any) {
      lastError = error;


      if (!isRetryableError(error, error.status)) {
        break;
      }


      if (attempt === maxRetries) {
        break;
      }


      const delay = Math.min(baseDelay * Math.pow(backoffFactor, attempt), maxDelay);
      
      console.warn(`Messenger API request failed (attempt ${attempt + 1}/${maxRetries + 1}), retrying in ${delay}ms:`, error.message);
      
      await sleep(delay);
    }
  }


  return {
    success: false,
    message: lastError?.message || 'Request failed after all retries',
    error: lastError?.message || 'Unknown error occurred'
  };
};

/**
 * Messenger-specific API methods
 */
export const messengerApi = {
  /**
   * Get connection status
   */
  getStatus: (channelId: number) =>
    apiRequest(`/api/messenger/${channelId}/status`),

  /**
   * Get folder counts
   */
  getFolderCounts: (channelId: number) =>
    apiRequest(`/api/messenger/${channelId}/folder-counts`),

  /**
   * Get conversations
   */
  getConversations: (channelId: number, params: {
    folder?: string;
    search?: string;
    page?: number;
    limit?: number;
  } = {}) => {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        searchParams.append(key, value.toString());
      }
    });
    
    return apiRequest(`/api/messenger/${channelId}/conversations?${searchParams}`);
  },

  /**
   * Get messages for a conversation
   */
  getMessages: (channelId: number, conversationId: number, params: {
    page?: number;
    limit?: number;
  } = {}) => {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        searchParams.append(key, value.toString());
      }
    });
    
    return apiRequest(`/api/messenger/${channelId}/conversations/${conversationId}/messages?${searchParams}`);
  },

  /**
   * Send a message
   */
  sendMessage: (channelId: number, data: {
    conversationId?: number;
    message: string;
    type?: string;
    to?: string;
  }) =>
    apiRequest(`/api/messenger/${channelId}/send`, {
      method: 'POST',
      body: JSON.stringify(data),
    }),

  /**
   * Send a message with file attachment
   */
  sendMessageWithFile: (channelId: number, formData: FormData) =>
    apiRequest(`/api/messenger/${channelId}/send`, {
      method: 'POST',
      body: formData,
      headers: {}, // Don't set Content-Type for FormData
    }),
};

export { MessengerApiError };
