import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useQuery } from '@tanstack/react-query';

interface ChannelConnection {
  id: number;
  channelType: string;
  accountName?: string;
  accountId?: string;
  status: 'active' | 'inactive' | 'error';
  lastConnectedAt?: string;
  metadata?: any;
}

interface ActiveChannelContextType {
  activeChannelId: number | null;
  setActiveChannelId: (channelId: number | null) => void;
  activeChannel: ChannelConnection | null;
  availableChannels: ChannelConnection[];
  isLoading: boolean;
  error: Error | null;
}

const ActiveChannelContext = createContext<ActiveChannelContextType | undefined>(undefined);

interface ActiveChannelProviderProps {
  children: ReactNode;
}

const ACTIVE_CHANNEL_STORAGE_KEY = 'powerchat_active_channel_id';

export function ActiveChannelProvider({ children }: ActiveChannelProviderProps) {
  const [activeChannelId, setActiveChannelIdState] = useState<number | null>(null);


  const { data: channels = [], isLoading, error } = useQuery<ChannelConnection[]>({
    queryKey: ['/api/channel-connections'],
    queryFn: async () => {
      const response = await fetch('/api/channel-connections');
      if (!response.ok) {
        throw new Error('Failed to fetch channel connections');
      }
      return response.json();
    },
    refetchInterval: 30000, // Refresh every 30 seconds
  });


  const availableChannels = channels.filter(channel => channel.status === 'active');
  

  const activeChannel = availableChannels.find(channel => channel.id === activeChannelId) || null;


  useEffect(() => {
    const savedChannelId = localStorage.getItem(ACTIVE_CHANNEL_STORAGE_KEY);
    if (savedChannelId) {
      const channelId = parseInt(savedChannelId);
      if (!isNaN(channelId)) {
        setActiveChannelIdState(channelId);
      }
    }
  }, []);


  useEffect(() => {
    if (availableChannels.length > 0) {

      if (!activeChannelId) {
        const firstChannel = availableChannels[0];
        setActiveChannelIdState(firstChannel.id);
        localStorage.setItem(ACTIVE_CHANNEL_STORAGE_KEY, firstChannel.id.toString());

      }

      else if (!availableChannels.find(c => c.id === activeChannelId)) {
        const firstChannel = availableChannels[0];
        const previousChannelId = activeChannelId;
        setActiveChannelIdState(firstChannel.id);
        localStorage.setItem(ACTIVE_CHANNEL_STORAGE_KEY, firstChannel.id.toString());

      }
    } else if (activeChannelId) {


      setActiveChannelIdState(null);
      localStorage.removeItem(ACTIVE_CHANNEL_STORAGE_KEY);
    }
  }, [availableChannels, activeChannelId]);

  const setActiveChannelId = (channelId: number | null) => {
    setActiveChannelIdState(channelId);
    
    if (channelId) {
      localStorage.setItem(ACTIVE_CHANNEL_STORAGE_KEY, channelId.toString());
    } else {
      localStorage.removeItem(ACTIVE_CHANNEL_STORAGE_KEY);
    }
  };

  const contextValue: ActiveChannelContextType = {
    activeChannelId,
    setActiveChannelId,
    activeChannel,
    availableChannels,
    isLoading,
    error: error as Error | null,
  };

  return (
    <ActiveChannelContext.Provider value={contextValue}>
      {children}
    </ActiveChannelContext.Provider>
  );
}

export function useActiveChannel(): ActiveChannelContextType {
  const context = useContext(ActiveChannelContext);
  if (context === undefined) {
    throw new Error('useActiveChannel must be used within an ActiveChannelProvider');
  }
  return context;
}


export function useChannelInfo() {
  const getChannelDisplayName = (channel: ChannelConnection): string => {
    const typeDisplay = getChannelTypeDisplay(channel.channelType);
    if (channel.accountName) {
      return `${typeDisplay} (${channel.accountName})`;
    }
    return typeDisplay;
  };

  const getChannelTypeDisplay = (channelType: string): string => {
    switch (channelType) {
      case 'whatsapp_official':
        return 'WhatsApp Official';
      case 'whatsapp_unofficial':
      case 'whatsapp':
        return 'WhatsApp';
      case 'messenger':
        return 'Messenger';
      case 'instagram':
        return 'Instagram';
      case 'telegram':
        return 'Telegram';
      case 'email':
        return 'Email';
      default:
        return channelType;
    }
  };

  const getChannelIcon = (channelType: string) => {
    switch (channelType) {
      case 'whatsapp_official':
        return <i className="ri-whatsapp-line" style={{ color: '#25D366' }} />;
      case 'whatsapp_unofficial':
      case 'whatsapp':
        return <i className="ri-whatsapp-line" style={{ color: '#F59E0B' }} />;
      case 'messenger':
        return <i className="ri-messenger-line" style={{ color: '#1877F2' }} />;
      case 'instagram':
        return <i className="ri-instagram-line" style={{ color: '#E4405F' }} />;
      case 'telegram':
        return <i className="ri-telegram-line" style={{ color: '#0088CC' }} />;
      case 'email':
        return <i className="ri-mail-line" style={{ color: '#6B7280' }} />;
      default:
        return <i className="ri-chat-1-line" style={{ color: '#333235' }} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return {
    getChannelDisplayName,
    getChannelTypeDisplay,
    getChannelIcon,
    getStatusColor,
  };
}
