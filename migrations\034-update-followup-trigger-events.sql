-- Update follow-up schedules trigger event constraint
-- Replace 'conversation_start' with 'duration' and keep other existing values

-- First, drop the existing check constraint
ALTER TABLE follow_up_schedules DROP CONSTRAINT IF EXISTS follow_up_schedules_trigger_event_check;

-- Update any existing 'conversation_start' records to 'duration' 
-- with a default 5 minute delay
UPDATE follow_up_schedules 
SET 
    trigger_event = 'duration',
    delay_amount = 5,
    delay_unit = 'minutes'
WHERE trigger_event = 'conversation_start';

-- Add the new check constraint with updated allowed values
ALTER TABLE follow_up_schedules 
ADD CONSTRAINT follow_up_schedules_trigger_event_check 
CHECK (trigger_event IN ('duration', 'node_execution', 'specific_datetime', 'relative_delay'));

-- Update the default value for new records
ALTER TABLE follow_up_schedules 
ALTER COLUMN trigger_event SET DEFAULT 'duration';
