import { useState, useRef, useEffect } from 'react';
import { useTranslation } from '@/hooks/use-translation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/use-toast';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  ArrowLeft,
  Phone,
  Video,
  Info,
  MoreVertical,
  Send,
  Paperclip,
  Smile,
  Check,
  CheckCheck,
  Clock,
  Play,
  Camera,
  Image,
  FileText,
  X,
  Reply,
  Copy,
  Trash2,
  Forward,
  Heart,
  ThumbsUp,
  Laugh,
  CornerUpLeft,
  Download
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { formatDistanceToNow } from 'date-fns';
import './MessengerViewer.css';

interface MessengerConversation {
  id: number;
  contactId: number;
  contact: {
    id: number;
    name: string;
    phone: string;
    profilePicture?: string;
  };
  unreadCount: number;
  isOnline?: boolean;
  lastSeen?: string;
}

interface MessengerMessage {
  id: number;
  conversationId: number;
  content: string;
  type: 'text' | 'media' | 'quick_reply' | 'postback';
  direction: 'inbound' | 'outbound';
  status: 'sent' | 'delivered' | 'read';
  mediaUrl?: string;
  createdAt: string;
  metadata?: any;
  replyToMessageId?: number;
  replyToMessage?: {
    id: number;
    content: string;
    direction: 'inbound' | 'outbound';
    senderName?: string;
  };
  reactions?: {
    type: 'like' | 'love' | 'laugh' | 'angry' | 'sad';
    count: number;
    userReacted?: boolean;
  }[];
}

interface MessengerViewerProps {
  conversation: MessengerConversation;
  messages: MessengerMessage[];
  channelId: number;
  isLoading: boolean;
  onLoadMore: () => void;
  hasMore: boolean;
  isLoadingMore: boolean;
  onBackToList?: () => void;
  onMessageDeleted?: (messageId: number) => void;
}

export default function MessengerViewer({
  conversation,
  messages,
  channelId,
  isLoading,
  onLoadMore,
  hasMore,
  isLoadingMore,
  onBackToList,
  onMessageDeleted
}: MessengerViewerProps) {
  const { t } = useTranslation();
  const { toast } = useToast();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [messageText, setMessageText] = useState('');
  const [isSending, setIsSending] = useState(false);


  const [showContactInfo, setShowContactInfo] = useState(false);
  const [showAttachmentMenu, setShowAttachmentMenu] = useState(false);
  const [isUploadingFile, setIsUploadingFile] = useState(false);


  const [replyToMessage, setReplyToMessage] = useState<MessengerMessage | null>(null);


  const [hoveredMessageId, setHoveredMessageId] = useState<number | null>(null);
  const [contextMenuMessage, setContextMenuMessage] = useState<MessengerMessage | null>(null);
  const [contextMenuPosition, setContextMenuPosition] = useState<{ x: number; y: number } | null>(null);


  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);


  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showAttachmentMenu && !(event.target as Element).closest('.attachment-menu')) {
        setShowAttachmentMenu(false);
      }
      if (contextMenuMessage && !(event.target as Element).closest('.context-menu')) {
        setContextMenuMessage(null);
        setContextMenuPosition(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showAttachmentMenu, contextMenuMessage]);


  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      if (container.scrollTop === 0 && hasMore && !isLoadingMore) {
        const previousScrollHeight = container.scrollHeight;
        onLoadMore();
        

        setTimeout(() => {
          const newScrollHeight = container.scrollHeight;
          container.scrollTop = newScrollHeight - previousScrollHeight;
        }, 100);
      }
    };

    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, [hasMore, isLoadingMore, onLoadMore]);

  const handleSendMessage = async () => {
    if (!messageText.trim() || isSending) return;

    const originalMessage = messageText.trim();
    setIsSending(true);

    try {
      const requestBody: any = {
        conversationId: conversation.id,
        message: originalMessage,
        type: 'text'
      };


      if (replyToMessage) {
        requestBody.replyToMessageId = replyToMessage.id;
      }

      const response = await fetch(`/api/messenger/${channelId}/send`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP ${response.status}: Failed to send message`);
      }

      if (!data.success) {
        throw new Error(data.message || 'Failed to send message');
      }


      setMessageText('');
      setReplyToMessage(null);




    } catch (error: any) {
      console.error('Error sending message:', error);


      alert(`Failed to send message: ${error.message}`);


      setMessageText(originalMessage);
    } finally {
      setIsSending(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    } else if (e.key === 'Escape' && replyToMessage) {
      e.preventDefault();
      setReplyToMessage(null);
    }
  };


  const handlePhoneCall = () => {
    const phoneNumber = conversation.contact.phone;
    if (phoneNumber) {
      window.open(`tel:${phoneNumber}`, '_self');
    } else {
      toast({
        title: t('messenger.no_phone', 'No Phone Number'),
        description: t('messenger.no_phone_desc', 'This contact does not have a phone number available.'),
        variant: 'destructive'
      });
    }
  };

  const handleVideoCall = () => {
    toast({
      title: t('messenger.video_call', 'Video Call'),
      description: t('messenger.video_call_desc', 'Video calling is not available for Messenger contacts. This feature is coming soon.'),
    });
  };

  const handleShowContactInfo = () => {
    setShowContactInfo(true);
  };

  const handleFileUpload = async (file: File) => {
    if (!file) return;

    setIsUploadingFile(true);
    const formData = new FormData();
    formData.append('file', file);
    formData.append('conversationId', conversation.id.toString());

    try {
      const response = await fetch(`/api/messenger/${channelId}/upload`, {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to upload file');
      }

      if (!data.success) {
        throw new Error(data.message || 'Failed to upload file');
      }

      toast({
        title: t('messenger.file_uploaded', 'File Uploaded'),
        description: t('messenger.file_uploaded_desc', 'Your file has been sent successfully.'),
      });

    } catch (error: any) {
      console.error('Error uploading file:', error);
      toast({
        title: t('messenger.upload_error', 'Upload Error'),
        description: error.message || t('messenger.upload_error_desc', 'Failed to upload file. Please try again.'),
        variant: 'destructive'
      });
    } finally {
      setIsUploadingFile(false);
      setShowAttachmentMenu(false);
    }
  };

  const handleAttachmentClick = () => {
    setShowAttachmentMenu(!showAttachmentMenu);
  };

  const handleFileSelect = (type: 'image' | 'file') => {
    const input = fileInputRef.current;
    if (input) {
      input.accept = type === 'image' ? 'image/*' : '*/*';
      input.click();
    }
  };


  const handleReplyToMessage = (message: MessengerMessage) => {
    setReplyToMessage(message);
    setContextMenuMessage(null);
    setContextMenuPosition(null);

    setTimeout(() => {
      const input = document.querySelector('input[placeholder*="Type a message"]') as HTMLInputElement;
      if (input) input.focus();
    }, 100);
  };

  const handleCopyMessage = async (message: MessengerMessage) => {
    try {
      await navigator.clipboard.writeText(message.content);
      toast({
        title: t('messenger.copied', 'Copied'),
        description: t('messenger.message_copied', 'Message copied to clipboard'),
      });
    } catch (error) {
      toast({
        title: t('messenger.copy_error', 'Copy Error'),
        description: t('messenger.copy_error_desc', 'Failed to copy message to clipboard'),
        variant: 'destructive'
      });
    }
    setContextMenuMessage(null);
    setContextMenuPosition(null);
  };

  const handleDeleteMessage = async (message: MessengerMessage) => {
    if (message.direction !== 'outbound') return;


    if (!window.confirm(t('messenger.delete_confirm', 'Are you sure you want to delete this message? This action cannot be undone.'))) {
      setContextMenuMessage(null);
      setContextMenuPosition(null);
      return;
    }

    try {
      const response = await fetch(`/api/messenger/${channelId}/messages/${message.id}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to delete message');
      }

      if (!data.success) {
        throw new Error(data.message || 'Failed to delete message');
      }

      toast({
        title: t('messenger.deleted', 'Message Deleted'),
        description: t('messenger.message_deleted', 'Message has been deleted'),
      });


      if (onMessageDeleted) {
        onMessageDeleted(message.id);
      }

    } catch (error: any) {
      console.error('Error deleting message:', error);
      toast({
        title: t('messenger.delete_error', 'Delete Error'),
        description: error.message || t('messenger.delete_error_desc', 'Failed to delete message'),
        variant: 'destructive'
      });
    }
    setContextMenuMessage(null);
    setContextMenuPosition(null);
  };

  const handleMessageContextMenu = (e: React.MouseEvent, message: MessengerMessage) => {
    e.preventDefault();
    setContextMenuMessage(message);
    setContextMenuPosition({ x: e.clientX, y: e.clientY });
  };

  const handleScrollToMessage = (messageId: number) => {
    const messageElement = document.getElementById(`message-${messageId}`);
    if (messageElement) {
      messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });

      messageElement.classList.add('highlight-message');
      setTimeout(() => {
        messageElement.classList.remove('highlight-message');
      }, 2000);
    }
  };

  const getMessageStatusIcon = (message: MessengerMessage) => {
    if (message.direction !== 'outbound') return null;

    switch (message.status) {
      case 'sent':
        return <Check className="h-3 w-3 text-gray-400" />;
      case 'delivered':
        return <CheckCheck className="h-3 w-3 text-gray-400" />;
      case 'read':
        return <CheckCheck className="h-3 w-3 text-blue-500" />;
      default:
        return <Clock className="h-3 w-3 text-gray-400" />;
    }
  };

  const getInitials = (name: string): string => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  const formatMessageTime = (dateString: string): string => {
    try {
      const date = new Date(dateString);
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } catch {
      return '';
    }
  };

  const renderMessage = (message: MessengerMessage, index: number) => {
    const isOutbound = message.direction === 'outbound';
    const showAvatar = !isOutbound && (index === 0 || messages[index - 1]?.direction === 'outbound');
    const showTime = index === messages.length - 1 ||
      new Date(messages[index + 1]?.createdAt).getTime() - new Date(message.createdAt).getTime() > 300000; // 5 minutes
    const isHovered = hoveredMessageId === message.id;

    return (
      <div
        key={message.id}
        id={`message-${message.id}`}
        className={cn(
          "group flex items-end gap-3 mb-4 relative transition-all duration-200",
          isOutbound ? "justify-end" : "justify-start"
        )}
        onMouseEnter={() => setHoveredMessageId(message.id)}
        onMouseLeave={() => setHoveredMessageId(null)}
        onContextMenu={(e) => handleMessageContextMenu(e, message)}
      >
        {!isOutbound && (
          <div className="w-8 h-8 flex-shrink-0">
            {showAvatar && (
              <Avatar className="h-8 w-8 ring-2 ring-white shadow-sm">
                <AvatarImage
                  src={conversation.contact.profilePicture}
                  alt={conversation.contact.name}
                />
                <AvatarFallback className="bg-gradient-to-br from-blue-100 to-blue-200 text-blue-700 text-xs font-medium">
                  {getInitials(conversation.contact.name)}
                </AvatarFallback>
              </Avatar>
            )}
          </div>
        )}

        <div className={cn(
          "max-w-xs lg:max-w-md relative",
          isOutbound ? "order-1" : "order-2"
        )}>
          {/* Reply indicator */}
          {message.replyToMessage && (
            <div
              className={cn(
                "mb-2 p-2 rounded-lg border-l-4 cursor-pointer transition-colors",
                isOutbound
                  ? "bg-blue-400 bg-opacity-20 border-blue-300 text-blue-100"
                  : "bg-gray-200 border-gray-400 text-gray-600"
              )}
              onClick={() => handleScrollToMessage(message.replyToMessage!.id)}
            >
              <div className="flex items-center gap-2 mb-1">
                <CornerUpLeft className="h-3 w-3" />
                <span className="text-xs font-medium">
                  {message.replyToMessage.direction === 'outbound' ? 'You' : conversation.contact.name}
                </span>
              </div>
              <p className="text-xs opacity-80 truncate">
                {message.replyToMessage.content}
              </p>
            </div>
          )}

          {/* Message Bubble */}
          <div className={cn(
            "px-4 py-3 rounded-2xl relative shadow-sm transition-all duration-200",
            isOutbound
              ? "bg-gradient-to-br from-blue-500 to-blue-600 text-white rounded-br-md shadow-blue-200"
              : "bg-white text-gray-900 rounded-bl-md border border-gray-100 shadow-gray-100",
            isHovered && "shadow-md transform scale-[1.02]"
          )}>
            {message.type === 'media' && message.mediaUrl ? (
              <div className="space-y-3">
                {message.mediaUrl.match(/\.(jpg|jpeg|png|gif|webp)$/i) ? (
                  <div className="relative group">
                    <img
                      src={message.mediaUrl}
                      alt="Media"
                      className="rounded-xl max-w-full h-auto shadow-sm"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-200 rounded-xl flex items-center justify-center">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="opacity-0 group-hover:opacity-100 transition-opacity bg-white bg-opacity-20 hover:bg-opacity-30"
                        onClick={() => window.open(message.mediaUrl, '_blank')}
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ) : message.mediaUrl.match(/\.(mp4|webm|ogg)$/i) ? (
                  <div className="relative">
                    <video
                      src={message.mediaUrl}
                      className="rounded-xl max-w-full h-auto shadow-sm"
                      controls
                    />
                  </div>
                ) : (
                  <div className={cn(
                    "flex items-center gap-3 p-3 rounded-lg transition-colors",
                    isOutbound ? "bg-white bg-opacity-20" : "bg-gray-50"
                  )}>
                    <FileText className="h-5 w-5 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">Document</p>
                      <p className="text-xs opacity-70">Click to download</p>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => window.open(message.mediaUrl, '_blank')}
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                  </div>
                )}
                {message.content && (
                  <p className="text-sm leading-relaxed">{message.content}</p>
                )}
              </div>
            ) : (
              <p className="text-sm leading-relaxed whitespace-pre-wrap">{message.content}</p>
            )}

            {/* Message reactions */}
            {message.reactions && message.reactions.length > 0 && (
              <div className="flex gap-1 mt-2 flex-wrap">
                {message.reactions.map((reaction, idx) => (
                  <div
                    key={idx}
                    className={cn(
                      "flex items-center gap-1 px-2 py-1 rounded-full text-xs transition-colors cursor-pointer",
                      isOutbound
                        ? "bg-white bg-opacity-20 hover:bg-opacity-30"
                        : "bg-gray-100 hover:bg-gray-200",
                      reaction.userReacted && "ring-2 ring-blue-400"
                    )}
                  >
                    {reaction.type === 'like' && <ThumbsUp className="h-3 w-3" />}
                    {reaction.type === 'love' && <Heart className="h-3 w-3" />}
                    {reaction.type === 'laugh' && <Laugh className="h-3 w-3" />}
                    <span>{reaction.count}</span>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Message actions on hover */}
          {isHovered && (
            <div className={cn(
              "absolute top-0 flex gap-1 transition-all duration-200",
              isOutbound ? "-left-20" : "-right-20"
            )}>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 bg-white shadow-md hover:shadow-lg border"
                onClick={() => handleReplyToMessage(message)}
                title={t('messenger.reply', 'Reply')}
              >
                <Reply className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 bg-white shadow-md hover:shadow-lg border"
                onClick={() => handleCopyMessage(message)}
                title={t('messenger.copy', 'Copy')}
              >
                <Copy className="h-3 w-3" />
              </Button>
              {isOutbound && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 bg-white shadow-md hover:shadow-lg border text-red-600 hover:text-red-700"
                  onClick={() => handleDeleteMessage(message)}
                  title={t('messenger.delete', 'Delete')}
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              )}
            </div>
          )}

          {/* Message Info */}
          {showTime && (
            <div className={cn(
              "flex items-center gap-2 mt-2 text-xs text-gray-500",
              isOutbound ? "justify-end" : "justify-start"
            )}>
              <span>{formatMessageTime(message.createdAt)}</span>
              {isOutbound && (
                <div className="flex items-center gap-1">
                  {getMessageStatusIcon(message)}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-white">
        <div className="flex items-center gap-3">
          {onBackToList && (
            <Button variant="ghost" size="sm" onClick={onBackToList}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
          )}
          
          <div className="relative">
            <Avatar className="h-10 w-10">
              <AvatarImage 
                src={conversation.contact.profilePicture} 
                alt={conversation.contact.name}
              />
              <AvatarFallback className="bg-blue-100 text-blue-700 font-medium">
                {getInitials(conversation.contact.name)}
              </AvatarFallback>
            </Avatar>
            {conversation.isOnline && (
              <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
            )}
          </div>

          <div>
            <h3 className="font-medium text-gray-900">{conversation.contact.name}</h3>
            <p className="text-sm text-gray-500">
              {conversation.isOnline 
                ? t('messenger.online', 'Online')
                : conversation.lastSeen 
                  ? t('messenger.last_seen', 'Last seen {{time}}', { 
                      time: formatDistanceToNow(new Date(conversation.lastSeen), { addSuffix: true })
                    })
                  : t('messenger.offline', 'Offline')
              }
            </p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={handlePhoneCall}
            title={t('messenger.call', 'Call')}
          >
            <Phone className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleVideoCall}
            title={t('messenger.video_call', 'Video Call')}
          >
            <Video className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleShowContactInfo}
            title={t('messenger.contact_info', 'Contact Info')}
          >
            <Info className="h-4 w-4" />
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleShowContactInfo}>
                {t('messenger.view_profile', 'View Profile')}
              </DropdownMenuItem>
              <DropdownMenuItem>
                {t('messenger.mute_conversation', 'Mute Conversation')}
              </DropdownMenuItem>
              <DropdownMenuItem className="text-red-600">
                {t('messenger.block_contact', 'Block Contact')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Messages */}
      <div
        ref={messagesContainerRef}
        className="messages-container flex-1 overflow-y-auto p-4 bg-gray-50"
      >
        {/* Loading More Messages */}
        {isLoadingMore && (
          <div className="flex justify-center mb-4 py-2">
            <div className="flex items-center gap-2 text-sm text-gray-500 bg-gray-50 px-3 py-2 rounded-full">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
              {t('messenger.loading_messages', 'Loading messages...')}
            </div>
          </div>
        )}

        {/* Messages */}
        {isLoading && messages.length === 0 ? (
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, index) => {
              const isOutbound = index % 2 === 1;
              return (
                <div key={index} className={cn(
                  "flex items-end gap-2 animate-pulse",
                  isOutbound ? "justify-end" : "justify-start"
                )}>
                  {!isOutbound && <Skeleton className="h-8 w-8 rounded-full bg-gray-200" />}
                  <div className={cn(
                    "max-w-xs lg:max-w-md",
                    isOutbound ? "order-1" : "order-2"
                  )}>
                    <Skeleton className={cn(
                      "h-10 rounded-2xl bg-gray-200",
                      isOutbound ? "rounded-br-md" : "rounded-bl-md",
                      index % 3 === 0 ? "w-48" : index % 3 === 1 ? "w-32" : "w-40"
                    )} />
                    <Skeleton className="h-3 w-16 mt-1 bg-gray-100" />
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div>
            {messages.map((message, index) => renderMessage(message, index))}
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>

      {/* Message Input */}
      <div className="border-t border-gray-200 bg-white">
        {/* Reply Preview */}
        {replyToMessage && (
          <div className="reply-preview px-4 pt-3 pb-2 bg-gray-50 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Reply className="h-4 w-4" />
                <span className="font-medium">
                  {t('messenger.replying_to', 'Replying to')} {replyToMessage.direction === 'outbound' ? 'You' : conversation.contact.name}
                </span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={() => setReplyToMessage(null)}
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
            <div className="mt-2 p-2 bg-white rounded border-l-4 border-blue-400">
              <p className="text-sm text-gray-700 truncate">{replyToMessage.content}</p>
            </div>
          </div>
        )}

        <div className="p-4">
          <div className="flex items-end gap-2 relative">
          {/* Attachment Menu */}
          {showAttachmentMenu && (
            <div className="attachment-menu absolute bottom-12 left-0 bg-white border border-gray-200 rounded-lg shadow-lg p-2 z-10">
              <div className="flex flex-col gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  className="justify-start gap-2"
                  onClick={() => handleFileSelect('image')}
                  disabled={isUploadingFile}
                >
                  <Image className="h-4 w-4 text-green-600" />
                  {t('messenger.attach_image', 'Photo')}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="justify-start gap-2"
                  onClick={() => handleFileSelect('file')}
                  disabled={isUploadingFile}
                >
                  <FileText className="h-4 w-4 text-blue-600" />
                  {t('messenger.attach_file', 'Document')}
                </Button>
              </div>
            </div>
          )}

          <Button
            variant="ghost"
            size="sm"
            onClick={handleAttachmentClick}
            disabled={isUploadingFile}
            title={t('messenger.attach', 'Attach')}
          >
            {isUploadingFile ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
            ) : (
              <Paperclip className="h-4 w-4" />
            )}
          </Button>

          <div className="flex-1 relative">
            <Input
              value={messageText}
              onChange={(e) => setMessageText(e.target.value)}
              onKeyDown={handleKeyPress}
              placeholder={t('messenger.type_message', 'Type a message...')}
              className="pr-10 rounded-full border-gray-300 focus:border-blue-500"
              disabled={isSending}
            />
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-1 top-1/2 transform -translate-y-1/2"
              title={t('messenger.emoji', 'Emoji')}
            >
              <Smile className="h-4 w-4" />
            </Button>
          </div>

          <Button
            onClick={handleSendMessage}
            disabled={!messageText.trim() || isSending}
            className="rounded-full bg-blue-500 hover:bg-blue-600 text-white"
            size="sm"
            title={t('messenger.send', 'Send')}
          >
            {isSending ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Send className="h-4 w-4" />
            )}
          </Button>
        </div>

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          className="hidden"
          onChange={(e) => {
            const file = e.target.files?.[0];
            if (file) {
              handleFileUpload(file);
            }
          }}
        />
        </div>
      </div>

      {/* Context Menu */}
      {contextMenuMessage && contextMenuPosition && (
        <div
          className="context-menu fixed bg-white border border-gray-200 rounded-lg shadow-lg py-2 z-50"
          style={{
            left: contextMenuPosition.x,
            top: contextMenuPosition.y,
          }}
          onMouseLeave={() => {
            setContextMenuMessage(null);
            setContextMenuPosition(null);
          }}
        >
          <button
            className="w-full px-4 py-2 text-left text-sm hover:bg-gray-100 flex items-center gap-2"
            onClick={() => handleReplyToMessage(contextMenuMessage)}
          >
            <Reply className="h-4 w-4" />
            {t('messenger.reply', 'Reply')}
          </button>
          <button
            className="w-full px-4 py-2 text-left text-sm hover:bg-gray-100 flex items-center gap-2"
            onClick={() => handleCopyMessage(contextMenuMessage)}
          >
            <Copy className="h-4 w-4" />
            {t('messenger.copy', 'Copy')}
          </button>
          {contextMenuMessage.direction === 'outbound' && (
            <>
              <div className="border-t border-gray-200 my-1"></div>
              <button
                className="w-full px-4 py-2 text-left text-sm hover:bg-red-50 flex items-center gap-2 text-red-600 hover:text-red-700 transition-colors"
                onClick={() => handleDeleteMessage(contextMenuMessage)}
              >
                <Trash2 className="h-4 w-4" />
                {t('messenger.delete', 'Delete')}
              </button>
            </>
          )}
        </div>
      )}

      {/* Contact Info Modal */}
      <Dialog open={showContactInfo} onOpenChange={setShowContactInfo}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-3">
              <Avatar className="h-12 w-12">
                <AvatarImage
                  src={conversation.contact.profilePicture}
                  alt={conversation.contact.name}
                />
                <AvatarFallback className="bg-blue-100 text-blue-700 font-medium text-lg">
                  {getInitials(conversation.contact.name)}
                </AvatarFallback>
              </Avatar>
              <div>
                <h3 className="text-lg font-semibold">{conversation.contact.name}</h3>
                <p className="text-sm text-gray-500">
                  {conversation.isOnline
                    ? t('messenger.online', 'Online')
                    : conversation.lastSeen
                      ? t('messenger.last_seen', 'Last seen {{time}}', {
                          time: formatDistanceToNow(new Date(conversation.lastSeen), { addSuffix: true })
                        })
                      : t('messenger.offline', 'Offline')
                  }
                </p>
              </div>
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label className="text-sm font-medium text-gray-700">
                {t('messenger.phone_number', 'Phone Number')}
              </Label>
              <p className="text-sm text-gray-900 mt-1">
                {conversation.contact.phone || t('messenger.no_phone_available', 'Not available')}
              </p>
            </div>

            <div>
              <Label className="text-sm font-medium text-gray-700">
                {t('messenger.conversation_id', 'Conversation ID')}
              </Label>
              <p className="text-sm text-gray-900 mt-1">#{conversation.id}</p>
            </div>

            <div>
              <Label className="text-sm font-medium text-gray-700">
                {t('messenger.contact_id', 'Contact ID')}
              </Label>
              <p className="text-sm text-gray-900 mt-1">#{conversation.contact.id}</p>
            </div>

            <div className="flex gap-2 pt-4">
              <Button
                variant="outline"
                className="flex-1"
                onClick={handlePhoneCall}
                disabled={!conversation.contact.phone}
              >
                <Phone className="h-4 w-4 mr-2" />
                {t('messenger.call', 'Call')}
              </Button>
              <Button
                variant="outline"
                className="flex-1"
                onClick={() => setShowContactInfo(false)}
              >
                <X className="h-4 w-4 mr-2" />
                {t('messenger.close', 'Close')}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
