import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { useChannelConnections } from '@/hooks/useChannelConnections';
import { useTranslation } from '@/hooks/use-translation';
import {
  AlertTriangle,
  ArrowLeft,
  ArrowRight,
  CheckCircle,
  Clock,
  Edit,
  Eye,
  MessageSquare,
  Plus,
  Save,
  Send,
  Settings,
  Shield,
  Users,
  X
} from 'lucide-react';
import { useEffect, useState, useRef } from 'react';
import { useLocation, useParams } from 'wouter';
import { CreateSegmentModal } from './CreateSegmentModal';
import { CreateTemplateModal } from './CreateTemplateModal';
import { EditSegmentModal } from './EditSegmentModal';
import { EditTemplateModal } from './EditTemplateModal';
import { VariableInsertion } from './VariableInsertion';

interface CampaignTemplate {
  id: number;
  name: string;
  content: string;
  category: string;
  variables: string[];
  mediaUrls?: string[];
}

interface ContactSegment {
  id: number;
  name: string;
  description: string;
  contactCount: number;
}

interface CampaignData {
  name: string;
  description: string;
  templateId?: number;
  segmentId?: number;
  content: string;
  mediaUrls?: string[];
  channelType: string;
  channelId?: number;
  channelIds?: number[];
  campaignType: 'immediate' | 'scheduled' | 'drip';
  scheduledAt?: string;
  rateLimitSettings: {
    messages_per_minute: number;
    messages_per_hour: number;
    messages_per_day: number;
    delay_between_messages: number;
    humanization_enabled: boolean;
  };
  antiBanSettings: {
    enabled: boolean;
    mode: 'conservative' | 'moderate' | 'aggressive';
    businessHoursOnly: boolean;
    respectWeekends: boolean;
    randomizeDelay: boolean;
    minDelay: number;
    maxDelay: number;
    accountRotation: boolean;
    cooldownPeriod: number;
    messageVariation: boolean;
  };
}

export function CampaignBuilder() {
  const [location, setLocation] = useLocation();
  const params = useParams();
  const isEditMode = location.includes('/edit');
  const campaignId = isEditMode ? parseInt(params.id || '0') : null;
  const { t } = useTranslation();

  const CAMPAIGN_STEPS = [
    { id: 'basic', title: t('campaigns.builder.steps.basic', 'Basic Info'), icon: MessageSquare },
    { id: 'audience', title: t('campaigns.builder.steps.audience', 'Audience'), icon: Users },
    { id: 'content', title: t('campaigns.builder.steps.content', 'Content'), icon: MessageSquare },
    { id: 'settings', title: t('campaigns.builder.steps.settings', 'Settings'), icon: Settings },
    { id: 'antiban', title: t('campaigns.builder.steps.antiban', 'Anti-Ban'), icon: Shield },
    { id: 'review', title: t('campaigns.builder.steps.review', 'Review'), icon: Eye }
  ];

  const [currentStep, setCurrentStep] = useState(0);
  const [campaignData, setCampaignData] = useState<CampaignData>({
    name: '',
    description: '',
    content: '',
    mediaUrls: [],
    channelType: 'whatsapp',
    campaignType: 'immediate',
    rateLimitSettings: {
      messages_per_minute: 10,
      messages_per_hour: 200,
      messages_per_day: 1000,
      delay_between_messages: 6,
      humanization_enabled: true
    },
    antiBanSettings: {
      enabled: true,
      mode: 'moderate',
      businessHoursOnly: false,
      respectWeekends: false,
      randomizeDelay: true,
      minDelay: 3,
      maxDelay: 15,
      accountRotation: true,
      cooldownPeriod: 30,
      messageVariation: false
    }
  });

  const [templates, setTemplates] = useState<CampaignTemplate[]>([]);
  const [segments, setSegments] = useState<ContactSegment[]>([]);
  const [contentValidation, setContentValidation] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [isSegmentModalOpen, setIsSegmentModalOpen] = useState(false);
  const [isTemplateModalOpen, setIsTemplateModalOpen] = useState(false);
  const [editSegmentId, setEditSegmentId] = useState<number | null>(null);
  const [editTemplateId, setEditTemplateId] = useState<number | null>(null);
  const [showLaunchConfirmation, setShowLaunchConfirmation] = useState(false);
  const contentTextareaRef = useRef<HTMLTextAreaElement>(null);
  const { toast } = useToast();

  const { data: channelConnections = [], isLoading: connectionsLoading } = useChannelConnections();

  const whatsappConnections = channelConnections.filter(
    (conn: any) => conn.channelType === 'whatsapp_unofficial' && conn.status === 'active'
  );

  useEffect(() => {
    fetchTemplates();
    fetchSegments();

    if (isEditMode && campaignId) {
      fetchCampaignData(campaignId);
    }
  }, [isEditMode, campaignId]);

  useEffect(() => {
    if (campaignData.content) {
      validateContent();
    }
  }, [campaignData.content]);

  const fetchTemplates = async () => {
    try {
      const response = await fetch('/api/campaigns/templates');
      const data = await response.json();
      if (data.success) {
        setTemplates(data.data);
      }
    } catch (error) {
      console.error('Failed to fetch templates:', error);
    }
  };

  const fetchSegments = async () => {
    try {
      const response = await fetch('/api/campaigns/segments');
      const data = await response.json();
      if (data.success) {
        setSegments(data.data);
      }
    } catch (error) {
      console.error('Failed to fetch segments:', error);
    }
  };

  const fetchCampaignData = async (id: number) => {
    try {
      const response = await fetch(`/api/campaigns/${id}`);
      const data = await response.json();
      if (data.success) {
        const campaign = data.data;
        setCampaignData({
          name: campaign.name || '',
          description: campaign.description || '',
          content: campaign.content || '',
          mediaUrls: campaign.mediaUrls || [],
          channelType: campaign.channelType || 'whatsapp_unofficial',
          channelId: campaign.channelId,
          channelIds: campaign.channelIds || [],
          templateId: campaign.templateId,
          segmentId: campaign.segmentId,
          campaignType: campaign.campaignType || 'immediate',
          scheduledAt: campaign.scheduledAt ? new Date(campaign.scheduledAt).toISOString().slice(0, 16) : '',
          rateLimitSettings: campaign.rateLimitSettings || {
            messages_per_minute: 10,
            messages_per_hour: 200,
            messages_per_day: 1000,
            delay_between_messages: 6,
            humanization_enabled: true,
          },
          antiBanSettings: campaign.antiBanSettings || {
            enabled: true,
            mode: 'moderate',
            businessHoursOnly: false,
            respectWeekends: false,
            randomizeDelay: true,
            minDelay: 3,
            maxDelay: 15,
            accountRotation: true,
            cooldownPeriod: 30,
            messageVariation: false
          }
        });
      }
    } catch (error) {
      console.error('Failed to fetch campaign data:', error);
      toast({
        title: t('common.error', 'Error'),
        description: t('campaigns.builder.messages.load_error', 'Failed to load campaign data'),
        variant: 'destructive'
      });
    }
  };

  const validateContent = async () => {
    try {
      const response = await fetch('/api/campaigns/validate-content', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ content: campaignData.content })
      });
      const data = await response.json();
      if (data.success) {
        setContentValidation(data.data);
      }
    } catch (error) {
      console.error('Failed to validate content:', error);
    }
  };

  const handleTemplateSelect = (templateId: string) => {
    const template = templates.find(t => t.id === parseInt(templateId));
    if (template) {
      setCampaignData(prev => ({
        ...prev,
        templateId: template.id,
        content: template.content,
        mediaUrls: template.mediaUrls || []
      }));
    }
  };

  const handleSegmentSelect = (segmentId: string) => {
    setCampaignData(prev => ({
      ...prev,
      segmentId: parseInt(segmentId)
    }));
  };

  const handleSegmentCreated = (newSegment: ContactSegment) => {
    setSegments(prev => [newSegment, ...prev]);

    setCampaignData(prev => ({
      ...prev,
      segmentId: newSegment.id
    }));

    toast({
      title: t('common.success', 'Success'),
      description: t('campaigns.builder.messages.segment_created', 'Segment "{{name}}" created and selected', { name: newSegment.name })
    });
  };

  const handleTemplateCreated = (newTemplate: CampaignTemplate) => {
    setTemplates(prev => [newTemplate, ...prev]);

    setCampaignData(prev => ({
      ...prev,
      templateId: newTemplate.id,
      content: newTemplate.content,
      mediaUrls: newTemplate.mediaUrls || []
    }));

    toast({
      title: t('common.success', 'Success'),
      description: t('campaigns.builder.messages.template_created', 'Template "{{name}}" created and selected', { name: newTemplate.name })
    });
  };

  const handleTemplateUpdated = (updatedTemplate: CampaignTemplate | null) => {
    if (updatedTemplate === null) {
      setTemplates(prev => prev.filter(t => t.id !== editTemplateId));
      if (campaignData.templateId === editTemplateId) {
        setCampaignData(prev => ({
          ...prev,
          templateId: undefined,
          content: ''
        }));
      }
    } else {
      setTemplates(prev => prev.map(t => t.id === updatedTemplate.id ? updatedTemplate : t));
      if (campaignData.templateId === updatedTemplate.id) {
        setCampaignData(prev => ({
          ...prev,
          content: updatedTemplate.content,
          mediaUrls: updatedTemplate.mediaUrls || []
        }));
      }
    }
    setEditTemplateId(null);
  };

  const handleSegmentUpdated = (updatedSegment: ContactSegment | null) => {
    if (updatedSegment === null) {
      setSegments(prev => prev.filter(s => s.id !== editSegmentId));
      if (campaignData.segmentId === editSegmentId) {
        setCampaignData(prev => ({
          ...prev,
          segmentId: undefined
        }));
      }
    } else {
      setSegments(prev => prev.map(s => s.id === updatedSegment.id ? updatedSegment : s));
    }
    setEditSegmentId(null);
  };

  const handleNext = () => {
    if (currentStep < CAMPAIGN_STEPS.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const resetCampaignBuilder = () => {
    setCampaignData({
      name: '',
      description: '',
      content: '',
      mediaUrls: [],
      channelType: 'whatsapp',
      campaignType: 'immediate',
      rateLimitSettings: {
        messages_per_minute: 10,
        messages_per_hour: 200,
        messages_per_day: 1000,
        delay_between_messages: 6,
        humanization_enabled: true
      },
      antiBanSettings: {
        enabled: true,
        mode: 'moderate',
        businessHoursOnly: false,
        respectWeekends: false,
        randomizeDelay: true,
        minDelay: 3,
        maxDelay: 15,
        accountRotation: true,
        cooldownPeriod: 30,
        messageVariation: false
      }
    });

    setCurrentStep(0);

    setContentValidation(null);

    setIsSegmentModalOpen(false);
    setIsTemplateModalOpen(false);
    setEditSegmentId(null);
    setEditTemplateId(null);
    setShowLaunchConfirmation(false);
  };

  const validateCampaignData = () => {
    const errors: string[] = [];

    if (!campaignData.name.trim()) {
      errors.push(t('campaigns.builder.validation.name_required', 'Campaign name is required'));
    }

    if (!campaignData.channelId && (!campaignData.channelIds || campaignData.channelIds.length === 0)) {
      errors.push(t('campaigns.builder.validation.connection_required', 'At least one WhatsApp connection is required'));
    }

    if (!campaignData.segmentId) {
      errors.push(t('campaigns.builder.validation.segment_required', 'Audience segment is required'));
    }

    if (!campaignData.content.trim()) {
      errors.push(t('campaigns.builder.validation.content_required', 'Message content is required'));
    }

    if (campaignData.campaignType === 'scheduled' && !campaignData.scheduledAt) {
      errors.push(t('campaigns.builder.validation.schedule_required', 'Scheduled date and time is required for scheduled campaigns'));
    }

    return errors;
  };

  const handleSaveDraft = async () => {
    setLoading(true);
    try {
      const url = isEditMode ? `/api/campaigns/${campaignId}` : '/api/campaigns';
      const method = isEditMode ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(campaignData)
      });

      const data = await response.json();
      if (data.success) {
        toast({
          title: t('common.success', 'Success'),
          description: isEditMode ? t('campaigns.builder.messages.update_success', 'Campaign updated successfully') : t('campaigns.builder.messages.save_success', 'Campaign saved as draft')
        });
      } else {
        throw new Error(data.error);
      }
    } catch (error) {
      toast({
        title: t('common.error', 'Error'),
        description: isEditMode ? t('campaigns.builder.messages.update_error', 'Failed to update campaign') : t('campaigns.builder.messages.save_error', 'Failed to save campaign'),
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleLaunchClick = () => {
    const validationErrors = validateCampaignData();
    if (validationErrors.length > 0) {
      toast({
        title: t('campaigns.builder.validation.error_title', 'Validation Error'),
        description: validationErrors.join(', '),
        variant: 'destructive'
      });
      return;
    }

    setShowLaunchConfirmation(true);
  };

  const handleLaunchConfirm = async () => {
    setShowLaunchConfirmation(false);
    setLoading(true);

    try {
      const response = await fetch('/api/campaigns', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(campaignData)
      });

      const data = await response.json();
      if (data.success) {
        const campaignId = data.data.id;

        const startResponse = await fetch(`/api/campaigns/${campaignId}/start`, {
          method: 'POST'
        });

        const startData = await startResponse.json();
        if (startData.success) {
          const selectedSegment = segments.find(s => s.id === campaignData.segmentId);
          const contactCount = selectedSegment?.contactCount || 0;

          toast({
            title: t('campaigns.builder.messages.launch_success_title', 'Campaign Launched Successfully! 🚀'),
            description: t('campaigns.builder.messages.launch_success_description', '"{{name}}" is now running and will send messages to {{count}} contacts', { name: campaignData.name, count: contactCount })
          });

          resetCampaignBuilder();

          setTimeout(() => {
            setLocation('/campaigns');
          }, 2000);

        } else {
          throw new Error(startData.error);
        }
      } else {
        throw new Error(data.error);
      }
    } catch (error) {
      console.error('Campaign launch error:', error);
      toast({
        title: t('campaigns.builder.messages.launch_error_title', 'Launch Failed'),
        description: error instanceof Error ? error.message : t('campaigns.builder.messages.launch_error_description', 'Failed to launch campaign. Please try again.'),
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const renderStepContent = () => {
    const step = CAMPAIGN_STEPS[currentStep];

    switch (step.id) {
      case 'basic':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="name">{t('campaigns.builder.basic.name_label', 'Campaign Name')}</Label>
              <Input
                id="name"
                value={campaignData.name}
                onChange={(e) => setCampaignData(prev => ({ ...prev, name: e.target.value }))}
                placeholder={t('campaigns.builder.basic.name_placeholder', 'Enter campaign name')}
              />
            </div>

            <div>
              <Label htmlFor="description">{t('campaigns.builder.basic.description_label', 'Description')}</Label>
              <Textarea
                id="description"
                value={campaignData.description}
                onChange={(e) => setCampaignData(prev => ({ ...prev, description: e.target.value }))}
                placeholder={t('campaigns.builder.basic.description_placeholder', 'Describe your campaign')}
                rows={3}
              />
            </div>

            <div>
              <Label htmlFor="channelIds">{t('campaigns.builder.basic.connections_label', 'WhatsApp Connections')}</Label>
              <p className="text-sm text-muted-foreground mb-2">
                {t('campaigns.builder.basic.connections_description', 'Select multiple WhatsApp accounts for better distribution and anti-ban protection')}
              </p>
              {connectionsLoading ? (
                <div className="flex items-center justify-center p-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                </div>
              ) : whatsappConnections.length === 0 ? (
                <div className="p-4 border border-dashed border-gray-300 rounded-lg text-center">
                  <MessageSquare className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-600 mb-2">{t('campaigns.builder.basic.no_connections', 'No WhatsApp connections available')}</p>
                  <p className="text-xs text-gray-500">
                    {t('campaigns.builder.basic.setup_connection', 'Please set up a WhatsApp connection in Settings > Channel Connections first')}
                  </p>
                </div>
              ) : (
                <div className="space-y-2">
                  <div className="border rounded-lg p-3 space-y-2 max-h-48 overflow-y-auto">
                    {whatsappConnections.map((connection: any) => {
                      const isSelected = campaignData.channelIds?.includes(connection.id) ||
                                       (campaignData.channelId === connection.id && !campaignData.channelIds?.length);

                      return (
                        <div key={connection.id} className="flex items-center space-x-3 p-2 hover:bg-muted rounded">
                          <input
                            type="checkbox"
                            id={`connection-${connection.id}`}
                            checked={isSelected}
                            onChange={(e) => {
                              const isChecked = e.target.checked;
                              setCampaignData(prev => {
                                let newChannelIds = prev.channelIds || [];

                                if (isChecked) {
                                  if (!newChannelIds.includes(connection.id)) {
                                    newChannelIds = [...newChannelIds, connection.id];
                                  }
                                } else {
                                  newChannelIds = newChannelIds.filter(id => id !== connection.id);
                                }

                                return {
                                  ...prev,
                                  channelIds: newChannelIds,
                                  channelId: newChannelIds.length > 0 ? newChannelIds[0] : undefined,
                                  channelType: 'whatsapp_unofficial'
                                };
                              });
                            }}
                            className="rounded border-gray-300 text-primary focus:ring-primary"
                          />
                          <label htmlFor={`connection-${connection.id}`} className="flex items-center gap-2 flex-1 cursor-pointer">
                            <i className="ri-whatsapp-line text-green-600"></i>
                            <span className="font-medium">{connection.accountName}</span>
                            <Badge variant="secondary" className="ml-auto">
                              {connection.status}
                            </Badge>
                          </label>
                        </div>
                      );
                    })}
                  </div>

                  {(campaignData.channelIds?.length || 0) > 0 && (
                    <div className="text-sm text-muted-foreground">
                      <strong>{campaignData.channelIds?.length || 0}</strong> {t('campaigns.builder.basic.accounts_selected', 'account(s) selected for distribution')}
                    </div>
                  )}

                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const allIds = whatsappConnections.map((conn: any) => conn.id);
                        setCampaignData(prev => ({
                          ...prev,
                          channelIds: allIds,
                          channelId: allIds[0],
                          channelType: 'whatsapp_unofficial'
                        }));
                      }}
                    >
                      {t('campaigns.builder.basic.select_all', 'Select All')}
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setCampaignData(prev => ({
                          ...prev,
                          channelIds: [],
                          channelId: undefined
                        }));
                      }}
                    >
                      {t('campaigns.builder.basic.clear_all', 'Clear All')}
                    </Button>
                  </div>
                </div>
              )}
            </div>

            <div>
              <Label htmlFor="campaignType">{t('campaigns.builder.basic.type_label', 'Campaign Type')}</Label>
              <Select
                value={campaignData.campaignType}
                onValueChange={(value: any) => setCampaignData(prev => ({ ...prev, campaignType: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="immediate">{t('campaigns.builder.basic.type_immediate', 'Send Immediately')}</SelectItem>
                  <SelectItem value="scheduled">{t('campaigns.builder.basic.type_scheduled', 'Schedule for Later')}</SelectItem>
                  <SelectItem value="drip">{t('campaigns.builder.basic.type_drip', 'Drip Campaign')}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {campaignData.campaignType === 'scheduled' && (
              <div>
                <Label htmlFor="scheduledAt">{t('campaigns.builder.basic.schedule_label', 'Scheduled Date & Time')}</Label>
                <Input
                  id="scheduledAt"
                  type="datetime-local"
                  value={campaignData.scheduledAt}
                  onChange={(e) => setCampaignData(prev => ({ ...prev, scheduledAt: e.target.value }))}
                />
              </div>
            )}
          </div>
        );

      case 'audience':
        return (
          <div className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-2">
                <Label>{t('campaigns.builder.audience.segment_label', 'Select Audience Segment')}</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setIsSegmentModalOpen(true)}
                  className="flex items-center gap-2"
                >
                  <Plus className="w-4 h-4" />
                  {t('campaigns.builder.audience.create_segment', 'Create New Segment')}
                </Button>
              </div>
              <div className="flex gap-2">
                <Select
                  value={campaignData.segmentId?.toString()}
                  onValueChange={handleSegmentSelect}
                >
                  <SelectTrigger className="flex-1">
                    <SelectValue placeholder={t('campaigns.builder.audience.segment_placeholder', 'Choose a segment')} />
                  </SelectTrigger>
                  <SelectContent>
                    {segments.map((segment) => (
                      <SelectItem key={segment.id} value={segment.id.toString()}>
                        <div className="flex justify-between items-center w-full">
                          <span>{segment.name}</span>
                          <Badge variant="secondary" className="ml-2">
                            {segment.contactCount} {t('campaigns.builder.audience.contacts', 'contacts')}
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {campaignData.segmentId && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setEditSegmentId(campaignData.segmentId!)}
                    className="flex items-center gap-2"
                  >
                    <Edit className="w-4 h-4" />
                    {t('common.edit', 'Edit')}
                  </Button>
                )}
              </div>
            </div>

            {campaignData.segmentId && (
              <Card>
                <CardContent className="pt-4">
                  {(() => {
                    const segment = segments.find(s => s.id === campaignData.segmentId);
                    return segment ? (
                      <div>
                        <h4 className="font-medium">{segment.name}</h4>
                        <p className="text-sm text-muted-foreground">{segment.description}</p>
                        <p className="text-sm mt-2">
                          <strong>{segment.contactCount}</strong> {t('campaigns.builder.audience.will_receive', 'contacts will receive this campaign')}
                        </p>
                      </div>
                    ) : null;
                  })()}
                </CardContent>
              </Card>
            )}
          </div>
        );

      case 'content':
        return (
          <div className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-2">
                <Label>{t('campaigns.builder.content.template_label', 'Use Template (Optional)')}</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setIsTemplateModalOpen(true)}
                  className="flex items-center gap-2"
                >
                  <Plus className="w-4 h-4" />
                  {t('campaigns.builder.content.create_template', 'Create New Template')}
                </Button>
              </div>
              <div className="flex gap-2">
                <Select onValueChange={handleTemplateSelect} value={campaignData.templateId?.toString()}>
                  <SelectTrigger className="flex-1">
                    <SelectValue placeholder={t('campaigns.builder.content.template_placeholder', 'Choose a template')} />
                  </SelectTrigger>
                  <SelectContent>
                    {templates.map((template) => (
                      <SelectItem key={template.id} value={template.id.toString()}>
                        {template.name} ({template.category})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {campaignData.templateId && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setEditTemplateId(campaignData.templateId!)}
                    className="flex items-center gap-2"
                  >
                    <Edit className="w-4 h-4" />
                    {t('common.edit', 'Edit')}
                  </Button>
                )}
              </div>
            </div>

            <div>
              <Label htmlFor="content">{t('campaigns.builder.content.message_label', 'Message Content')}</Label>
              <Textarea
                ref={contentTextareaRef}
                id="content"
                value={campaignData.content}
                onChange={(e) => setCampaignData(prev => ({ ...prev, content: e.target.value }))}
                placeholder={t('campaigns.builder.content.message_placeholder', 'Enter your message content. Click \'Insert Variable\' to add personalization...')}
                rows={6}
              />

              <div className="mt-2">
                <VariableInsertion
                  textareaRef={contentTextareaRef}
                  value={campaignData.content}
                  onChange={(content) => setCampaignData(prev => ({ ...prev, content }))}
                  customVariables={['company', 'position', 'location', 'industry']}
                />
              </div>
            </div>

            {contentValidation && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertTriangle className="w-4 h-4" />
                    {t('campaigns.builder.content.validation_title', 'Content Validation')}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-2 mb-2">
                    <span>{t('campaigns.builder.content.validation_score', 'Score')}:</span>
                    <Badge variant={contentValidation.score >= 80 ? 'default' : 'destructive'}>
                      {contentValidation.score}/100
                    </Badge>
                  </div>
                  {contentValidation.issues.length > 0 && (
                    <div>
                      <p className="text-sm font-medium mb-2">{t('campaigns.builder.content.validation_issues', 'Issues')}:</p>
                      <ul className="text-sm space-y-1">
                        {contentValidation.issues.map((issue: string, index: number) => (
                          <li key={index} className="flex items-start gap-2">
                            <AlertTriangle className="w-3 h-3 mt-0.5 text-yellow-500" />
                            {issue}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </div>
        );

      case 'settings':
        return (
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>{t('campaigns.builder.settings.rate_limiting_title', 'Rate Limiting & Anti-Ban Settings')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="messagesPerMinute">{t('campaigns.builder.settings.messages_per_minute', 'Messages per Minute')}</Label>
                    <Input
                      id="messagesPerMinute"
                      type="number"
                      value={campaignData.rateLimitSettings.messages_per_minute}
                      onChange={(e) => setCampaignData(prev => ({
                        ...prev,
                        rateLimitSettings: {
                          ...prev.rateLimitSettings,
                          messages_per_minute: parseInt(e.target.value)
                        }
                      }))}
                    />
                  </div>

                  <div>
                    <Label htmlFor="messagesPerHour">{t('campaigns.builder.settings.messages_per_hour', 'Messages per Hour')}</Label>
                    <Input
                      id="messagesPerHour"
                      type="number"
                      value={campaignData.rateLimitSettings.messages_per_hour}
                      onChange={(e) => setCampaignData(prev => ({
                        ...prev,
                        rateLimitSettings: {
                          ...prev.rateLimitSettings,
                          messages_per_hour: parseInt(e.target.value)
                        }
                      }))}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="delayBetweenMessages">{t('campaigns.builder.settings.delay_between_messages', 'Delay Between Messages (seconds)')}</Label>
                  <Input
                    id="delayBetweenMessages"
                    type="number"
                    value={campaignData.rateLimitSettings.delay_between_messages}
                    onChange={(e) => setCampaignData(prev => ({
                      ...prev,
                      rateLimitSettings: {
                        ...prev.rateLimitSettings,
                        delay_between_messages: parseInt(e.target.value)
                      }
                    }))}
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case 'antiban':
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="w-5 h-5" />
                  {t('campaigns.builder.antiban.title', 'Anti-Ban Protection')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="antiBanEnabled">{t('campaigns.builder.antiban.enable_label', 'Enable Anti-Ban Protection')}</Label>
                    <p className="text-sm text-muted-foreground">
                      {t('campaigns.builder.antiban.enable_description', 'Automatically apply intelligent rate limiting and account rotation')}
                    </p>
                  </div>
                  <Switch
                    id="antiBanEnabled"
                    checked={campaignData.antiBanSettings.enabled}
                    onCheckedChange={(checked) => setCampaignData(prev => ({
                      ...prev,
                      antiBanSettings: { ...prev.antiBanSettings, enabled: checked }
                    }))}
                  />
                </div>

                {campaignData.antiBanSettings.enabled && (
                  <div className="space-y-4 pt-4 border-t">
                    <div>
                      <Label>{t('campaigns.builder.antiban.mode_label', 'Protection Mode')}</Label>
                      <p className="text-sm text-muted-foreground mb-2">
                        {t('campaigns.builder.antiban.mode_description', 'Choose how aggressive the anti-ban protection should be')}
                      </p>
                      <div className="grid grid-cols-3 gap-2">
                        {[
                          { value: 'conservative', label: t('campaigns.builder.antiban.mode_conservative', 'Conservative'), desc: t('campaigns.builder.antiban.mode_conservative_desc', 'Slowest, safest') },
                          { value: 'moderate', label: t('campaigns.builder.antiban.mode_moderate', 'Moderate'), desc: t('campaigns.builder.antiban.mode_moderate_desc', 'Balanced approach') },
                          { value: 'aggressive', label: t('campaigns.builder.antiban.mode_aggressive', 'Aggressive'), desc: t('campaigns.builder.antiban.mode_aggressive_desc', 'Faster, higher risk') }
                        ].map((mode) => (
                          <Button
                            key={mode.value}
                            type="button"
                            variant={campaignData.antiBanSettings.mode === mode.value ? 'default' : 'outline'}
                            className="h-auto p-3 flex flex-col items-center"
                            onClick={() => setCampaignData(prev => ({
                              ...prev,
                              antiBanSettings: { ...prev.antiBanSettings, mode: mode.value as any }
                            }))}
                          >
                            <span className="font-medium">{mode.label}</span>
                            <span className="text-xs text-muted-foreground">{mode.desc}</span>
                          </Button>
                        ))}
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <Label htmlFor="businessHoursOnly">{t('campaigns.builder.antiban.business_hours_label', 'Business Hours Only')}</Label>
                          <p className="text-sm text-muted-foreground">{t('campaigns.builder.antiban.business_hours_desc', 'Send only during 9 AM - 6 PM')}</p>
                        </div>
                        <Switch
                          id="businessHoursOnly"
                          checked={campaignData.antiBanSettings.businessHoursOnly}
                          onCheckedChange={(checked) => setCampaignData(prev => ({
                            ...prev,
                            antiBanSettings: { ...prev.antiBanSettings, businessHoursOnly: checked }
                          }))}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <Label htmlFor="respectWeekends">{t('campaigns.builder.antiban.respect_weekends_label', 'Respect Weekends')}</Label>
                          <p className="text-sm text-muted-foreground">{t('campaigns.builder.antiban.respect_weekends_desc', 'Pause on weekends')}</p>
                        </div>
                        <Switch
                          id="respectWeekends"
                          checked={campaignData.antiBanSettings.respectWeekends}
                          onCheckedChange={(checked) => setCampaignData(prev => ({
                            ...prev,
                            antiBanSettings: { ...prev.antiBanSettings, respectWeekends: checked }
                          }))}
                        />
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <Label htmlFor="randomizeDelay">{t('campaigns.builder.antiban.randomize_delays_label', 'Randomize Delays')}</Label>
                          <p className="text-sm text-muted-foreground">{t('campaigns.builder.antiban.randomize_delays_desc', 'Add human-like variance to message timing')}</p>
                        </div>
                        <Switch
                          id="randomizeDelay"
                          checked={campaignData.antiBanSettings.randomizeDelay}
                          onCheckedChange={(checked) => setCampaignData(prev => ({
                            ...prev,
                            antiBanSettings: { ...prev.antiBanSettings, randomizeDelay: checked }
                          }))}
                        />
                      </div>

                      {campaignData.antiBanSettings.randomizeDelay && (
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="minDelay">{t('campaigns.builder.antiban.min_delay_label', 'Min Delay (seconds)')}</Label>
                            <Input
                              id="minDelay"
                              type="number"
                              min="1"
                              max="60"
                              value={campaignData.antiBanSettings.minDelay}
                              onChange={(e) => setCampaignData(prev => ({
                                ...prev,
                                antiBanSettings: { ...prev.antiBanSettings, minDelay: parseInt(e.target.value) || 3 }
                              }))}
                            />
                          </div>
                          <div>
                            <Label htmlFor="maxDelay">{t('campaigns.builder.antiban.max_delay_label', 'Max Delay (seconds)')}</Label>
                            <Input
                              id="maxDelay"
                              type="number"
                              min="1"
                              max="300"
                              value={campaignData.antiBanSettings.maxDelay}
                              onChange={(e) => setCampaignData(prev => ({
                                ...prev,
                                antiBanSettings: { ...prev.antiBanSettings, maxDelay: parseInt(e.target.value) || 15 }
                              }))}
                            />
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <Label htmlFor="accountRotation">{t('campaigns.builder.antiban.account_rotation_label', 'Smart Account Rotation')}</Label>
                          <p className="text-sm text-muted-foreground">{t('campaigns.builder.antiban.account_rotation_desc', 'Distribute messages across selected accounts')}</p>
                        </div>
                        <Switch
                          id="accountRotation"
                          checked={campaignData.antiBanSettings.accountRotation}
                          onCheckedChange={(checked) => setCampaignData(prev => ({
                            ...prev,
                            antiBanSettings: { ...prev.antiBanSettings, accountRotation: checked }
                          }))}
                        />
                      </div>

                      <div>
                        <Label htmlFor="cooldownPeriod">{t('campaigns.builder.antiban.cooldown_period_label', 'Account Cooldown Period (minutes)')}</Label>
                        <p className="text-sm text-muted-foreground mb-2">
                          {t('campaigns.builder.antiban.cooldown_period_desc', 'Rest time between high-volume sending for each account')}
                        </p>
                        <Input
                          id="cooldownPeriod"
                          type="number"
                          min="5"
                          max="1440"
                          value={campaignData.antiBanSettings.cooldownPeriod}
                          onChange={(e) => setCampaignData(prev => ({
                            ...prev,
                            antiBanSettings: { ...prev.antiBanSettings, cooldownPeriod: parseInt(e.target.value) || 30 }
                          }))}
                        />
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="messageVariation">{t('campaigns.builder.antiban.message_variation_label', 'Message Variation')}</Label>
                        <p className="text-sm text-muted-foreground">{t('campaigns.builder.antiban.message_variation_desc', 'Add slight variations to avoid identical content')}</p>
                      </div>
                      <Switch
                        id="messageVariation"
                        checked={campaignData.antiBanSettings.messageVariation}
                        onCheckedChange={(checked) => setCampaignData(prev => ({
                          ...prev,
                          antiBanSettings: { ...prev.antiBanSettings, messageVariation: checked }
                        }))}
                      />
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {(campaignData.channelIds?.length || 0) > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="w-5 h-5" />
                    {t('campaigns.builder.antiban.account_health_title', 'Account Health Status')}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {whatsappConnections
                      .filter((conn: any) => campaignData.channelIds?.includes(conn.id))
                      .map((connection: any) => (
                        <div key={connection.id} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center gap-3">
                            <i className="ri-whatsapp-line text-green-600"></i>
                            <div>
                              <p className="font-medium">{connection.accountName}</p>
                              <p className="text-sm text-muted-foreground">
                                {t('campaigns.builder.antiban.last_active', 'Last active')}: {new Date(connection.lastActiveAt || Date.now()).toLocaleDateString()}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant={connection.status === 'active' ? 'default' : 'secondary'}>
                              {connection.status}
                            </Badge>
                            <div className="flex items-center gap-1">
                              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                              <span className="text-sm text-muted-foreground">{t('campaigns.builder.antiban.account_status_healthy', 'Healthy')}</span>
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        );

      case 'review':
        return (
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>{t('campaigns.builder.review.campaign_summary_title', 'Campaign Summary')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium">{t('campaigns.builder.review.basic_info_title', 'Basic Information')}</h4>
                  <p><strong>{t('campaigns.builder.review.name_label', 'Name')}:</strong> {campaignData.name}</p>
                  <p><strong>{t('campaigns.builder.review.description_label', 'Description')}:</strong> {campaignData.description}</p>
                  <p><strong>{t('campaigns.builder.review.channel_label', 'Channel')}:</strong> {campaignData.channelType}</p>
                  <p><strong>{t('campaigns.builder.review.type_label', 'Type')}:</strong> {campaignData.campaignType}</p>
                  {campaignData.campaignType === 'scheduled' && campaignData.scheduledAt && (
                    <p><strong>{t('campaigns.builder.review.scheduled_for_label', 'Scheduled for')}:</strong> {new Date(campaignData.scheduledAt).toLocaleString()}</p>
                  )}
                </div>

                <Separator />

                <div>
                  <h4 className="font-medium">{t('campaigns.builder.review.whatsapp_accounts_title', 'WhatsApp Accounts')}</h4>
                  {(campaignData.channelIds?.length || 0) > 0 ? (
                    <div className="space-y-2">
                      <p><strong>{campaignData.channelIds?.length || 0}</strong> {t('campaigns.builder.review.accounts_selected_for_distribution', 'account(s) selected for distribution')}:</p>
                      <div className="space-y-1">
                        {whatsappConnections
                          .filter((conn: any) => campaignData.channelIds?.includes(conn.id))
                          .map((connection: any) => (
                            <div key={connection.id} className="flex items-center gap-2 text-sm">
                              <i className="ri-whatsapp-line text-green-600"></i>
                              <span>{connection.accountName}</span>
                              <Badge variant="secondary" className="text-xs">
                                {connection.status}
                              </Badge>
                            </div>
                          ))}
                      </div>
                    </div>
                  ) : campaignData.channelId ? (
                    <p><strong>{t('campaigns.builder.review.single_account_label', 'Single Account')}:</strong> {
                      whatsappConnections.find((conn: any) => conn.id === campaignData.channelId)?.accountName || 'Unknown'
                    }</p>
                  ) : (
                    <p>{t('campaigns.builder.review.no_account_selected', 'No WhatsApp account selected')}</p>
                  )}
                </div>

                <Separator />

                <div>
                  <h4 className="font-medium">{t('campaigns.builder.review.audience_title', 'Audience')}</h4>
                  {(() => {
                    const segment = segments.find(s => s.id === campaignData.segmentId);
                    return segment ? (
                      <p><strong>{t('campaigns.builder.review.segment_label', 'Segment')}:</strong> {segment.name} ({segment.contactCount} {t('campaigns.builder.audience.contacts', 'contacts')})</p>
                    ) : (
                      <p>{t('campaigns.builder.review.no_segment_selected', 'No segment selected')}</p>
                    );
                  })()}
                </div>

                <Separator />

                <div>
                  <h4 className="font-medium">{t('campaigns.builder.review.antiban_title', 'Anti-Ban Protection')}</h4>
                  {campaignData.antiBanSettings.enabled ? (
                    <div className="space-y-1">
                      <p><strong>{t('campaigns.builder.review.antiban_mode_label', 'Mode')}:</strong> {campaignData.antiBanSettings.mode}</p>
                      <div className="flex flex-wrap gap-2">
                        {campaignData.antiBanSettings.businessHoursOnly && (
                          <Badge variant="outline">{t('campaigns.builder.antiban.business_hours_label', 'Business Hours Only')}</Badge>
                        )}
                        {campaignData.antiBanSettings.respectWeekends && (
                          <Badge variant="outline">{t('campaigns.builder.antiban.respect_weekends_label', 'Respect Weekends')}</Badge>
                        )}
                        {campaignData.antiBanSettings.randomizeDelay && (
                          <Badge variant="outline">
                            {t('campaigns.builder.review.random_delays_badge', 'Random Delays')} ({campaignData.antiBanSettings.minDelay}-{campaignData.antiBanSettings.maxDelay}s)
                          </Badge>
                        )}
                        {campaignData.antiBanSettings.accountRotation && (
                          <Badge variant="outline">{t('campaigns.builder.review.account_rotation_badge', 'Account Rotation')}</Badge>
                        )}
                        {campaignData.antiBanSettings.messageVariation && (
                          <Badge variant="outline">{t('campaigns.builder.antiban.message_variation_label', 'Message Variation')}</Badge>
                        )}
                      </div>
                    </div>
                  ) : (
                    <p>{t('campaigns.builder.review.antiban_disabled', 'Anti-ban protection disabled')}</p>
                  )}
                </div>

                <Separator />

                <div>
                  <h4 className="font-medium">{t('campaigns.builder.review.rate_limiting_title', 'Rate Limiting')}</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <p><strong>{t('campaigns.builder.review.per_minute_label', 'Per Minute')}:</strong> {campaignData.rateLimitSettings.messages_per_minute} {t('campaigns.builder.review.messages_unit', 'messages')}</p>
                    <p><strong>{t('campaigns.builder.review.per_hour_label', 'Per Hour')}:</strong> {campaignData.rateLimitSettings.messages_per_hour} {t('campaigns.builder.review.messages_unit', 'messages')}</p>
                    <p><strong>{t('campaigns.builder.review.per_day_label', 'Per Day')}:</strong> {campaignData.rateLimitSettings.messages_per_day} {t('campaigns.builder.review.messages_unit', 'messages')}</p>
                    <p><strong>{t('campaigns.builder.review.base_delay_label', 'Base Delay')}:</strong> {campaignData.rateLimitSettings.delay_between_messages} {t('campaigns.builder.review.seconds_unit', 'seconds')}</p>
                  </div>
                </div>

                <Separator />

                <div>
                  <h4 className="font-medium">{t('campaigns.builder.review.message_content_title', 'Message Content')}</h4>
                  <div className="bg-muted p-3 rounded-md">
                    <pre className="whitespace-pre-wrap text-sm">{campaignData.content}</pre>
                  </div>
                  {campaignData.mediaUrls && campaignData.mediaUrls.length > 0 && (
                    <div className="mt-2">
                      <p className="text-sm font-medium">{t('campaigns.builder.review.media_attachments_label', 'Media Attachments')}:</p>
                      <div className="flex flex-wrap gap-2 mt-1">
                        {campaignData.mediaUrls.map((url, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {t('campaigns.builder.review.media_item_label', 'Media')} {index + 1}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setLocation('/campaigns')}
        >
          <ArrowLeft className="w-4 h-4" />
        </Button>
        <div>
        <h1 className="text-2xl">
            {isEditMode ? t('campaigns.builder.header.edit', 'Edit Campaign') : t('campaigns.builder.header.create', 'Create Campaign')}
          </h1>
          <p className="text-muted-foreground">
            {t('campaigns.builder.header.step_progress', 'Step {{current}} of {{total}}: {{title}}', {
              current: currentStep + 1,
              total: CAMPAIGN_STEPS.length,
              title: CAMPAIGN_STEPS[currentStep].title
            })}
          </p>
        </div>
      </div>

      <div className="flex items-center justify-between">
        {CAMPAIGN_STEPS.map((step, index) => {
          const Icon = step.icon;
          const isActive = index === currentStep;
          const isCompleted = index < currentStep;

          return (
            <div key={step.id} className="flex items-center">
              <div className={
                `flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                  isActive
                    ? 'border-primary bg-primary text-primary-foreground'
                    : isCompleted
                      ? 'border-primary bg-primary text-primary-foreground'
                      : 'border-muted-foreground bg-background'
                }`
              }>
                <Icon className="w-4 h-4" />
              </div>
              <span className={`ml-2 text-sm ${isActive ? 'font-medium' : ''}`}>
                {step.title}
              </span>
              {index < CAMPAIGN_STEPS.length - 1 && (
                <div className="w-12 h-px bg-muted-foreground mx-4" />
              )}
            </div>
          );
        })}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{CAMPAIGN_STEPS[currentStep].title}</CardTitle>
        </CardHeader>
        <CardContent>
          {renderStepContent()}
        </CardContent>
      </Card>

      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={handlePrevious}
          disabled={currentStep === 0}
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          {t('campaigns.builder.navigation.previous', 'Previous')}
        </Button>

        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleSaveDraft}
            disabled={loading}
          >
            <Save className="w-4 h-4 mr-2" />
            {t('campaigns.builder.navigation.save_draft', 'Save Draft')}
          </Button>

          {currentStep === CAMPAIGN_STEPS.length - 1 ? (
            <Button
              onClick={handleLaunchClick}
              disabled={loading}
              className="min-w-[140px]"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {t('campaigns.builder.navigation.launching', 'Launching...')}
                </>
              ) : (
                <>
                  <Send className="w-4 h-4 mr-2" />
                  {t('campaigns.builder.navigation.launch', 'Launch Campaign')}
                </>
              )}
            </Button>
          ) : (
            <Button onClick={handleNext}>
              {t('campaigns.builder.navigation.next', 'Next')}
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          )}
        </div>
      </div>

      <CreateSegmentModal
        isOpen={isSegmentModalOpen}
        onClose={() => setIsSegmentModalOpen(false)}
        onSegmentCreated={handleSegmentCreated}
      />

      <CreateTemplateModal
        isOpen={isTemplateModalOpen}
        onClose={() => setIsTemplateModalOpen(false)}
        onTemplateCreated={handleTemplateCreated}
      />

      {editSegmentId && (
        <EditSegmentModal
          isOpen={editSegmentId !== null}
          onClose={() => setEditSegmentId(null)}
          segmentId={editSegmentId}
          onSegmentUpdated={handleSegmentUpdated}
        />
      )}

      {editTemplateId && (
        <EditTemplateModal
          isOpen={editTemplateId !== null}
          onClose={() => setEditTemplateId(null)}
          templateId={editTemplateId}
          onTemplateUpdated={handleTemplateUpdated}
        />
      )}

      <Dialog open={showLaunchConfirmation} onOpenChange={setShowLaunchConfirmation}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('campaigns.builder.launch.confirmation_title', 'Launch Campaign Confirmation')}</DialogTitle>
            <DialogDescription>
              {t('campaigns.builder.launch.confirmation_description', 'Are you sure you want to launch this campaign? This action cannot be undone.')}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="bg-muted p-4 rounded-lg">
              <h4 className="font-medium mb-2">{t('campaigns.builder.launch.summary_title', 'Campaign Summary')}:</h4>
              <div className="space-y-1 text-sm">
                <p><strong>{t('campaigns.builder.launch.summary_name', 'Name')}:</strong> {campaignData.name}</p>
                <p><strong>{t('campaigns.builder.launch.summary_type', 'Type')}:</strong> {campaignData.campaignType}</p>
                {(() => {
                  const segment = segments.find(s => s.id === campaignData.segmentId);
                  return segment ? (
                    <p><strong>{t('campaigns.builder.launch.summary_recipients', 'Recipients')}:</strong> {segment.contactCount} {t('campaigns.builder.audience.contacts', 'contacts')}</p>
                  ) : null;
                })()}
                {campaignData.campaignType === 'scheduled' && campaignData.scheduledAt && (
                  <p><strong>{t('campaigns.builder.launch.summary_scheduled', 'Scheduled for')}:</strong> {new Date(campaignData.scheduledAt).toLocaleString()}</p>
                )}
              </div>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 p-3 rounded-lg">
              <div className="flex items-start gap-2">
                <AlertTriangle className="w-4 h-4 text-yellow-600 mt-0.5" />
                <div className="text-sm text-yellow-800">
                  <p className="font-medium">{t('campaigns.builder.launch.warning_title', 'Important')}:</p>
                  <p>{t('campaigns.builder.launch.warning_description', 'Once launched, this campaign will start sending messages immediately and cannot be stopped.')}</p>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowLaunchConfirmation(false)}
              disabled={loading}
            >
              {t('common.cancel', 'Cancel')}
            </Button>
            <Button
              onClick={handleLaunchConfirm}
              disabled={loading}
              className="min-w-[120px]"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {t('campaigns.builder.navigation.launching', 'Launching...')}
                </>
              ) : (
                <>
                  <Send className="w-4 h-4 mr-2" />
                  {t('campaigns.builder.launch.confirm_button', 'Yes, Launch')}
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
