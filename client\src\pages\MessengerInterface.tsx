import { useState, useEffect, useCallback } from 'react';
import { useParams } from 'wouter';
import { useQuery, useInfiniteQuery, useQueryClient } from '@tanstack/react-query';

import { useTranslation } from '@/hooks/use-translation';
import useSocket from '@/hooks/useSocket';
import Header from '@/components/layout/Header';
import Sidebar from '@/components/layout/Sidebar';
import { MessengerSidebar, MessengerList, MessengerComposer, MessengerViewer, MessengerErrorBoundary } from '@/components/messenger';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search, Plus, RefreshCw, Menu, ArrowLeft } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';
import { messengerApi } from '@/utils/messengerApi';
import { useMessengerActions } from '@/hooks/use-messenger-actions';

interface MessengerConversation {
  id: number;
  contactId: number;
  contact: {
    id: number;
    name: string;
    phone: string;
    profilePicture?: string;
  };
  lastMessage?: {
    id: number;
    content: string;
    createdAt: string;
    direction: 'inbound' | 'outbound';
    type: string;
  };
  unreadCount: number;
  isOnline?: boolean;
  lastSeen?: string;
  createdAt: string;
  updatedAt: string;
  isStarred?: boolean;
  isArchived?: boolean;
}

interface MessengerMessage {
  id: number;
  conversationId: number;
  content: string;
  type: 'text' | 'media' | 'quick_reply' | 'postback';
  direction: 'inbound' | 'outbound';
  status: 'sent' | 'delivered' | 'read';
  mediaUrl?: string;
  createdAt: string;
  metadata?: any;
}

interface ConversationPage {
  conversations: MessengerConversation[];
  nextPage?: number;
  hasMore: boolean;
}

interface MessagePage {
  messages: MessengerMessage[];
  nextPage?: number;
  hasMore: boolean;
}

export default function MessengerInterface() {
  const { channelId } = useParams<{ channelId: string }>();
  const { t } = useTranslation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { starConversation, archiveConversation } = useMessengerActions();
  const socket = useSocket('/socket.io/');


  const [selectedConversation, setSelectedConversation] = useState<MessengerConversation | null>(null);
  const [isComposing, setIsComposing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFolder, setSelectedFolder] = useState('all');
  const [showMessengerSidebar, setShowMessengerSidebar] = useState(true);
  const [showConversationList, setShowConversationList] = useState(true);


  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);
  const [isDesktop, setIsDesktop] = useState(false);

  useEffect(() => {
    const checkScreenSize = () => {
      const width = window.innerWidth;
      setIsMobile(width < 768);
      setIsTablet(width >= 768 && width < 1024);
      setIsDesktop(width >= 1024);
    };

    checkScreenSize();
    const debouncedResize = debounce(checkScreenSize, 100);
    window.addEventListener('resize', debouncedResize);
    return () => window.removeEventListener('resize', debouncedResize);
  }, []);


  function debounce(func: Function, wait: number) {
    let timeout: NodeJS.Timeout;
    return function executedFunction(...args: any[]) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }


  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 300);
    return () => clearTimeout(timer);
  }, [searchQuery]);


  const {
    data: conversationPages,
    fetchNextPage: fetchNextConversationPage,
    hasNextPage: hasMoreConversations,
    isFetchingNextPage: isFetchingNextConversationPage,
    refetch: refetchConversations,
    isLoading: isLoadingConversations,
    error: conversationsError
  } = useInfiniteQuery<ConversationPage, Error>({
    queryKey: ['/api/messenger/conversations', channelId, selectedFolder, debouncedSearchQuery],
    queryFn: async ({ pageParam = 1 }): Promise<ConversationPage> => {
      const params = new URLSearchParams({
        folder: selectedFolder,
        page: (pageParam as number).toString(),
        limit: '20',
        ...(debouncedSearchQuery && { search: debouncedSearchQuery })
      });

      const response = await fetch(`/api/messenger/${channelId}/conversations?${params}`);
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: Failed to fetch conversations`);
      }

      const data = await response.json();

      console.log('📨 API Response:', {
        success: data.success,
        hasData: !!data.data,
        conversationsCount: data.data?.conversations?.length || 0,
        firstConversation: data.data?.conversations?.[0]
      });


      if (data.success && data.data) {

        const conversations = Array.isArray(data.data.conversations) ? data.data.conversations : [];


        const validConversations = conversations.filter((conv: any) => {
          if (!conv || typeof conv !== 'object') {
            console.warn('Invalid conversation object:', conv);
            return false;
          }
          return true;
        }).map((conv: any) => ({
          ...conv,
          contact: conv.contact || {
            id: 0,
            name: 'Unknown Contact',
            phone: conv.contactId?.toString() || 'Unknown',
            profilePicture: null
          }
        }));

        return {
          conversations: validConversations,
          nextPage: data.data.pagination?.nextPage,
          hasMore: data.data.pagination?.hasMore || false
        };
      }


      return {
        conversations: [],
        nextPage: undefined,
        hasMore: false
      };
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => lastPage.nextPage,
    enabled: !!channelId,
    refetchInterval: 30000, // Refresh every 30 seconds
    retry: (failureCount, error) => {

      if (failureCount >= 3) return false;
      if (error.message.includes('HTTP 4')) return false;
      return true;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });


  const {
    data: messagePages,
    fetchNextPage: fetchNextMessagePage,
    hasNextPage: hasMoreMessages,
    isFetchingNextPage: isFetchingNextMessagePage,
    refetch: refetchMessages,
    isLoading: isLoadingMessages,
    error: messagesError
  } = useInfiniteQuery<MessagePage, Error>({
    queryKey: ['/api/messenger/messages', channelId, selectedConversation?.id],
    queryFn: async ({ pageParam = 1 }): Promise<MessagePage> => {
      const params = new URLSearchParams({
        page: (pageParam as number).toString(),
        limit: '50'
      });

      const response = await fetch(`/api/messenger/${channelId}/conversations/${selectedConversation?.id}/messages?${params}`);
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: Failed to fetch messages`);
      }

      const data = await response.json();


      if (data.success && data.data) {
        return {
          messages: data.data.messages || [],
          nextPage: data.data.pagination?.nextPage,
          hasMore: data.data.pagination?.hasMore || false
        };
      }


      return {
        messages: data.messages || [],
        nextPage: data.nextPage,
        hasMore: data.hasMore || false
      };
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => lastPage.nextPage,
    enabled: !!channelId && !!selectedConversation,
    refetchInterval: 5000, // Refresh messages more frequently
    retry: (failureCount, error) => {
      if (failureCount >= 3) return false;
      if (error.message.includes('HTTP 4')) return false;
      return true;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });


  const conversations = conversationPages?.pages.flatMap(page => page.conversations) || [];
  const messages = messagePages?.pages.flatMap(page => page.messages) || [];


  useEffect(() => {
    if (!socket) return;

    const handleNewMessage = (data: any) => {
      if (data.channelType === 'messenger' && data.connection?.id === parseInt(channelId || '0')) {

        queryClient.invalidateQueries({ queryKey: ['/api/messenger/conversations'] });
        if (selectedConversation && data.message.conversationId === selectedConversation.id) {
          queryClient.invalidateQueries({ queryKey: ['/api/messenger/messages'] });
        }
      }
    };

    const handleMessageStatusUpdate = (data: any) => {
      if (data.channelType === 'messenger') {
        queryClient.invalidateQueries({ queryKey: ['/api/messenger/messages'] });
      }
    };

    const handleMessageDeleted = (data: any) => {

      refetchMessages();
      refetchConversations();


      toast({
        title: t('messenger.message_deleted_notification', 'Message Deleted'),
        description: t('messenger.message_deleted_by_user', 'A message was deleted'),
      });
    };

    const handleConversationDeleted = (data: any) => {

      if (selectedConversation?.id === data.conversationId) {
        setSelectedConversation(null);
      }


      refetchConversations();


      toast({
        title: t('messenger.conversation_deleted_notification', 'Conversation Deleted'),
        description: t('messenger.conversation_deleted_by_user', 'A conversation was deleted'),
      });
    };

    const unsubscribeNewMessage = socket.onMessage('messageReceived', handleNewMessage);
    const unsubscribeStatusUpdate = socket.onMessage('messageStatusUpdate', handleMessageStatusUpdate);
    const unsubscribeMessageDeleted = socket.onMessage('messageDeleted', handleMessageDeleted);
    const unsubscribeConversationDeleted = socket.onMessage('conversationDeleted', handleConversationDeleted);

    return () => {
      unsubscribeNewMessage();
      unsubscribeStatusUpdate();
      unsubscribeMessageDeleted();
      unsubscribeConversationDeleted();
    };
  }, [socket, channelId, selectedConversation, queryClient]);


  const handleConversationSelect = (conversation: MessengerConversation) => {
    setSelectedConversation(conversation);
    setIsComposing(false);

    if (isMobile) {
      setShowConversationList(false);
    }
  };



  const handleMessageSent = () => {
    setIsComposing(false);
    refetchConversations();
    refetchMessages();

    if (isMobile) {
      setShowConversationList(true);
    }

    toast({
      title: t('messenger.sent', 'Message Sent'),
      description: t('messenger.message_sent_successfully', 'Your message has been sent successfully'),
    });
  };

  const handleStarConversation = useCallback((conversationId: number) => {
    const conversation = conversations.find(c => c.id === conversationId);
    if (conversation) {
      starConversation({
        channelId: parseInt(channelId),
        conversationId,
        starred: !conversation.isStarred
      });
    }
  }, [conversations, channelId, starConversation]);

  const handleArchiveConversation = useCallback((conversationId: number) => {
    const conversation = conversations.find(c => c.id === conversationId);
    if (conversation) {
      archiveConversation({
        channelId: parseInt(channelId),
        conversationId,
        archived: !conversation.isArchived
      });
    }
  }, [conversations, channelId, archiveConversation]);

  const handleDeleteConversation = async (conversationId: number) => {

    if (!window.confirm(t('messenger.delete_conversation_confirm', 'Are you sure you want to delete this conversation? This will permanently delete all messages and cannot be undone.'))) {
      return;
    }

    try {
      const response = await fetch(`/api/messenger/${channelId}/conversations/${conversationId}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to delete conversation');
      }

      if (!data.success) {
        throw new Error(data.message || 'Failed to delete conversation');
      }


      if (selectedConversation?.id === conversationId) {
        setSelectedConversation(null);
      }


      refetchConversations();

      toast({
        title: t('messenger.conversation_deleted', 'Conversation Deleted'),
        description: t('messenger.conversation_deleted_desc', 'The conversation has been permanently deleted.'),
      });

    } catch (error: any) {
      console.error('Error deleting conversation:', error);
      toast({
        title: t('messenger.delete_error', 'Delete Error'),
        description: error.message || t('messenger.delete_error_desc', 'Failed to delete conversation.'),
        variant: 'destructive'
      });
    }
  };

  const handleBackToList = () => {
    setSelectedConversation(null);
    setIsComposing(false);
    setShowConversationList(true);
  };

  const loadMoreConversations = useCallback(() => {
    if (hasMoreConversations && !isFetchingNextConversationPage) {
      fetchNextConversationPage();
    }
  }, [hasMoreConversations, isFetchingNextConversationPage, fetchNextConversationPage]);

  const loadMoreMessages = useCallback(() => {
    if (hasMoreMessages && !isFetchingNextMessagePage) {
      fetchNextMessagePage();
    }
  }, [hasMoreMessages, isFetchingNextMessagePage, fetchNextMessagePage]);

  if (!channelId) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center text-gray-500">
          <div className="mb-4">
            <i className="ri-messenger-line text-6xl text-gray-300"></i>
          </div>
          <h3 className="text-lg font-medium mb-2">
            {t('messenger.no_channel', 'No Channel Selected')}
          </h3>
          <p className="text-sm">
            {t('messenger.select_channel', 'Please select a Messenger channel to continue')}
          </p>
        </div>
      </div>
    );
  }


  if (conversationsError && !isLoadingConversations) {
    return (
      <div className="h-screen flex flex-col overflow-hidden font-sans text-gray-800">
        <Header />
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center text-gray-500 max-w-md mx-auto p-8">
            <div className="mb-4">
              <i className="ri-error-warning-line text-6xl text-red-300"></i>
            </div>
            <h3 className="text-lg font-medium mb-2 text-red-600">
              {t('messenger.error_loading', 'Error Loading Conversations')}
            </h3>
            <p className="text-sm mb-4">
              {conversationsError.message || t('messenger.error_loading_desc', 'Unable to load conversations. Please try again.')}
            </p>
            <Button onClick={() => refetchConversations()} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              {t('common.retry', 'Retry')}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <MessengerErrorBoundary>
      <div className="h-screen flex flex-col overflow-hidden font-sans text-gray-800">
        <Header />

        <div className="flex flex-1 overflow-hidden relative">
        {/* Main Sidebar */}
        <div className={`${isMobile ? 'hidden' : 'flex'}`}>
          <Sidebar />
        </div>

        {/* Messenger Sidebar */}
        <div className={cn(
          "bg-gray-50 border-r border-gray-200 flex-shrink-0 transition-all duration-300",
          "h-full overflow-hidden flex flex-col",
          isMobile ? (showMessengerSidebar ? "w-full" : "hidden") : "w-64",
          isTablet ? "w-56" : ""
        )}>
          <MessengerSidebar
            selectedFolder={selectedFolder}
            onFolderSelect={setSelectedFolder}
            channelId={parseInt(channelId)}
          />
        </div>

        {/* Main Content Area */}
        <div className={cn(
          "flex-1 flex",
          isMobile && !showConversationList ? "w-full" : ""
        )}>
          {/* Conversation List */}
          <div className={cn(
            "bg-white border-r border-gray-200 flex flex-col transition-all duration-300",
            "h-full overflow-hidden",
            isMobile ? (showConversationList ? "w-full" : "hidden") : "w-96",
            isTablet ? "w-80" : ""
          )}>
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-sm font-medium text-gray-700">
                  {t('messenger.conversations', 'Conversations')}
                </h2>
                {(isMobile || isTablet) && !showMessengerSidebar && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowMessengerSidebar(true)}
                  >
                    <Menu className="h-4 w-4" />
                  </Button>
                )}
              </div>

              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={t('messenger.search_conversations', 'Search conversations...')}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 h-9 text-sm"
                />
              </div>
            </div>

            <MessengerList
              conversations={conversations}
              selectedConversation={selectedConversation}
              onConversationSelect={handleConversationSelect}
              isLoading={isLoadingConversations}
              onLoadMore={loadMoreConversations}
              hasMore={hasMoreConversations}
              isLoadingMore={isFetchingNextConversationPage}
              onStarConversation={handleStarConversation}
              onArchiveConversation={handleArchiveConversation}
              onDeleteConversation={handleDeleteConversation}
            />
          </div>

          {/* Message Content */}
          <div className="flex-1 bg-white">
            {isComposing ? (
              <MessengerComposer
                channelId={parseInt(channelId)}
                onMessageSent={handleMessageSent}
                onCancel={() => setIsComposing(false)}
              />
            ) : selectedConversation ? (
              <MessengerViewer
                conversation={selectedConversation}
                messages={messages}
                channelId={parseInt(channelId)}
                isLoading={isLoadingMessages}
                onLoadMore={loadMoreMessages}
                hasMore={hasMoreMessages}
                isLoadingMore={isFetchingNextMessagePage}
                onBackToList={isMobile ? handleBackToList : undefined}
                onMessageDeleted={() => {
                  refetchMessages();
                  refetchConversations();
                }}
              />
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center text-gray-500">
                  <div className="mb-4">
                    <i className="ri-messenger-line text-6xl text-gray-300"></i>
                  </div>
                  <h3 className="text-lg font-medium mb-2">
                    {t('messenger.select_conversation', 'Select a Conversation')}
                  </h3>
                  <p className="text-sm">
                    {t('messenger.choose_conversation', 'Choose a conversation from the list to start messaging')}
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
    </MessengerErrorBoundary>
  );
}
