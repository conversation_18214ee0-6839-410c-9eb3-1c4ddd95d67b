import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import { useTranslation } from '@/hooks/use-translation';

interface StarConversationParams {
  channelId: number;
  conversationId: number;
  starred: boolean;
}

interface ArchiveConversationParams {
  channelId: number;
  conversationId: number;
  archived: boolean;
}

export function useMessengerActions() {
  const { toast } = useToast();
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  const starConversation = useMutation({
    mutationFn: async ({ channelId, conversationId, starred }: StarConversationParams) => {
      const response = await fetch(`/api/messenger/${channelId}/conversations/${conversationId}/star`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ starred }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Failed to update conversation');
      }

      return response.json();
    },
    onSuccess: (data, variables) => {
      toast({
        title: variables.starred 
          ? t('messenger.conversation_starred', 'Conversation starred')
          : t('messenger.conversation_unstarred', 'Conversation unstarred'),
        description: variables.starred
          ? t('messenger.conversation_starred_desc', 'You can find this conversation in the Starred folder')
          : t('messenger.conversation_unstarred_desc', 'Conversation removed from Starred folder'),
      });


      queryClient.invalidateQueries({ queryKey: ['/api/messenger', variables.channelId, 'conversations'] });
      queryClient.invalidateQueries({ queryKey: ['/api/messenger/folder-counts', variables.channelId] });
    },
    onError: (error: Error, variables) => {
      toast({
        title: t('messenger.error', 'Error'),
        description: error.message || (variables.starred 
          ? t('messenger.star_error', 'Failed to star conversation')
          : t('messenger.unstar_error', 'Failed to unstar conversation')),
        variant: 'destructive',
      });
    },
  });

  const archiveConversation = useMutation({
    mutationFn: async ({ channelId, conversationId, archived }: ArchiveConversationParams) => {
      const response = await fetch(`/api/messenger/${channelId}/conversations/${conversationId}/archive`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ archived }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Failed to update conversation');
      }

      return response.json();
    },
    onSuccess: (data, variables) => {
      toast({
        title: variables.archived 
          ? t('messenger.conversation_archived', 'Conversation archived')
          : t('messenger.conversation_unarchived', 'Conversation unarchived'),
        description: variables.archived
          ? t('messenger.conversation_archived_desc', 'You can find this conversation in the Archived folder')
          : t('messenger.conversation_unarchived_desc', 'Conversation restored to inbox'),
      });


      queryClient.invalidateQueries({ queryKey: ['/api/messenger', variables.channelId, 'conversations'] });
      queryClient.invalidateQueries({ queryKey: ['/api/messenger/folder-counts', variables.channelId] });
    },
    onError: (error: Error, variables) => {
      toast({
        title: t('messenger.error', 'Error'),
        description: error.message || (variables.archived 
          ? t('messenger.archive_error', 'Failed to archive conversation')
          : t('messenger.unarchive_error', 'Failed to unarchive conversation')),
        variant: 'destructive',
      });
    },
  });

  return {
    starConversation: starConversation.mutate,
    archiveConversation: archiveConversation.mutate,
    isStarring: starConversation.isPending,
    isArchiving: archiveConversation.isPending,
  };
}
