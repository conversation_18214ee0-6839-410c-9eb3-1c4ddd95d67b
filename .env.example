DATABASE_URL=postgresql://postgres:postgres@localhost:5432/powerchat

# Logging Configuration
# LOG_LEVEL options: ERROR, WARN, INFO, DEBUG, VERBOSE
# Default: INFO in production, DEBUG in development
LOG_LEVEL=INFO

# Session Secret
SESSION_SECRET=your-secret-key-here

# Super Admin Credentials
SUPER_ADMIN_EMAIL=<EMAIL>
SUPER_ADMIN_PASSWORD=your-secure-password

# WhatsApp Configuration
WHATSAPP_SESSION_DIR=whatsapp-sessions

# Facebook/Meta Integration for WhatsApp Business API
# Get these from https://developers.facebook.com/
FACEBOOK_APP_ID=your_facebook_app_id_here
FACEBOOK_APP_SECRET=your_facebook_app_secret_here

# WhatsApp Business API Webhook Configuration
# Use a secure random string for webhook verification
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your_webhook_verify_token_here

# Frontend Environment Variables (for Vite)
# These must match the backend values
VITE_FACEBOOK_APP_ID=your_facebook_app_id_here
VITE_WHATSAPP_CONFIG_ID=your_whatsapp_config_id_here

# Twilio Integration for WhatsApp Business API (Alternative to Meta)
# Get these from https://console.twilio.com/
TWILIO_ACCOUNT_SID=your_twilio_account_sid_here
TWILIO_AUTH_TOKEN=your_twilio_auth_token_here
TWILIO_CONVERSATION_SERVICE_SID=your_conversation_service_sid_here
TWILIO_WHATSAPP_NUMBER=whatsapp:+***********

# 360Dialog Partner API Integration (SaaS Platform Configuration)
# Get these from https://partner.360dialog.io/
DIALOG_360_PARTNER_API_KEY=your_360dialog_partner_api_key_here
DIALOG_360_PARTNER_ID=your_360dialog_partner_id_here
DIALOG_360_PARTNER_WEBHOOK_URL=https://yourdomain.com/api/webhooks/360dialog-partner
DIALOG_360_REDIRECT_URL=https://yourdomain.com/settings/channels/360dialog/callback

# Instructions for WhatsApp Business API Setup:
# 1. Create a Facebook App at https://developers.facebook.com/
# 2. Add WhatsApp Business API product to your app
# 3. Configure webhook URL: https://yourdomain.com/api/webhooks/whatsapp
# 4. Set webhook verify token to match WHATSAPP_WEBHOOK_VERIFY_TOKEN
# 5. Subscribe to webhook fields: messages, message_deliveries, message_reads
# 6. Get your App ID and App Secret from the app dashboard
# 7. For embedded signup, create a WhatsApp Business configuration

# Instructions for Twilio WhatsApp Setup:
# 1. Create a Twilio account at https://console.twilio.com/
# 2. Create a Conversations Service in the Twilio Console
# 3. Enable WhatsApp for your Conversations Service
# 4. Configure webhook URL: https://yourdomain.com/api/webhooks/twilio-whatsapp
# 5. Get your Account SID, Auth Token, and Conversation Service SID
# 6. Use Twilio's WhatsApp Sandbox number or get approved for production

# Instructions for 360Dialog Partner API Setup:
# 1. Create a 360Dialog Partner account at https://partner.360dialog.io/
# 2. Register as a Meta Tech Provider (required for partner status)
# 3. Get your Partner API Key and Partner ID from the Partner Hub
# 4. Configure partner webhook URL: https://yourdomain.com/api/webhooks/360dialog-partner
# 5. Set redirect URL: https://yourdomain.com/settings/channels/360dialog/callback
# 6. Configure these credentials in Super Admin settings (not per-company)
# 7. Companies will use Integrated Onboarding flow to connect their WhatsApp accounts

# File Upload Configuration
UPLOAD_DIR=uploads
MAX_FILE_SIZE=********

# Server Configuration
PORT=5000
NODE_ENV=development
