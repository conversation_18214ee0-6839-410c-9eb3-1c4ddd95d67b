import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import LandingPage from '@/pages/landing';
import NotFound from '@/pages/not-found';
import { Loader2 } from 'lucide-react';

export default function ProtectedLandingPage() {

  const { data: settings, isLoading, error } = useQuery({
    queryKey: ['/api/public/website-enabled'],
    queryFn: async () => {
      try {
        const res = await fetch('/api/public/website-enabled');
        if (!res.ok) {

          return { enabled: false };
        }
        return res.json();
      } catch {

        return { enabled: false };
      }
    },
    retry: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
          <p className="text-white">Loading...</p>
        </div>
      </div>
    );
  }


  if (!settings?.enabled) {
    return <NotFound />;
  }


  return <LandingPage />;
}
