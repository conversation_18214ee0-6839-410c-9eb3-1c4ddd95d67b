/* MessengerViewer custom styles */

.highlight-message {
  animation: highlight 2s ease-in-out;
  border-radius: 1rem;
}

@keyframes highlight {
  0% { 
    background-color: rgba(59, 130, 246, 0.1);
    transform: scale(1);
  }
  50% { 
    background-color: rgba(59, 130, 246, 0.2);
    transform: scale(1.02);
  }
  100% { 
    background-color: transparent;
    transform: scale(1);
  }
}

/* Message bubble hover effects */
.message-bubble {
  transition: all 0.2s ease-in-out;
}

.message-bubble:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Smooth scrolling for message container */
.messages-container {
  scroll-behavior: smooth;
}

/* Context menu animations */
.context-menu {
  animation: contextMenuFadeIn 0.15s ease-out;
}

@keyframes contextMenuFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-5px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Attachment menu animations */
.attachment-menu {
  animation: attachmentMenuSlideUp 0.2s ease-out;
}

@keyframes attachmentMenuSlideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Reply preview animations */
.reply-preview {
  animation: replyPreviewSlideDown 0.3s ease-out;
}

@keyframes replyPreviewSlideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
    max-height: 0;
  }
  to {
    opacity: 1;
    transform: translateY(0);
    max-height: 200px;
  }
}

/* Message status icons */
.message-status {
  transition: all 0.2s ease-in-out;
}

.message-status.read {
  color: #3b82f6;
}

.message-status.delivered {
  color: #6b7280;
}

.message-status.sent {
  color: #9ca3af;
}

/* Typing indicator */
.typing-indicator {
  animation: typingPulse 1.5s ease-in-out infinite;
}

@keyframes typingPulse {
  0%, 60%, 100% {
    opacity: 0.4;
  }
  30% {
    opacity: 1;
  }
}

/* Media message hover effects */
.media-message {
  position: relative;
  overflow: hidden;
  border-radius: 0.75rem;
}

.media-message::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease-in-out;
}

.media-message:hover::before {
  transform: translateX(100%);
}

/* Reaction animations */
.reaction-button {
  transition: all 0.2s ease-in-out;
}

.reaction-button:hover {
  transform: scale(1.1);
}

.reaction-button.active {
  animation: reactionPop 0.3s ease-out;
}

@keyframes reactionPop {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.3);
  }
  100% {
    transform: scale(1.1);
  }
}

/* Message actions hover */
.message-actions {
  opacity: 0;
  transform: translateY(5px);
  transition: all 0.2s ease-in-out;
}

.message-group:hover .message-actions {
  opacity: 1;
  transform: translateY(0);
}

/* Smooth transitions for all interactive elements */
.interactive-element {
  transition: all 0.2s ease-in-out;
}

.interactive-element:hover {
  transform: translateY(-1px);
}

.interactive-element:active {
  transform: translateY(0);
}
