# PowerChatPlus Multi-Instance Configuration Template
# Copy this file to .env.{instance_name} and customize for each instance

# =============================================================================
# INSTANCE CONFIGURATION
# =============================================================================
INSTANCE_NAME=powerchat-default
COMPANY_NAME=PowerChat Default
COMPANY_SLUG=powerchat-default
PRIMARY_COLOR=#333235

# =============================================================================
# NETWORK & PORTS CONFIGURATION
# =============================================================================
# Application port (must be unique per instance)
APP_PORT=5000

# Database port (must be unique per instance)
DB_PORT=5432

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DB_NAME=powerchat_default
DB_USER=postgres
DB_PASSWORD=root
NODE_ENV=production

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# Generate a unique session secret for each instance
SESSION_SECRET=change_this_to_a_random_string_for_each_instance
FORCE_INSECURE_COOKIE=true

# =============================================================================
# ADMIN CONFIGURATION
# =============================================================================
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=change_this_password

# =============================================================================
# AI SERVICES API KEYS
# =============================================================================
XAI_API_KEY=
OPENAI_API_KEY=
ANTHROPIC_API_KEY=
GOOGLE_GEMINI_API_KEY=

# =============================================================================
# GOOGLE OAUTH CONFIGURATION
# =============================================================================
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GOOGLE_REDIRECT_URI=http://localhost:5000/auth/google/callback

# Base URL for OAuth redirects and callbacks
BASE_URL=http://localhost:9000

# =============================================================================
# PAYMENT CONFIGURATION
# =============================================================================
# Stripe
STRIPE_PUBLISHABLE_KEY=
STRIPE_SECRET_KEY=

# PayPal
PAYPAL_CLIENT_ID=
PAYPAL_CLIENT_SECRET=

# MercadoPago
MERCADOPAGO_ACCESS_TOKEN=

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
# SendGrid
SENDGRID_API_KEY=

# SMTP (alternative to SendGrid)
SMTP_HOST=
SMTP_PORT=465
SMTP_USER=
SMTP_PASS=

# =============================================================================
# WHATSAPP CONFIGURATION
# =============================================================================
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your_webhook_verify_token

# =============================================================================
# CLOUD STORAGE CONFIGURATION
# =============================================================================
STORAGE_PRIMARY_PROVIDER=local
GOOGLE_CLOUD_PROJECT_ID=
GOOGLE_CLOUD_STORAGE_BUCKET=

# =============================================================================
# FEATURE FLAGS
# =============================================================================
ENABLE_BACKUP_SCHEDULER=true
ENABLE_ANALYTICS=true
ENABLE_FLOW_BUILDER=true

# =============================================================================
# RESOURCE LIMITS (Optional)
# =============================================================================
# Memory limits for containers (uncomment to use)
# POSTGRES_MEMORY_LIMIT=512m
# APP_MEMORY_LIMIT=1g

# CPU limits for containers (uncomment to use)
# POSTGRES_CPU_LIMIT=0.5
# APP_CPU_LIMIT=1.0
