@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    font-family: 'Poppins', sans-serif;

    --brand-primary-color: #28282d;
    --brand-secondary-color: #333235;
    --brand-primary-rgb: 40, 40, 45;
    --brand-secondary-rgb: 51, 50, 53;
    --brand-primary-hover: #4b4b56;
    --brand-secondary-hover: #2a2932;

    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply font-poppins antialiased bg-background text-foreground;
  }

  html {
    font-family: 'Poppins', sans-serif;
  }
}

@keyframes pulse {
  0% {
    height: 5px;
    opacity: 0.6;
  }
  50% {
    height: 15px;
    opacity: 1;
  }
  100% {
    height: 5px;
    opacity: 0.6;
  }
}

@keyframes voiceWave {
  0%, 100% {
    transform: scaleY(0.5);
    opacity: 0.6;
  }
  50% {
    transform: scaleY(1);
    opacity: 1;
  }
}

.animate-voiceWave {
  animation: voiceWave 1.2s ease-in-out infinite;
}

/* Custom Scrollbar Styles */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Flow Builder Node Scrollbar */
.node-ai-assistant .custom-scrollbar::-webkit-scrollbar {
  width: 4px;
}

.node-ai-assistant .custom-scrollbar::-webkit-scrollbar-track {
  background: #f8fafc;
  border-radius: 2px;
}

.node-ai-assistant .custom-scrollbar::-webkit-scrollbar-thumb {
  background: #e2e8f0;
  border-radius: 2px;
}

.node-ai-assistant .custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #cbd5e1;
}

/* Emoji Picker Styles */
.emoji-picker-container {
  /* Override default emoji picker styles to match our design */
}

.emoji-picker-container .epr-main {
  border: none !important;
  border-radius: 0 !important;
  box-shadow: none !important;
}

.emoji-picker-container .epr-header {
  border-bottom: 1px solid #e5e7eb !important;
  padding: 8px 12px !important;
}

.emoji-picker-container .epr-search-container {
  padding: 8px 12px !important;
  border-bottom: 1px solid #e5e7eb !important;
}

.emoji-picker-container .epr-search {
  border: 1px solid #d1d5db !important;
  border-radius: 6px !important;
  padding: 6px 12px !important;
  font-size: 14px !important;
}

.emoji-picker-container .epr-search:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
  outline: none !important;
}

.emoji-picker-container .epr-category-nav {
  border-bottom: 1px solid #e5e7eb !important;
  padding: 4px 8px !important;
}

.emoji-picker-container .epr-cat-btn {
  border-radius: 6px !important;
  padding: 6px !important;
  margin: 2px !important;
}

.emoji-picker-container .epr-cat-btn:hover {
  background-color: #f3f4f6 !important;
}

.emoji-picker-container .epr-cat-btn.epr-active {
  background-color: #3b82f6 !important;
  color: white !important;
}

.emoji-picker-container .epr-body {
  padding: 8px !important;
}

.emoji-picker-container .epr-emoji-category-label {
  font-size: 12px !important;
  font-weight: 600 !important;
  color: #6b7280 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.05em !important;
  padding: 8px 4px 4px 4px !important;
}

.emoji-picker-container .epr-emoji-img {
  transition: transform 0.1s ease !important;
}

.emoji-picker-container .epr-emoji-img:hover {
  transform: scale(1.2) !important;
}

/* Mobile responsive adjustments for emoji picker */
@media (max-width: 768px) {
  /* Mobile-specific positioning for the entire picker container */
  .emoji-picker-container {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    top: auto !important;
    z-index: 9999 !important;
  }

  .emoji-picker-container .epr-main {
    width: 100vw !important;
    height: 60vh !important;
    max-height: 400px !important;
    position: relative !important;
    bottom: auto !important;
    left: auto !important;
    top: auto !important;
    border-radius: 16px 16px 0 0 !important;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15) !important;
  }

  .emoji-picker-container .epr-search {
    font-size: 16px !important; /* Prevent zoom on iOS */
  }
}

/* Desktop positioning - ensure it doesn't get overridden */
@media (min-width: 769px) {
  .emoji-picker-container {
    position: static !important;
  }

  .emoji-picker-container .epr-main {
    position: static !important;
    width: 320px !important;
    height: 400px !important;
  }
}

.btn-brand-primary {
  background-color: var(--brand-primary-color) !important;
  color: white !important;
}

.btn-brand-primary:hover {
  opacity: 0.9;
}

.btn-brand-secondary {
  background-color: var(--brand-secondary-color) !important;
  color: white !important;
}

.btn-brand-secondary:hover {
  opacity: 0.9;
}

button[role="checkbox"] {
  border: 2px solid hsl(var(--border)) ;
  background-color: hsl(var(--background)) ;
  min-width: 16px !important;
  min-height: 16px !important;
}

button[role="checkbox"]:hover {
  border-color: hsl(var(--primary)) ;
}

button[role="checkbox"][data-state="checked"] {
  background-color: hsl(var(--primary)) ;
  border-color: hsl(var(--primary));
}

button[role="checkbox"][data-state="checked"] svg {
  color: hsl(var(--primary-foreground)) ;
  display: block !important;
}

.checkbox-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.checkbox-container label {
  cursor: pointer;
  user-select: none;
}

[data-radix-dropdown-menu-content] {
  z-index: 9999 !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  pointer-events: auto !important;
}

[data-radix-popper-content-wrapper] {
  z-index: 9999 !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.agent-assignment-dropdown {
  position: relative;
}

.agent-assignment-dropdown [data-radix-dropdown-menu-content] {
  background: white !important;
  border: 1px solid #ccc !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  min-width: 200px !important;
  min-height: 50px !important;
  max-height: 300px !important;
  overflow-y: auto !important;
}

.agent-assignment-dropdown [data-radix-dropdown-menu-content]::before {
  content: "DROPDOWN CONTENT" !important;
  display: block !important;
  background: red !important;
  color: white !important;
  padding: 4px !important;
  font-size: 10px !important;
}

/* Mobile conversation interface styles */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Touch-friendly mobile interactions */
@media (max-width: 768px) {
  /* Ensure minimum touch targets */
  button, [role="button"], input[type="button"], input[type="submit"] {
    min-height: 23px;
    min-width: 23px;
  }

  /* Improve text readability on mobile */
  input, textarea, select {
    font-size: 16px; /* Prevent zoom on iOS */
  }

  /* iPhone 14 Pro Max specific fixes */
  @supports (-webkit-touch-callout: none) {
    /* iOS Safari specific styles */
    .h-screen {
      height: 100vh;
      height: -webkit-fill-available;
    }

    /* Ensure proper viewport height on iOS */
    html, body {
      height: 100%;
      height: -webkit-fill-available;
    }
  }

  /* Mobile conversation list animations */
  .conversation-list-mobile {
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
  }

  .conversation-list-mobile.open {
    transform: translateX(0);
  }

  /* Mobile contact details animations */
  .contact-details-mobile {
    transform: translateX(100%);
    transition: transform 0.3s ease-in-out;
  }

  .contact-details-mobile.open {
    transform: translateX(0);
  }

  /* Desktop contact details panel transitions */
  .contact-details-panel {
    transition: all 0.3s ease-in-out;
  }

  .contact-details-panel.hidden {
    opacity: 0;
    transform: translateX(20px);
    pointer-events: none;
  }

  .contact-details-panel.visible {
    opacity: 1;
    transform: translateX(0);
    pointer-events: auto;
  }

  /* More options button active state */
  .more-options-button {
    transition: all 0.2s ease-in-out;
  }

  .more-options-button.active {
    background-color: rgb(239 246 255);
    color: rgb(37 99 235);
  }

  .more-options-button.active i {
    transform: rotate(90deg);
  }

  /* Desktop contact details panel transitions */
  .contact-details-panel {
    transition: all 0.3s ease-in-out;
  }

  .contact-details-panel.hidden {
    opacity: 0;
    transform: translateX(20px);
    pointer-events: none;
  }

  .contact-details-panel.visible {
    opacity: 1;
    transform: translateX(0);
    pointer-events: auto;
  }

  /* Improve mobile scrolling */
  .mobile-scroll {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }

  /* Mobile-specific spacing adjustments */
  .mobile-padding {
    padding-left: 16px;
    padding-right: 16px;
  }

  .mobile-margin {
    margin-left: 16px;
    margin-right: 16px;
  }

  /* Ensure mobile panels are properly layered and interactive */
  .mobile-panel {
    position: fixed;
    top: 0;
    bottom: 0;
    background: white;
    z-index: 50;
    touch-action: auto;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }

  /* Prevent touch events from being blocked */
  .mobile-panel * {
    pointer-events: auto;
    touch-action: auto;
  }

  /* Mobile overlay should be behind panels */
  .mobile-overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 30;
    touch-action: none;
  }
}

/* Tablet-specific adjustments */
@media (min-width: 768px) and (max-width: 1024px) {
  .tablet-layout {
    display: flex;
    flex-direction: row;
  }

  .tablet-conversation-list {
    width: 320px;
    flex-shrink: 0;
  }

  .tablet-main-content {
    flex: 1;
    min-width: 0;
  }
}

@layer utilities {
  .bg-brand-primary {
    background-color: var(--brand-primary-color) !important;
  }

  .bg-brand-secondary {
    background-color: var(--brand-secondary-color) !important;
  }

  .text-brand-primary {
    color: var(--brand-primary-color) !important;
  }

  .text-brand-secondary {
    color: var(--brand-secondary-color) !important;
  }

  .border-brand-primary {
    border-color: var(--brand-primary-color) !important;
  }

  .border-brand-secondary {
    border-color: var(--brand-secondary-color) !important;
  }

  .hover\:bg-brand-primary:hover {
    background-color: var(--brand-primary-hover) !important;
  }

  .hover\:bg-brand-secondary:hover {
    background-color: var(--brand-secondary-hover) !important;
  }

  .switch-brand[data-state="checked"] {
    background-color: #333 !important;
  }

  .toggle-brand[data-state="on"] {
    background-color: #333 !important;
  }

  .btn-brand-outline {
    border: 2px solid var(--brand-primary-color) !important;
    color: var(--brand-primary-color) !important;
    background-color: transparent !important;
  }

  .btn-brand-outline:hover {
    background-color: var(--brand-primary-color) !important;
    color: white !important;
  }

  .btn-brand-ghost {
    color: var(--brand-primary-color) !important;
    background-color: transparent !important;
  }

  .btn-brand-ghost:hover {
    background-color: rgba(var(--brand-primary-rgb), 0.1) !important;
  }

  /* Embedded context styles */
  .embedded-context {
    overflow: hidden;
  }

  .embedded-context .main-layout {
    height: 100vh;
    overflow: hidden;
  }

  .embedded-context .sidebar {
    position: relative;
  }

  .embedded-context .content-area {
    height: calc(100vh - 60px);
    overflow-y: auto;
  }

  /* Hide certain elements in embedded context */
  .embedded-context .external-links,
  .embedded-context .new-window-links {
    display: none;
  }

  /* Adjust header for embedded context */
  .embedded-context .app-header {
    padding: 8px 16px;
    min-height: 50px;
  }

  /* Responsive iframe adjustments */
  @media (max-width: 768px) {
    .embedded-context .sidebar {
      transform: translateX(-100%);
      transition: transform 0.3s ease;
    }

    .embedded-context .sidebar.open {
      transform: translateX(0);
    }
  }
}