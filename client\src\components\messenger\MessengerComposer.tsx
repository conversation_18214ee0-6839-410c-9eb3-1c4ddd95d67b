import { useState, useRef } from 'react';
import { useTranslation } from '@/hooks/use-translation';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { 
  Send, 
  Paperclip, 
  X, 
  Plus, 
  Minus,
  Image as ImageIcon,
  Video,
  File,
  Smile,
  ArrowLeft
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';

interface MessengerComposerProps {
  channelId: number;
  onMessageSent: () => void;
  onCancel: () => void;
  recipientId?: string;
  className?: string;
}

interface QuickReply {
  title: string;
  payload: string;
}

interface ButtonTemplate {
  text: string;
  buttons: Array<{
    type: 'postback' | 'web_url';
    title: string;
    payload?: string;
    url?: string;
  }>;
}

export default function MessengerComposer({
  channelId,
  onMessageSent,
  onCancel,
  recipientId,
  className
}: MessengerComposerProps) {
  const { t } = useTranslation();
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);


  const [recipient, setRecipient] = useState(recipientId || '');
  const [messageText, setMessageText] = useState('');
  const [messageType, setMessageType] = useState<'text' | 'quick_reply' | 'template'>('text');
  const [quickReplies, setQuickReplies] = useState<QuickReply[]>([]);
  const [buttonTemplate, setButtonTemplate] = useState<ButtonTemplate>({ text: '', buttons: [] });
  const [attachments, setAttachments] = useState<File[]>([]);
  const [isSending, setIsSending] = useState(false);

  const handleAddQuickReply = () => {
    if (quickReplies.length < 13) { // Facebook limit
      setQuickReplies([...quickReplies, { title: '', payload: '' }]);
    }
  };

  const handleRemoveQuickReply = (index: number) => {
    setQuickReplies(quickReplies.filter((_, i) => i !== index));
  };

  const handleQuickReplyChange = (index: number, field: 'title' | 'payload', value: string) => {
    const updated = quickReplies.map((qr, i) => 
      i === index ? { ...qr, [field]: value } : qr
    );
    setQuickReplies(updated);
  };

  const handleAddButton = () => {
    if (buttonTemplate.buttons.length < 3) { // Facebook limit
      setButtonTemplate({
        ...buttonTemplate,
        buttons: [...buttonTemplate.buttons, { type: 'postback', title: '', payload: '' }]
      });
    }
  };

  const handleRemoveButton = (index: number) => {
    setButtonTemplate({
      ...buttonTemplate,
      buttons: buttonTemplate.buttons.filter((_, i) => i !== index)
    });
  };

  const handleButtonChange = (index: number, field: string, value: string) => {
    const updated = buttonTemplate.buttons.map((btn, i) => 
      i === index ? { ...btn, [field]: value } : btn
    );
    setButtonTemplate({ ...buttonTemplate, buttons: updated });
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setAttachments([...attachments, ...files]);
  };

  const handleRemoveAttachment = (index: number) => {
    setAttachments(attachments.filter((_, i) => i !== index));
  };

  const handleSend = async () => {
    if (!recipient.trim()) {
      toast({
        title: t('messenger.error', 'Error'),
        description: t('messenger.recipient_required', 'Recipient is required'),
        variant: 'destructive',
      });
      return;
    }

    if (!messageText.trim() && attachments.length === 0) {
      toast({
        title: t('messenger.error', 'Error'),
        description: t('messenger.message_required', 'Message content is required'),
        variant: 'destructive',
      });
      return;
    }

    setIsSending(true);

    try {
      const formData = new FormData();
      formData.append('to', recipient);
      formData.append('message', messageText);
      formData.append('type', messageType);


      if (messageType === 'quick_reply' && quickReplies.length > 0) {
        formData.append('quickReplies', JSON.stringify(quickReplies.filter(qr => qr.title && qr.payload)));
      }


      if (messageType === 'template' && buttonTemplate.text && buttonTemplate.buttons.length > 0) {
        formData.append('template', JSON.stringify(buttonTemplate));
      }


      attachments.forEach((file, index) => {
        formData.append(`attachment_${index}`, file);
      });

      const response = await fetch(`/api/messenger/${channelId}/send`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to send message');
      }


      setRecipient('');
      setMessageText('');
      setMessageType('text');
      setQuickReplies([]);
      setButtonTemplate({ text: '', buttons: [] });
      setAttachments([]);

      onMessageSent();

    } catch (error: any) {
      console.error('Error sending message:', error);
      toast({
        title: t('messenger.send_error', 'Send Error'),
        description: error.message || t('messenger.send_failed', 'Failed to send message'),
        variant: 'destructive',
      });
    } finally {
      setIsSending(false);
    }
  };

  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/')) return <ImageIcon className="h-4 w-4" />;
    if (file.type.startsWith('video/')) return <Video className="h-4 w-4" />;
    return <File className="h-4 w-4" />;
  };

  return (
    <div className={cn("flex flex-col h-full bg-white", className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center gap-3">
          <Button variant="ghost" size="sm" onClick={onCancel}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h2 className="text-lg font-semibold text-gray-900">
            {t('messenger.compose', 'Compose Message')}
          </h2>
        </div>
        
        <div className="flex items-center gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                {messageType === 'text' && t('messenger.text_message', 'Text Message')}
                {messageType === 'quick_reply' && t('messenger.quick_reply', 'Quick Reply')}
                {messageType === 'template' && t('messenger.button_template', 'Button Template')}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setMessageType('text')}>
                {t('messenger.text_message', 'Text Message')}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setMessageType('quick_reply')}>
                {t('messenger.quick_reply', 'Quick Reply')}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setMessageType('template')}>
                {t('messenger.button_template', 'Button Template')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {/* Recipient */}
        <div className="space-y-2">
          <Label htmlFor="recipient">{t('messenger.recipient', 'Recipient')}</Label>
          <Input
            id="recipient"
            value={recipient}
            onChange={(e) => setRecipient(e.target.value)}
            placeholder={t('messenger.enter_recipient', 'Enter recipient ID or phone number')}
            disabled={!!recipientId}
          />
        </div>

        {/* Message Content */}
        <div className="space-y-2">
          <Label htmlFor="message">{t('messenger.message', 'Message')}</Label>
          <Textarea
            id="message"
            value={messageText}
            onChange={(e) => setMessageText(e.target.value)}
            placeholder={t('messenger.enter_message', 'Enter your message...')}
            rows={4}
            className="resize-none"
          />
        </div>

        {/* Quick Replies */}
        {messageType === 'quick_reply' && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label>{t('messenger.quick_replies', 'Quick Replies')}</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleAddQuickReply}
                disabled={quickReplies.length >= 13}
              >
                <Plus className="h-4 w-4 mr-1" />
                {t('messenger.add_quick_reply', 'Add Quick Reply')}
              </Button>
            </div>
            
            {quickReplies.map((qr, index) => (
              <div key={index} className="flex gap-2 p-3 border border-gray-200 rounded-lg">
                <div className="flex-1 space-y-2">
                  <Input
                    placeholder={t('messenger.quick_reply_title', 'Quick reply title')}
                    value={qr.title}
                    onChange={(e) => handleQuickReplyChange(index, 'title', e.target.value)}
                    maxLength={20}
                  />
                  <Input
                    placeholder={t('messenger.quick_reply_payload', 'Payload')}
                    value={qr.payload}
                    onChange={(e) => handleQuickReplyChange(index, 'payload', e.target.value)}
                    maxLength={1000}
                  />
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => handleRemoveQuickReply(index)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        )}

        {/* Button Template */}
        {messageType === 'template' && (
          <div className="space-y-3">
            <div className="space-y-2">
              <Label>{t('messenger.template_text', 'Template Text')}</Label>
              <Textarea
                value={buttonTemplate.text}
                onChange={(e) => setButtonTemplate({ ...buttonTemplate, text: e.target.value })}
                placeholder={t('messenger.enter_template_text', 'Enter template text...')}
                rows={3}
                maxLength={640}
              />
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label>{t('messenger.buttons', 'Buttons')}</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleAddButton}
                  disabled={buttonTemplate.buttons.length >= 3}
                >
                  <Plus className="h-4 w-4 mr-1" />
                  {t('messenger.add_button', 'Add Button')}
                </Button>
              </div>

              {buttonTemplate.buttons.map((button, index) => (
                <div key={index} className="flex gap-2 p-3 border border-gray-200 rounded-lg">
                  <div className="flex-1 space-y-2">
                    <select
                      value={button.type}
                      onChange={(e) => handleButtonChange(index, 'type', e.target.value)}
                      className="w-full p-2 border border-gray-300 rounded-md"
                    >
                      <option value="postback">{t('messenger.postback', 'Postback')}</option>
                      <option value="web_url">{t('messenger.web_url', 'Web URL')}</option>
                    </select>
                    <Input
                      placeholder={t('messenger.button_title', 'Button title')}
                      value={button.title}
                      onChange={(e) => handleButtonChange(index, 'title', e.target.value)}
                      maxLength={20}
                    />
                    {button.type === 'postback' ? (
                      <Input
                        placeholder={t('messenger.payload', 'Payload')}
                        value={button.payload || ''}
                        onChange={(e) => handleButtonChange(index, 'payload', e.target.value)}
                        maxLength={1000}
                      />
                    ) : (
                      <Input
                        placeholder={t('messenger.url', 'URL')}
                        value={button.url || ''}
                        onChange={(e) => handleButtonChange(index, 'url', e.target.value)}
                        type="url"
                      />
                    )}
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveButton(index)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Attachments */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label>{t('messenger.attachments', 'Attachments')}</Label>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => fileInputRef.current?.click()}
            >
              <Paperclip className="h-4 w-4 mr-1" />
              {t('messenger.attach_file', 'Attach File')}
            </Button>
          </div>

          {attachments.length > 0 && (
            <div className="space-y-2">
              {attachments.map((file, index) => (
                <div key={index} className="flex items-center gap-2 p-2 bg-gray-50 rounded-lg">
                  {getFileIcon(file)}
                  <span className="flex-1 text-sm truncate">{file.name}</span>
                  <Badge variant="secondary" className="text-xs">
                    {(file.size / 1024 / 1024).toFixed(1)} MB
                  </Badge>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveAttachment(index)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          )}

          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept="image/*,video/*,audio/*,.pdf,.doc,.docx"
            onChange={handleFileSelect}
            className="hidden"
          />
        </div>
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex items-center justify-end gap-2">
          <Button variant="outline" onClick={onCancel} disabled={isSending}>
            {t('common.cancel', 'Cancel')}
          </Button>
          <Button onClick={handleSend} disabled={isSending}>
            {isSending ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                {t('messenger.sending', 'Sending...')}
              </>
            ) : (
              <>
                <Send className="h-4 w-4 mr-2" />
                {t('messenger.send', 'Send Message')}
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}
