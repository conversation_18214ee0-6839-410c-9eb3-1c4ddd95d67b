import { useState } from 'react';
import { useTranslation } from '@/hooks/use-translation';
import { useQuery } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  MessageCircle,
  Star,
  Archive,
  Settings,
  RefreshCw,
  Wifi,
  WifiOff
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface MessengerFolder {
  id: string;
  name: string;
  count: number;
  icon: React.ReactNode;
  type: 'all' | 'unread' | 'starred' | 'archived' | 'custom';
  disabled?: boolean;
}

interface MessengerSidebarProps {
  selectedFolder: string;
  onFolderSelect: (folderId: string) => void;
  channelId: number;
  className?: string;
}

interface ConnectionStatus {
  isConnected: boolean;
  lastConnected?: string;
  status: 'active' | 'inactive' | 'error' | 'connecting';
}

export default function MessengerSidebar({
  selectedFolder,
  onFolderSelect,
  channelId,
  className
}: MessengerSidebarProps) {
  const { t } = useTranslation();
  const [isRefreshing, setIsRefreshing] = useState(false);


  const { data: connectionStatus, refetch: refetchStatus } = useQuery<ConnectionStatus>({
    queryKey: ['/api/messenger/status', channelId],
    queryFn: async () => {
      const response = await fetch(`/api/messenger/${channelId}/status`);
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Failed to fetch connection status');
      }
      const data = await response.json();


      if (data.success && data.data) {
        return data.data;
      }


      return data;
    },
    refetchInterval: 10000, // Check status every 10 seconds
    enabled: !!channelId,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });


  const { data: folderCounts = {}, refetch: refetchCounts, error: countsError } = useQuery({
    queryKey: ['/api/messenger/folder-counts', channelId],
    queryFn: async () => {
      const response = await fetch(`/api/messenger/${channelId}/folder-counts`);
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Failed to fetch folder counts');
      }
      const data = await response.json();


      if (data.success && data.data) {
        return data.data;
      }


      return data;
    },
    refetchInterval: 30000, // Refresh counts every 30 seconds
    enabled: !!channelId,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  const folders: MessengerFolder[] = [
    {
      id: 'all',
      name: t('messenger.folders.all', 'All Conversations'),
      count: folderCounts.all || 0,
      icon: <MessageCircle className="h-4 w-4" />,
      type: 'all'
    },
    {
      id: 'unread',
      name: t('messenger.folders.unread', 'Unread'),
      count: folderCounts.unread || 0,
      icon: <MessageCircle className="h-4 w-4" />,
      type: 'unread'
    },
    {
      id: 'starred',
      name: t('messenger.folders.starred', 'Starred'),
      count: folderCounts.starred || 0,
      icon: <Star className="h-4 w-4" />,
      type: 'starred',
      disabled: false
    },
    {
      id: 'archived',
      name: t('messenger.folders.archived', 'Archived'),
      count: folderCounts.archived || 0,
      icon: <Archive className="h-4 w-4" />,
      type: 'archived',
      disabled: false
    }
  ];

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await Promise.all([refetchStatus(), refetchCounts()]);
    } finally {
      setIsRefreshing(false);
    }
  };

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'active':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'connecting':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'error':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'active':
        return <Wifi className="h-3 w-3" />;
      case 'connecting':
        return <RefreshCw className="h-3 w-3 animate-spin" />;
      default:
        return <WifiOff className="h-3 w-3" />;
    }
  };

  return (
    <div className={cn("flex flex-col h-full bg-gray-50", className)}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
              <i className="ri-messenger-line text-xl text-white"></i>
            </div>
            {t('messenger.title', 'Messenger')}
          </h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            <RefreshCw className={cn("h-4 w-4", isRefreshing && "animate-spin")} />
          </Button>
        </div>
      </div>

      {/* Folders */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-2">
          <div className="space-y-1">
            {folders.map((folder) => (
              <Button
                key={folder.id}
                variant={selectedFolder === folder.id ? "secondary" : "ghost"}
                className={cn(
                  "w-full justify-start h-10 px-3",
                  selectedFolder === folder.id && "bg-blue-50 text-blue-700 border-blue-200",
                  folder.disabled && "opacity-50 cursor-not-allowed"
                )}
                onClick={() => folder.disabled ? undefined : onFolderSelect(folder.id)}
                disabled={folder.disabled}
              >
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center gap-3">
                    {folder.icon}
                    <span className="text-sm font-medium">{folder.name}</span>
                  </div>
                  {folder.count > 0 && (
                    <Badge 
                      variant="secondary" 
                      className={cn(
                        "text-xs h-5 px-2",
                        selectedFolder === folder.id 
                          ? "bg-blue-100 text-blue-700" 
                          : "bg-gray-100 text-gray-600"
                      )}
                    >
                      {folder.count}
                    </Badge>
                  )}
                </div>
              </Button>
            ))}
          </div>
        </div>

        <Separator className="my-4" />
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200">
        <div className="text-xs text-gray-500 text-center">
          {connectionStatus?.lastConnected && (
            <div>
              {t('messenger.last_sync', 'Last sync')}: {' '}
              {new Date(connectionStatus.lastConnected).toLocaleTimeString()}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
