[{"key": "admin.languages.cannot_delete_default_desc", "value": "Por favor, establezca primero otro idioma como predeterminado."}, {"key": "admin.languages.cannot_delete_default_title", "value": "No se puede eliminar el idioma predeterminado"}, {"key": "admin.languages.confirm_delete", "value": "¿Está seguro de que desea eliminar {{name}}?"}, {"key": "admin.languages.default_updated_desc", "value": "El idioma predeterminado se ha actualizado correctamente."}, {"key": "admin.languages.default_updated_title", "value": "Idioma predeterminado actualizado"}, {"key": "admin.languages.deleted_desc", "value": "El idioma se ha eliminado correctamente."}, {"key": "admin.languages.deleted_title", "value": "Idioma eliminado"}, {"key": "admin.settings.updates", "value": "Actualizaciones del sistema"}, {"key": "settings.updates.title", "value": "Actualizaciones del sistema"}, {"key": "settings.updates.current_version", "value": "Versión actual"}, {"key": "settings.updates.checking", "value": "Comprobando..."}, {"key": "settings.updates.check_updates", "value": "Buscar actualizaciones"}, {"key": "settings.updates.update_available", "value": "Actualización disponible: v{{version}}"}, {"key": "settings.updates.package_size", "value": "<PERSON><PERSON><PERSON> del paquete: {{size}}"}, {"key": "settings.updates.release_notes", "value": "Notas de la versión:"}, {"key": "settings.updates.starting", "value": "Iniciando..."}, {"key": "settings.updates.install_update", "value": "Instalar actualización"}, {"key": "settings.updates.up_to_date", "value": "Su sistema está actualizado"}, {"key": "settings.updates.update_progress", "value": "Progreso de la actualización"}, {"key": "settings.updates.preparing", "value": "Preparando actualización..."}, {"key": "settings.updates.update_warning", "value": "No cierre esta página ni reinicie el sistema durante el proceso de actualización."}, {"key": "settings.updates.history", "value": "Historial de actualizaciones"}, {"key": "settings.updates.no_history", "value": "No hay historial de actualizaciones disponible"}, {"key": "settings.updates.update_started", "value": "Actualización iniciada"}, {"key": "settings.updates.update_started_desc", "value": "La actualización del sistema se ha iniciado"}, {"key": "settings.updates.update_failed", "value": "Error al iniciar la actualización"}, {"key": "settings.updates.status.pending", "value": "Pendiente"}, {"key": "settings.updates.status.downloading", "value": "Descargando"}, {"key": "settings.updates.status.validating", "value": "Validando"}, {"key": "settings.updates.status.applying", "value": "Aplicando"}, {"key": "settings.updates.status.completed", "value": "Completado"}, {"key": "settings.updates.status.failed", "value": "Fallido"}, {"key": "settings.updates.status.rolled_back", "value": "Revertido"}, {"key": "trial.status_title", "value": "<PERSON><PERSON><PERSON>"}, {"key": "trial.days_remaining", "value": "{{days}} días restantes"}, {"key": "trial.expires_today", "value": "<PERSON><PERSON><PERSON> hoy"}, {"key": "trial.expired", "value": "Prueba expirada"}, {"key": "trial.upgrade_now", "value": "<PERSON><PERSON><PERSON><PERSON>ora"}, {"key": "plans.trial_days", "value": "Días de prueba"}, {"key": "plans.trial_period", "value": "<PERSON><PERSON><PERSON>"}, {"key": "plans.has_trial", "value": "Tiene prueba"}, {"key": "plans.no_trial", "value": "Sin prueba"}, {"key": "plans.free_plan", "value": "Plan Gratuito"}, {"key": "plans.free", "value": "<PERSON><PERSON><PERSON>"}, {"key": "plans.paid", "value": "Pago"}, {"key": "registration.trial_available", "value": "Prueba disponible de {{days}} días"}, {"key": "registration.free_plan_available", "value": "Plan gratuito"}, {"key": "admin.languages.description", "value": "Administre los idiomas disponibles en su aplicación. El idioma predeterminado se utilizará cuando el idioma preferido de un usuario no esté disponible."}, {"key": "admin.languages.table.actions", "value": "Acciones"}, {"key": "admin.languages.table.active", "value": "Activo"}, {"key": "admin.languages.table.code", "value": "Código"}, {"key": "admin.languages.table.default", "value": "Predeterminado"}, {"key": "admin.languages.table.direction", "value": "Dirección"}, {"key": "admin.languages.table.language", "value": "Idioma"}, {"key": "admin.languages.title", "value": "Idiomas Disponibles"}, {"key": "admin.languages.updated_desc", "value": "El idioma se ha actualizado correctamente."}, {"key": "admin.languages.updated_title", "value": "Idioma actualizado"}, {"key": "admin.namespaces.confirm_delete", "value": "¿Está seguro de que desea eliminar el espacio de nombres \"{{name}}\"? Esto eliminará todas las claves y traducciones de este espacio de nombres."}, {"key": "admin.namespaces.deleted_desc", "value": "El espacio de nombres se ha eliminado correctamente."}, {"key": "admin.namespaces.deleted_title", "value": "Espacio de nombres eliminado"}, {"key": "admin.namespaces.description", "value": "Los espacios de nombres ayudan a organizar las traducciones en grupos lógicos."}, {"key": "admin.namespaces.edit_description", "value": "Actualice la configuración del espacio de nombres."}, {"key": "admin.namespaces.edit_title", "value": "Editar Espacio <PERSON> Nombres"}, {"key": "admin.namespaces.form.description", "value": "Descripción"}, {"key": "admin.namespaces.form.description_placeholder", "value": "Traducciones comunes utilizadas en toda la aplicación"}, {"key": "admin.namespaces.form.name", "value": "Nombre"}, {"key": "admin.namespaces.form.name_placeholder", "value": "común"}, {"key": "admin.namespaces.table.actions", "value": "Acciones"}, {"key": "admin.namespaces.table.description", "value": "Descripción"}, {"key": "admin.namespaces.table.name", "value": "Nombre"}, {"key": "admin.namespaces.title", "value": "Espacios de Nombres de Traducción"}, {"key": "admin.namespaces.update_button", "value": "Actualizar Espacio <PERSON> Nombres"}, {"key": "admin.namespaces.updated_desc", "value": "El espacio de nombres se ha actualizado correctamente."}, {"key": "admin.namespaces.updated_title", "value": "Espacio de nombres actualizado"}, {"key": "admin.translations.description", "value": "Seleccione un idioma y un espacio de nombres para administrar las traducciones."}, {"key": "admin.translations.language_label", "value": "Idioma"}, {"key": "admin.translations.namespace_label", "value": "Espacio <PERSON> Nombres"}, {"key": "admin.translations.no_keys", "value": "No se encontraron claves de traducción en este espacio de nombres. Agregue una nueva clave para empezar."}, {"key": "admin.translations.no_translation", "value": "Sin traducción"}, {"key": "admin.translations.saved_desc", "value": "La traducción se ha guardado correctamente."}, {"key": "admin.translations.saved_title", "value": "Traducción guardada"}, {"key": "admin.translations.select_both", "value": "Por favor, seleccione tanto un idioma como un espacio de nombres para administrar las traducciones."}, {"key": "admin.translations.select_language", "value": "Seleccionar idioma"}, {"key": "admin.translations.select_namespace", "value": "Seleccionar espacio de nombres"}, {"key": "admin.translations.table.actions", "value": "Acciones"}, {"key": "admin.translations.table.key", "value": "Clave"}, {"key": "admin.translations.table.translation", "value": "Traducción"}, {"key": "admin.translations.title", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "common.edit", "value": "<PERSON><PERSON>"}, {"key": "common.error", "value": "Error"}, {"key": "common.updating", "value": "Actualizando..."}, {"key": "common.search", "value": "Buscar"}, {"key": "common.searching", "value": "Buscando..."}, {"key": "common.search_placeholder", "value": "Buscar conversaciones, contactos, plantillas..."}, {"key": "search.no_results", "value": "No se encontraron resultados para \"{{query}}\""}, {"key": "search.conversations", "value": "Conversaciones"}, {"key": "search.contacts", "value": "Contactos"}, {"key": "search.templates", "value": "Plantillas"}, {"key": "emoji.picker_title", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"key": "emoji.recent", "value": "Usados recientemente"}, {"key": "emoji.search_placeholder", "value": "Buscar emojis..."}, {"key": "messages.input.add_emoji", "value": "Agregar emoji"}, {"key": "quoted_message.unknown_sender", "value": "Desconocido"}, {"key": "quoted_message.you", "value": "Tú"}, {"key": "quoted_message.assistant", "value": "<PERSON><PERSON><PERSON>"}, {"key": "quoted_message.contact", "value": "Contacto"}, {"key": "quoted_message.image", "value": "📷 Imagen"}, {"key": "quoted_message.video", "value": "🎥 Video"}, {"key": "quoted_message.audio", "value": "🎵 Audio"}, {"key": "quoted_message.document", "value": "📄 Documento"}, {"key": "quoted_message.message", "value": "Men<PERSON><PERSON>"}, {"key": "quoted_message.deleted_sender", "value": "Desconocido"}, {"key": "quoted_message.deleted_message", "value": "Este mensaje fue eliminado"}, {"key": "message_bubble.confirm_delete_whatsapp_title", "value": "Eliminar mensaje para todos"}, {"key": "message_bubble.confirm_delete_whatsapp_message", "value": "Este mensaje se eliminará de {{appName}} y del chat de WhatsApp del destinatario. Esta acción no se puede deshacer."}, {"key": "message_bubble.confirm_delete_whatsapp_old", "value": "Este mensaje es demasiado antiguo para ser eliminado de WhatsApp (límite de 72 minutos). Solo se eliminará de {{appName}}."}, {"key": "welcome.message", "value": "bienvenido a pointer"}, {"key": "admin.payments.title", "value": "Gestión de Pagos"}, {"key": "admin.payments.description", "value": "Panel integral de seguimiento y gestión de pagos"}, {"key": "admin.payments.dashboard.title", "value": "Panel de Pagos"}, {"key": "admin.payments.metrics.total_revenue", "value": "Ingresos Totales"}, {"key": "admin.payments.metrics.monthly_revenue", "value": "Ingresos Mensuales"}, {"key": "admin.payments.metrics.yearly_revenue", "value": "Ingresos Anuales"}, {"key": "admin.payments.metrics.monthly_growth", "value": "Crecimiento Mensual"}, {"key": "admin.payments.metrics.active_subscriptions", "value": "Suscripciones Activas"}, {"key": "admin.payments.metrics.pending_payments", "value": "Pagos Pen<PERSON>"}, {"key": "admin.payments.metrics.payment_success_rate", "value": "Tasa de Éxito de Pago"}, {"key": "admin.payments.trends.title", "value": "Tendencias de Pago"}, {"key": "admin.payments.trends.revenue", "value": "Ingresos"}, {"key": "admin.payments.trends.transactions", "value": "Transacciones"}, {"key": "admin.payments.trends.period.7days", "value": "Últimos 7 Días"}, {"key": "admin.payments.trends.period.30days", "value": "Últimos 30 Días"}, {"key": "admin.payments.trends.period.12months", "value": "Últimos 12 Meses"}, {"key": "admin.payments.companies.title", "value": "Detalles de Pago de Empresas"}, {"key": "admin.payments.companies.search_placeholder", "value": "Buscar empresas..."}, {"key": "admin.payments.companies.table.company", "value": "Empresa"}, {"key": "admin.payments.companies.table.plan", "value": "Plan"}, {"key": "admin.payments.companies.table.status", "value": "Estado"}, {"key": "admin.payments.companies.table.last_payment", "value": "Último <PERSON>"}, {"key": "admin.payments.companies.table.next_renewal", "value": "Próxima Renovación"}, {"key": "admin.payments.companies.table.payment_method", "value": "Mé<PERSON><PERSON>"}, {"key": "admin.payments.companies.table.total_paid", "value": "Total Pagado"}, {"key": "admin.payments.companies.table.actions", "value": "Acciones"}, {"key": "admin.payments.companies.status.active", "value": "Activo"}, {"key": "admin.payments.companies.status.pending", "value": "Pendiente"}, {"key": "admin.payments.companies.status.overdue", "value": "V<PERSON>cid<PERSON>"}, {"key": "admin.payments.companies.status.cancelled", "value": "Cancelado"}, {"key": "admin.payments.transactions.title", "value": "Transacciones de Pago"}, {"key": "admin.payments.transactions.filter.all_methods", "value": "Todos los Métodos de Pago"}, {"key": "admin.payments.transactions.filter.all_statuses", "value": "Todos los Estados"}, {"key": "admin.payments.transactions.filter.date_range", "value": "<PERSON><PERSON>"}, {"key": "admin.payments.transactions.table.id", "value": "ID de Transacción"}, {"key": "admin.payments.transactions.table.company", "value": "Empresa"}, {"key": "admin.payments.transactions.table.plan", "value": "Plan"}, {"key": "admin.payments.transactions.table.amount", "value": "Monto"}, {"key": "admin.payments.transactions.table.method", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "admin.payments.transactions.table.status", "value": "Estado"}, {"key": "admin.payments.transactions.table.date", "value": "<PERSON><PERSON>"}, {"key": "admin.payments.transactions.table.actions", "value": "Acciones"}, {"key": "admin.payments.transactions.status.completed", "value": "Completado"}, {"key": "admin.payments.transactions.status.pending", "value": "Pendiente"}, {"key": "admin.payments.transactions.status.failed", "value": "Fallido"}, {"key": "admin.payments.transactions.status.cancelled", "value": "Cancelado"}, {"key": "admin.payments.pending.title", "value": "Pagos Pen<PERSON>"}, {"key": "admin.payments.pending.description", "value": "Administre pagos vencidos y pendientes"}, {"key": "admin.payments.pending.table.company", "value": "Empresa"}, {"key": "admin.payments.pending.table.plan", "value": "Plan"}, {"key": "admin.payments.pending.table.amount", "value": "Monto"}, {"key": "admin.payments.pending.table.days_overdue", "value": "<PERSON>ías Vencidos"}, {"key": "admin.payments.pending.table.method", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "admin.payments.pending.table.actions", "value": "Acciones"}, {"key": "admin.payments.actions.mark_received", "value": "Marcar como Recibido"}, {"key": "admin.payments.actions.send_reminder", "value": "Enviar Recordatorio"}, {"key": "admin.payments.actions.view_details", "value": "<PERSON><PERSON>"}, {"key": "admin.payments.performance.title", "value": "Rendimiento del Método de Pago"}, {"key": "admin.payments.performance.table.method", "value": "Mé<PERSON><PERSON>"}, {"key": "admin.payments.performance.table.transactions", "value": "Transacciones Totales"}, {"key": "admin.payments.performance.table.success_rate", "value": "Tasa de Éxito"}, {"key": "admin.payments.performance.table.revenue", "value": "Ingresos Totales"}, {"key": "admin.payments.performance.table.avg_amount", "value": "<PERSON><PERSON> Promedio"}, {"key": "admin.payments.export.title", "value": "Exportar Datos de Pago"}, {"key": "admin.payments.export.format.csv", "value": "CSV"}, {"key": "admin.payments.export.format.json", "value": "JSON"}, {"key": "admin.payments.export.button", "value": "Exportar Datos"}, {"key": "admin.payments.reminders.title", "value": "Enviar Recordatorio de Pago"}, {"key": "admin.payments.reminders.message_placeholder", "value": "Ingrese el mensaje de recordatorio..."}, {"key": "admin.payments.reminders.send_button", "value": "Enviar Recordatorio"}, {"key": "admin.payments.reminders.success", "value": "Recordatorio enviado con éxito"}, {"key": "admin.payments.status_update.title", "value": "Actualizar <PERSON>"}, {"key": "admin.payments.status_update.notes_placeholder", "value": "Ag<PERSON><PERSON> notas (opcional)..."}, {"key": "admin.payments.status_update.success", "value": "Estado de pago actualizado con éxito"}, {"key": "common.save", "value": "Guardar"}, {"key": "common.close", "value": "<PERSON><PERSON><PERSON>"}, {"key": "common.hide", "value": "Ocultar"}, {"key": "common.something_went_wrong", "value": "Algo salió mal"}, {"key": "flow_builder.send_message", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flow_builder.enter_message", "value": "Introduce tu mensaje..."}, {"key": "flow_builder.condition", "value": "Condición"}, {"key": "flow_builder.condition_example", "value": "si message.contains('hola')"}, {"key": "flow_builder.yes", "value": "Sí"}, {"key": "flow_builder.no", "value": "No"}, {"key": "flow_builder.input", "value": "Entrada"}, {"key": "flow_builder.collect_response", "value": "Recopilar respuesta del usuario"}, {"key": "flow_builder.action", "value": "Acción"}, {"key": "flow_builder.perform_api_call", "value": "Realizar llamada API"}, {"key": "flow_builder.add_node", "value": "Agregar <PERSON>"}, {"key": "flow_builder.message", "value": "Men<PERSON><PERSON>"}, {"key": "flow_builder.error_loading_flow", "value": "Error al cargar el flujo"}, {"key": "flow_builder.parse_error", "value": "No se pudieron analizar los datos del flujo"}, {"key": "flow_builder.flow_created", "value": "<PERSON><PERSON><PERSON> c<PERSON>o"}, {"key": "flow_builder.flow_created_success", "value": "Su flujo se ha creado correctamente."}, {"key": "flow_builder.plan_limit_reached", "value": "Límite de Plan Alcanzado"}, {"key": "flow_builder.upgrade_plan_message", "value": "Para crear más flujos, póngase en contacto con su administrador para actualizar su plan."}, {"key": "flow_builder.error_creating_flow", "value": "Error al crear el flujo"}, {"key": "flow_builder.flow_updated", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flow_builder.flow_updated_success", "value": "Su flujo se ha actualizado correctamente."}, {"key": "flow_builder.error_updating_flow", "value": "Error al actualizar el flujo"}, {"key": "flow_builder.name_required", "value": "Se requiere nombre"}, {"key": "flow_builder.provide_name", "value": "Por favor, proporcione un nombre para su flujo"}, {"key": "flow_builder.flow_name_placeholder", "value": "Nombre del flujo"}, {"key": "flow_builder.active", "value": "Activo"}, {"key": "flow_builder.draft", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flow_builder.creating_new_flow", "value": "Creando Nuevo Flujo"}, {"key": "flow_builder.current_flow_status", "value": "Estado actual del flujo"}, {"key": "flow_builder.ai_assistant", "value": "Asistente IA"}, {"key": "flow_builder.ai_history", "value": "Historial"}, {"key": "flow_builder.ai_messages", "value": "mensa<PERSON><PERSON>"}, {"key": "flow_builder.ai_audio_enabled", "value": "Audio habilitado"}, {"key": "flow_builder.ai_image_enabled", "value": "Imagen habilitada"}, {"key": "flow_builder.ai_video_enabled", "value": "Video habilitado"}, {"key": "flow_builder.ai_functions_enabled", "value": "Funciones habilitadas"}, {"key": "flow_builder.ai_provider", "value": "Proveedor de IA"}, {"key": "flow_builder.ai_select_provider", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"key": "flow_builder.ai_model", "value": "<PERSON><PERSON>"}, {"key": "flow_builder.ai_select_model", "value": "Seleccionar modelo"}, {"key": "flow_builder.ai_api_key", "value": "Clave API"}, {"key": "flow_builder.ai_api_key_placeholder", "value": "Introduce tu clave API de {{provider}}"}, {"key": "flow_builder.ai_get_api_key", "value": "Obtén tu clave API aquí"}, {"key": "flow_builder.ai_system_prompt", "value": "<PERSON><PERSON> (Prompt del Sistema)"}, {"key": "flow_builder.ai_prompt_placeholder", "value": "Introduce instrucciones para la IA"}, {"key": "flow_builder.ai_enable_history", "value": "Habilitar historial de conversación"}, {"key": "flow_builder.ai_history_limit", "value": "Límite de mensajes del historial"}, {"key": "flow_builder.ai_history_description", "value": "E<PERSON>ce cuántos mensajes anteriores incluir para el contexto."}, {"key": "flow_builder.ai_enable_audio", "value": "Habilitar procesamiento de audio"}, {"key": "flow_builder.ai_enable_image", "value": "Habilitar procesamiento de imágenes"}, {"key": "flow_builder.ai_image_tooltip", "value": "Permite a la IA procesar imágenes enviadas por el usuario. Nota: Solo disponible con modelos multimodales como Gemini 2.5."}, {"key": "flow_builder.ai_enable_video", "value": "Habilitar procesamiento de video"}, {"key": "flow_builder.ai_video_tooltip", "value": "Permite a la IA procesar archivos de video enviados por el usuario. Nota: Solo disponible con modelos multimodales como Gemini 2.5."}, {"key": "flow_builder.ai_enable_functions", "value": "Habilitar llamadas a funciones"}, {"key": "flow_builder.ai_functions_tooltip", "value": "Solo disponible con ciertos proveedores y modelos. Permite a la IA realizar acciones como programar citas, verificar datos, etc."}, {"key": "flow_builder.ai_enable_task_execution", "value": "Habilitar Ejecución de Tareas"}, {"key": "flow_builder.ai_task_execution_tooltip", "value": "Permite a la IA ejecutar tareas predefinidas y activar diferentes rutas de flujo según la intención del usuario. Cada tarea puede conectarse a diferentes nodos del flujo."}, {"key": "flow_builder.ai_tasks", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flow_builder.ai_add_task", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "flow_builder.ai_no_tasks", "value": "No hay tareas configuradas. Agregue una tarea para habilitar las llamadas a funciones de IA."}, {"key": "flow_builder.ai_tasks_description", "value": "Las tareas permiten a la IA ejecutar funciones específicas cuando se detecta la intención del usuario. Cada tarea crea un identificador de salida que se puede conectar a otros nodos."}, {"key": "flow_builder.ai_tasks_enabled", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flow_builder.ai_default_output", "value": "Salida Predeterminada"}, {"key": "flow_builder.ai_default_output_desc", "value": "Se utiliza cuando no se activan tareas o la ejecución de tareas está deshabilitada"}, {"key": "flow_builder.ai_session_stopped", "value": "Sesión <PERSON>"}, {"key": "flow_builder.ai_session_stopped_desc", "value": "Se activa cuando el usuario escribe la palabra clave de detención para salir de la sesión de IA"}, {"key": "flow_builder.ai_description", "value": "El nodo Asistente de IA procesará los mensajes entrantes y generará respuestas basadas en el contexto. Puede acceder al historial de conversaciones, datos de contacto y otras variables del flujo."}, {"key": "flow_builder.http_request", "value": "Solicitud HTTP"}, {"key": "flow_builder.http_no_url", "value": "No hay URL configurada"}, {"key": "flow_builder.http_auth", "value": "Autenticación"}, {"key": "flow_builder.http_header", "value": "encabezado"}, {"key": "flow_builder.http_body_configured", "value": "<PERSON><PERSON><PERSON> configurado"}, {"key": "flow_builder.http_retry", "value": "Reintentar"}, {"key": "flow_builder.http_mapping", "value": "mapeo"}, {"key": "flow_builder.http_quick_templates", "value": "Plant<PERSON><PERSON>"}, {"key": "flow_builder.http_choose_template", "value": "Elegir una plantilla..."}, {"key": "flow_builder.http_method", "value": "Método HTTP"}, {"key": "flow_builder.http_select_method", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"key": "flow_builder.http_request_url", "value": "URL de Solicitud"}, {"key": "flow_builder.webhook", "value": "Webhook"}, {"key": "flow_builder.webhook_no_url", "value": "No hay URL configurada"}, {"key": "flow_builder.webhook_body_configured", "value": "<PERSON><PERSON><PERSON> configurado"}, {"key": "flow_builder.webhook_method", "value": "Método HTTP"}, {"key": "flow_builder.webhook_select_method", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"key": "flow_builder.webhook_url", "value": "URL de Webhook"}, {"key": "flow_builder.webhook_url_placeholder", "value": "https://api.example.com/webhook"}, {"key": "flow_builder.webhook_test_tooltip", "value": "Prueba el webhook con la configuración actual"}, {"key": "flow_builder.webhook_authentication", "value": "Autenticación"}, {"key": "flow_builder.webhook_select_auth", "value": "Seleccionar tipo de autenticación"}, {"key": "flow_builder.webhook_bearer_token", "value": "<PERSON><PERSON>"}, {"key": "flow_builder.webhook_username", "value": "Nombre de Usuario"}, {"key": "flow_builder.webhook_password", "value": "Contraseña"}, {"key": "flow_builder.calendar_event", "value": "Evento de Calendario"}, {"key": "flow_builder.calendar_invalid_email", "value": "Correo electrónico no válido"}, {"key": "flow_builder.calendar_valid_email_required", "value": "Por favor, introduzca una dirección de correo electrónico válida"}, {"key": "flow_builder.calendar_untitled_event", "value": "Evento sin título"}, {"key": "flow_builder.calendar_start", "value": "<PERSON><PERSON>o"}, {"key": "flow_builder.calendar_end", "value": "Fin"}, {"key": "flow_builder.calendar_attendees", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "flow_builder.calendar_event_title", "value": "Título del Evento"}, {"key": "flow_builder.calendar_enter_title", "value": "Introduce el título del evento"}, {"key": "flow_builder.calendar_description", "value": "Descripción"}, {"key": "flow_builder.calendar_enter_description", "value": "Introduce la descripción del evento"}, {"key": "flow_builder.duplicate_node", "value": "Duplicar nodo"}, {"key": "flow_builder.delete_node", "value": "Eliminar nodo"}, {"key": "flow_builder.hard_reset_keyword", "value": "Palabra Clave de Restablecimiento Forzado"}, {"key": "flow_builder.hard_reset_keyword_placeholder", "value": "reset, restart, newchat, etc."}, {"key": "flow_builder.hard_reset_keyword_help", "value": "Cuando el bot está deshabilitado, los usuarios pueden escribir esta palabra clave para habilitar el bot nuevamente y comenzar de cero"}, {"key": "flow_builder.hard_reset_confirmation_message", "value": "Mensaje de Confirmación de Restablecimiento"}, {"key": "flow_builder.hard_reset_confirmation_placeholder", "value": "El bot ha sido reactivado. Iniciando nueva conversación..."}, {"key": "flow_builder.hard_reset_confirmation_help", "value": "Mensaje enviado al usuario cuando se activa el restablecimiento forzado"}, {"key": "flow_builder.hard_reset_label", "value": "Restablecimiento Forzado"}, {"key": "admin.translations.select_both", "value": "Por favor, seleccione tanto un idioma como un espacio de nombres para administrar las traducciones."}, {"key": "errors.404_title", "value": "404 Página No Encontrada"}, {"key": "errors.404_description", "value": "¿Olvidaste añadir la página al router?"}, {"key": "auth.welcome_team", "value": "¡Bienvenido al equipo!"}, {"key": "auth.account_created_success", "value": "Su cuenta ha sido creada exitosamente. Ahora ha iniciado sesión."}, {"key": "auth.validation.username_min_length", "value": "El nombre de usuario debe tener al menos 3 caracteres"}, {"key": "auth.validation.password_min_length", "value": "La contraseña debe tener al menos 6 caracteres"}, {"key": "auth.validation.passwords_no_match", "value": "Las contraseñas no coinciden"}, {"key": "auth.validation.full_name_required", "value": "El nombre completo es obligatorio"}, {"key": "auth.invalid_invitation_title", "value": "Enlace de Invitación Inválido"}, {"key": "auth.invalid_invitation_desc", "value": "El enlace de invitación falta o es inválido. Por favor, revise su correo electrónico y vuelva a intentarlo."}, {"key": "auth.go_to_login", "value": "<PERSON>r a Inicio de Sesión"}, {"key": "auth.verifying_invitation", "value": "Verificando invitación..."}, {"key": "auth.invitation_not_found_title", "value": "Invitación No Encontrada"}, {"key": "auth.invitation_not_found_desc", "value": "Este enlace de invitación es inválido, ha expirado o ya ha sido utilizado."}, {"key": "auth.accept_invitation_title", "value": "Aceptar Invitación de Equipo"}, {"key": "auth.invited_as_role", "value": "Ha sido invitado a unirse como {{role}}"}, {"key": "auth.full_name", "value": "Nombre Completo"}, {"key": "auth.enter_full_name", "value": "Introduce tu nombre completo"}, {"key": "auth.username", "value": "Nombre de Usuario"}, {"key": "auth.choose_username", "value": "Elige un nombre de usuario"}, {"key": "auth.password", "value": "Contraseña"}, {"key": "auth.create_password", "value": "Crea una contraseña"}, {"key": "auth.confirm_password", "value": "Con<PERSON><PERSON><PERSON>"}, {"key": "auth.confirm_password_placeholder", "value": "Confirma tu contraseña"}, {"key": "auth.creating_account", "value": "<PERSON><PERSON><PERSON>..."}, {"key": "auth.accept_invitation_button", "value": "Aceptar Invitación y Crear Cuenta"}, {"key": "auth.already_have_account", "value": "¿Ya tienes cuenta?"}, {"key": "auth.sign_in_here", "value": "Inicia sesión aquí"}, {"key": "auth.invitation_agreement", "value": "Al aceptar esta invitación, usted acepta unirse al equipo y seguir las políticas de la empresa."}, {"key": "auth.login_success", "value": "Inicio de sesión exitoso"}, {"key": "auth.welcome_back", "value": "¡Bienvenido de nuevo, {{name}}!"}, {"key": "auth.login_failed", "value": "Fallo de inicio de sesión"}, {"key": "auth.invalid_credentials", "value": "Nombre de usuario o contraseña inválidos"}, {"key": "auth.email", "value": "Correo Electrónico"}, {"key": "auth.email_placeholder", "value": "<EMAIL>"}, {"key": "auth.password_placeholder", "value": "â€¢â€¢â€¢â€¢â€¢â€¢â€¢â€¢"}, {"key": "auth.logging_in", "value": "Iniciando se<PERSON>..."}, {"key": "auth.login", "value": "<PERSON><PERSON><PERSON>"}, {"key": "auth.welcome_title", "value": "Bienvenido a {{appName}}"}, {"key": "auth.welcome_subtitle", "value": "Plataforma de bandeja de entrada de equipo multicanal y chatbot IA"}, {"key": "auth.login_title", "value": "Inicia sesión en tu cuenta"}, {"key": "auth.login_description", "value": "Introduce tu nombre de usuario y contraseña para acceder a tu cuenta"}, {"key": "auth.username_placeholder", "value": "Tu nombre de usuario"}, {"key": "auth.register_company", "value": "Registrar una nueva empresa"}, {"key": "auth.hero_title", "value": "Unifica tus Comunicaciones con Clientes"}, {"key": "auth.hero_subtitle", "value": "La plataforma todo en uno para gestionar conversaciones con clientes a través de WhatsApp, Facebook, Instagram y más."}, {"key": "auth.feature_inbox_title", "value": "Bandeja de entrada multicanal"}, {"key": "auth.feature_inbox_desc", "value": "Gestiona todas las conversaciones con clientes en una bandeja de entrada unificada"}, {"key": "auth.feature_ai_title", "value": "Chatbots impulsados por IA"}, {"key": "auth.feature_ai_desc", "value": "Automatiza respuestas con chatbots de IA personalizables"}, {"key": "auth.feature_team_title", "value": "Colaboración en equipo"}, {"key": "auth.feature_team_desc", "value": "Asigna conversaciones, deja notas y colabora eficazmente"}, {"key": "admin.login_title", "value": "Inicio de Sesión Admin"}, {"key": "admin.login_description", "value": "Introduce tus credenciales para acceder al panel de administración"}, {"key": "admin.restricted_area", "value": "Esta área está restringida solo para administradores"}, {"key": "nav.dashboard", "value": "Panel de Control"}, {"key": "dashboard.total_conversations", "value": "Conversaciones Totales"}, {"key": "dashboard.active_conversations", "value": "Conversaciones Activas"}, {"key": "dashboard.response_time", "value": "Tiempo de Respuesta"}, {"key": "nav.inbox", "value": "Bandeja de Entrada"}, {"key": "nav.flow_builder", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "nav.contacts", "value": "Contactos"}, {"key": "nav.pipeline", "value": "Pipeline"}, {"key": "nav.calendar", "value": "Calendario"}, {"key": "nav.campaigns", "value": "Campañas"}, {"key": "nav.analytics", "value": "Analíticas"}, {"key": "nav.channels", "value": "Canales"}, {"key": "nav.settings", "value": "Configuraciones"}, {"key": "nav.help_support", "value": "Ayuda y Soporte"}, {"key": "nav.company", "value": "Empresa"}, {"key": "nav.plan", "value": "Plan"}, {"key": "nav.profile", "value": "Perfil"}, {"key": "admin.nav.dashboard", "value": "Panel de Control"}, {"key": "admin.nav.companies", "value": "Empresas"}, {"key": "admin.nav.users", "value": "Usuarios"}, {"key": "admin.nav.plans", "value": "Planes"}, {"key": "admin.nav.analytics", "value": "Analíticas"}, {"key": "admin.nav.translations", "value": "Traducciones"}, {"key": "admin.nav.settings", "value": "Configuraciones"}, {"key": "admin.companies.new_company", "value": "Nueva Empresa"}, {"key": "admin.companies.total_companies", "value": "Total de Empresas"}, {"key": "admin.companies.active_companies", "value": "Empresas Activas"}, {"key": "admin.companies.inactive_companies", "value": "Empresas Inactivas"}, {"key": "admin.companies.manage_description", "value": "Administra todas las empresas del sistema"}, {"key": "admin.companies.no_companies_found", "value": "No se encontraron empresas. Crea tu primera empresa para empezar."}, {"key": "admin.companies.table.name", "value": "Nombre"}, {"key": "admin.companies.table.slug", "value": "Slug"}, {"key": "admin.companies.table.plan", "value": "Plan"}, {"key": "admin.companies.table.status", "value": "Estado"}, {"key": "admin.companies.table.actions", "value": "Acciones"}, {"key": "common.active", "value": "Activo"}, {"key": "common.inactive", "value": "Inactivo"}, {"key": "common.manage", "value": "Gestionar"}, {"key": "common.please_wait", "value": "Por favor, espere..."}, {"key": "common.search", "value": "Buscar"}, {"key": "common.searching_for", "value": "Buscando: {{query}}"}, {"key": "common.search_placeholder", "value": "Buscar conversaciones, plantillas..."}, {"key": "admin.returning_to_admin", "value": "Volviendo a la cuenta de administrador"}, {"key": "admin.error_returning", "value": "Error al volver al administrador"}, {"key": "admin.trying_fallback", "value": "Intentando método de reserva..."}, {"key": "admin.returning", "value": "Volviendo..."}, {"key": "admin.return_to_admin", "value": "Volver al Administrador"}, {"key": "admin.impersonating", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"key": "auth.logged_out", "value": "Cerrada la sesión"}, {"key": "auth.logged_out_success", "value": "Ha cerrado la sesión correctamente"}, {"key": "auth.logout_failed", "value": "Fallo al cerrar sesión: {{error}}"}, {"key": "auth.back_to_login", "value": "Volver al inicio de sesión"}, {"key": "registration.success_title", "value": "¡Registro Exitoso!"}, {"key": "registration.approval_required", "value": "El registro de su empresa ha sido enviado para aprobación. Recibirá un correo electrónico una vez aprobado."}, {"key": "registration.success_desc", "value": "Su empresa ha sido registrada correctamente. Ahora puede iniciar sesión."}, {"key": "registration.failed_title", "value": "Fallo en el Registro"}, {"key": "registration.unavailable_title", "value": "Registro No Disponible"}, {"key": "registration.unavailable_desc", "value": "El registro de empresas está actualmente deshabilitado. Por favor, contacte al administrador para más información."}, {"key": "registration.title", "value": "Registro de Empresa"}, {"key": "registration.description", "value": "Complete los detalles a continuación para registrar su empresa y crear una cuenta de administrador"}, {"key": "registration.approval_notice", "value": "Los nuevos registros requieren aprobación del administrador. Recibirá un correo electrónico una vez que su registro sea aprobado."}, {"key": "registration.company_info", "value": "Información de la Empresa"}, {"key": "registration.company_name", "value": "Nombre de la Empresa"}, {"key": "registration.company_name_placeholder", "value": "Nombre de tu Empresa"}, {"key": "registration.admin_details", "value": "Detalles del Usuario Administrador"}, {"key": "registration.plan_selection", "value": "Selección de Plan"}, {"key": "registration.registering", "value": "Registrando..."}, {"key": "registration.register_button", "value": "Registrar Empresa"}, {"key": "registration.page_title", "value": "Registra tu Empresa"}, {"key": "registration.page_subtitle", "value": "Crea la cuenta de tu empresa y empieza a gestionar conversaciones"}, {"key": "registration.company_slug", "value": "Slug de la Empresa"}, {"key": "registration.company_slug_placeholder", "value": "tu-empresa"}, {"key": "registration.company_slug_description", "value": "Este será tu identificador único (ej. tu-empresa.app.com)"}, {"key": "registration.company_email", "value": "Correo Electrónico de la Empresa"}, {"key": "registration.company_email_placeholder", "value": "<EMAIL>"}, {"key": "registration.company_phone", "value": "Teléfono de la Empresa"}, {"key": "registration.company_phone_placeholder", "value": "+****************"}, {"key": "registration.company_website", "value": "Sitio Web de la Empresa"}, {"key": "registration.company_website_placeholder", "value": "https://tueempresa.com"}, {"key": "registration.admin_full_name", "value": "Nombre Completo"}, {"key": "registration.admin_full_name_placeholder", "value": "<PERSON>"}, {"key": "registration.admin_email", "value": "Dirección de Correo Electrónico"}, {"key": "registration.admin_email_placeholder", "value": "<EMAIL>"}, {"key": "registration.admin_username", "value": "Nombre de Usuario"}, {"key": "registration.admin_username_placeholder", "value": "juan<PERSON><PERSON>"}, {"key": "registration.admin_username_description", "value": "Este se utilizará para iniciar sesión en tu cuenta"}, {"key": "registration.admin_password", "value": "Contraseña"}, {"key": "registration.admin_password_placeholder", "value": "Crea una contraseña segura"}, {"key": "registration.admin_confirm_password", "value": "Con<PERSON><PERSON><PERSON>"}, {"key": "registration.admin_confirm_password_placeholder", "value": "Confirma tu contraseña"}, {"key": "registration.select_plan", "value": "Seleccionar Plan"}, {"key": "registration.select_plan_placeholder", "value": "Elige un plan"}, {"key": "registration.plan_change_note", "value": "<PERSON>uedes cambiar tu plan más tarde desde la configuración"}, {"key": "registration.validation.company_name_min", "value": "El nombre de la empresa debe tener al menos 2 caracteres"}, {"key": "registration.validation.company_slug_min", "value": "El slug de la empresa debe tener al menos 3 caracteres"}, {"key": "registration.validation.company_slug_format", "value": "El slug solo puede contener letras minúsculas, números y guiones"}, {"key": "registration.validation.company_email_invalid", "value": "Por favor, introduce una dirección de correo electrónico de empresa válida"}, {"key": "registration.validation.company_website_invalid", "value": "Por favor, introduce una URL de sitio web válida"}, {"key": "registration.validation.admin_name_min", "value": "El nombre completo debe tener al menos 2 caracteres"}, {"key": "registration.validation.admin_email_invalid", "value": "Por favor, introduce una dirección de correo electrónico válida"}, {"key": "registration.validation.admin_username_min", "value": "El nombre de usuario debe tener al menos 3 caracteres"}, {"key": "registration.validation.admin_password_min", "value": "La contraseña debe tener al menos 6 caracteres"}, {"key": "registration.validation.confirm_password_required", "value": "Por favor, confirma tu contraseña"}, {"key": "registration.validation.passwords_no_match", "value": "Las contraseñas no coinciden"}, {"key": "registration.validation.plan_required", "value": "Por favor, selecciona un plan"}, {"key": "registration.validation.slug_taken", "value": "Este slug ya está en uso"}, {"key": "registration.error.status_check_failed", "value": "Error al verificar el estado del registro"}, {"key": "registration.error.plans_fetch_failed", "value": "Error al obtener los planes"}, {"key": "registration.error.slug_check_failed", "value": "Error al verificar la disponibilidad del slug"}, {"key": "registration.error.register_failed", "value": "Error al registrar la empresa"}, {"key": "flow_builder.error.no_file", "value": "No se ha seleccionado ningún archivo"}, {"key": "flow_builder.error.no_file_desc", "value": "Por favor, selecciona un archivo para subir"}, {"key": "flow_builder.error.invalid_file_type", "value": "Tipo de archivo inválido"}, {"key": "flow_builder.error.invalid_image_type", "value": "<PERSON>r favor, selecciona un archivo de imagen válido (JPEG, PNG, GIF, WebP, SVG)"}, {"key": "flow_builder.error.file_too_large", "value": "Archivo demasiado grande"}, {"key": "flow_builder.error.max_file_size", "value": "El tamaño máximo del archivo es 30MB"}, {"key": "flow_builder.error.upload_failed", "value": "Fallo al subir"}, {"key": "flow_builder.error.upload_error", "value": "Ocurrió un error al subir el archivo"}, {"key": "flow_builder.success.upload_complete", "value": "Subida completa"}, {"key": "flow_builder.success.image_uploaded", "value": "Imagen subida correctamente"}, {"key": "flow_builder.success.image_removed", "value": "Imagen eliminada"}, {"key": "flow_builder.success.image_removed_desc", "value": "La imagen ha sido eliminada del nodo"}, {"key": "flow_builder.remove_image", "value": "Eliminar Imagen"}, {"key": "flow_builder.preview.loading_audio", "value": "Cargando audio..."}, {"key": "flow_builder.preview.loading_video", "value": "Cargando video..."}, {"key": "flow_builder.preview.loading_image", "value": "Cargando imagen..."}, {"key": "flow_builder.preview.failed_audio", "value": "Error al cargar audio"}, {"key": "flow_builder.preview.failed_video", "value": "Error al cargar video"}, {"key": "flow_builder.preview.failed_image", "value": "Error al cargar imagen"}, {"key": "flow_builder.preview.duration", "value": "Duración"}, {"key": "flow_builder.preview.resolution", "value": "Resolución"}, {"key": "flow_builder.preview.click_to_download", "value": "Haz clic para descargar/ver"}, {"key": "flow_builder.preview.open_document", "value": "Abrir"}, {"key": "flow_builder.preview_audio", "value": "Previsualizar Audio"}, {"key": "flow_builder.preview_video", "value": "Previsualizar Video"}, {"key": "flow_builder.preview_document", "value": "Previsualizar Documento"}, {"key": "flow_builder.hide_preview", "value": "Ocultar Previsualización"}, {"key": "inbox.conversations", "value": "Conversaciones"}, {"key": "inbox.filtered_by_channel", "value": "Filtrado por canal"}, {"key": "inbox.clear_channel_filter", "value": "Borrar filtro de canal"}, {"key": "inbox.filter_conversations", "value": "Filtrar conversaciones"}, {"key": "inbox.start_new_conversation", "value": "Iniciar nueva conversación"}, {"key": "inbox.search_conversations", "value": "Buscar conversaciones"}, {"key": "inbox.search_conversations_enhanced", "value": "Buscar por nombre, etiqueta, teléfono, correo electrónico..."}, {"key": "inbox.filter.all", "value": "Todos"}, {"key": "inbox.filter.unassigned", "value": "No asignados"}, {"key": "inbox.filter.my_chats", "value": "<PERSON><PERSON>"}, {"key": "inbox.filter.assigned", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "inbox.no_conversations_found", "value": "No se encontraron conversaciones"}, {"key": "inbox.no_conversation_selected", "value": "No Hay Conversación Seleccionada"}, {"key": "inbox.select_conversation_hint", "value": "Selecciona una conversación de la lista para ver los mensajes"}, {"key": "inbox.show_conversations", "value": "Mostrar conversaciones"}, {"key": "inbox.close_conversations", "value": "Cerrar conversaciones"}, {"key": "inbox.new_lead", "value": "Nuevo Cliente Potencial"}, {"key": "inbox.last_active_time", "value": "Última actividad hace 10 min"}, {"key": "inbox.conversation_started_via", "value": "Conversación iniciada a través de {{channel}}"}, {"key": "inbox.scroll_to_bottom", "value": "<PERSON><PERSON><PERSON><PERSON> hasta el final"}, {"key": "inbox.new_conversation", "value": "Nueva Conversación"}, {"key": "inbox.not_connected", "value": "No Conectado"}, {"key": "inbox.cannot_send_message", "value": "No se puede enviar mensaje, no conectado al servidor"}, {"key": "inbox.failed_send_media", "value": "Error al enviar mensaje multimedia"}, {"key": "contacts.redirecting_to_inbox", "value": "Redirigiendo a la bandeja de entrada"}, {"key": "contacts.opening_conversation_with", "value": "Abriendo conversación con {{name}}"}, {"key": "contacts.add_contact", "value": "<PERSON>g<PERSON><PERSON>"}, {"key": "contacts.search_placeholder", "value": "Buscar contactos..."}, {"key": "contacts.all_channels", "value": "Todos los Canales"}, {"key": "contacts.whatsapp_official", "value": "WhatsApp Oficial"}, {"key": "contacts.whatsapp_unofficial", "value": "WhatsApp No Oficial"}, {"key": "contacts.add.title", "value": "Agregar Nuevo Contacto"}, {"key": "contacts.add.description", "value": "Crea un nuevo contacto con la información a continuación."}, {"key": "contacts.add.name_label", "value": "Nombre"}, {"key": "contacts.add.name_placeholder", "value": "Introduce el nombre del contacto"}, {"key": "contacts.add.name_required", "value": "Se requiere el nombre del contacto"}, {"key": "contacts.add.email_label", "value": "Correo Electrónico"}, {"key": "contacts.add.email_placeholder", "value": "Introduce la dirección de correo electrónico"}, {"key": "contacts.add.phone_label", "value": "Teléfono"}, {"key": "contacts.add.phone_placeholder", "value": "Introduce el número de teléfono"}, {"key": "contacts.add.company_label", "value": "Empresa"}, {"key": "contacts.add.company_placeholder", "value": "Introduce el nombre de la empresa"}, {"key": "contacts.add.channel_label", "value": "Canal"}, {"key": "contacts.add.select_channel_placeholder", "value": "Selecciona canal"}, {"key": "contacts.add.channel.whatsapp_official", "value": "WhatsApp Oficial"}, {"key": "contacts.add.channel.whatsapp_unofficial", "value": "WhatsApp No Oficial"}, {"key": "contacts.add.channel.messenger", "value": "Facebook Messenger"}, {"key": "contacts.add.channel.instagram", "value": "Instagram"}, {"key": "contacts.add.channel_identifier_label", "value": "Identificador de Canal"}, {"key": "contacts.add.channel_identifier_placeholder", "value": "Número de teléfono o ID"}, {"key": "contacts.add.tags_label", "value": "Etiquetas (separadas por comas)"}, {"key": "contacts.add.tags_placeholder", "value": "cliente potencial, cliente, etc."}, {"key": "contacts.add.notes_label", "value": "Notas"}, {"key": "contacts.add.notes_placeholder", "value": "Notas adicionales sobre este contacto..."}, {"key": "contacts.add.creating", "value": "Creando..."}, {"key": "contacts.add.create_button", "value": "<PERSON><PERSON><PERSON>"}, {"key": "contacts.add.success_title", "value": "<PERSON><PERSON> c<PERSON>o"}, {"key": "contacts.add.success_description", "value": "El contacto se ha creado correctamente."}, {"key": "contacts.add.error_title", "value": "Fallo en la creación"}, {"key": "contacts.import.button", "value": "Importar CSV"}, {"key": "contacts.import.title", "value": "Importar Contactos desde CSV"}, {"key": "contacts.import.description", "value": "Sube un archivo CSV para importar varios contactos a la vez. Descarga la plantilla para ver el formato requerido."}, {"key": "contacts.import.download_template", "value": "Descargar Plantilla"}, {"key": "contacts.import.file_label", "value": "Archivo CSV"}, {"key": "contacts.import.file_help", "value": "Tamaño máximo de archivo: 10MB. Solo se admiten archivos CSV."}, {"key": "contacts.import.preview_label", "value": "Vista previa (primeras 5 filas)"}, {"key": "contacts.import.duplicate_handling_label", "value": "Man<PERSON>o <PERSON>"}, {"key": "contacts.import.duplicate.skip", "value": "<PERSON><PERSON><PERSON>"}, {"key": "contacts.import.duplicate.update", "value": "Actualizar existentes"}, {"key": "contacts.import.duplicate.create", "value": "<PERSON><PERSON><PERSON> nue<PERSON>"}, {"key": "contacts.import.duplicate_help", "value": "Cómo manejar contactos con direcciones de correo electrónico duplicadas"}, {"key": "contacts.import.importing", "value": "Importando..."}, {"key": "contacts.import.results_label", "value": "Resultados de Importación"}, {"key": "contacts.import.successful", "value": "Importados con éxito: {{count}}"}, {"key": "contacts.import.failed", "value": "Fallo al importar: {{count}}"}, {"key": "contacts.import.errors", "value": "Errores:"}, {"key": "contacts.import.more_errors", "value": "Y {{count}} errores más..."}, {"key": "contacts.import.import_button", "value": "Importar Contactos"}, {"key": "contacts.import.no_file_selected", "value": "Por favor, selecciona un archivo CSV para importar"}, {"key": "contacts.import.success_title", "value": "Importación completada"}, {"key": "contacts.import.success_description", "value": "Se importaron con éxito {{count}} contactos"}, {"key": "contacts.import.error_title", "value": "Fallo en la importación"}, {"key": "contacts.bulk_actions.selected_count", "value": "{{count}} contactos seleccionados"}, {"key": "contacts.bulk_actions.clear_selection", "value": "<PERSON><PERSON><PERSON>"}, {"key": "contacts.bulk_actions.delete_selected", "value": "Eliminar Seleccionados"}, {"key": "contacts.bulk_actions.deleting", "value": "Eliminando..."}, {"key": "contacts.bulk_actions.select_all", "value": "Seleccionar todos los contactos"}, {"key": "contacts.bulk_actions.select_contact", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON> contacto {{name}}"}, {"key": "contacts.bulk_delete.title", "value": "Eliminar {{count}} <PERSON><PERSON>"}, {"key": "contacts.bulk_delete.warning", "value": "Esto eliminará permanentemente estos contactos y todas las conversaciones, mensajes y notas asociadas. Esta acción no se puede deshacer."}, {"key": "contacts.bulk_delete.confirm", "value": "Eliminar {{count}} <PERSON><PERSON>"}, {"key": "contacts.bulk_delete.deleting", "value": "Eliminando..."}, {"key": "contacts.bulk_delete.success_title", "value": "Contactos eliminados"}, {"key": "contacts.bulk_delete.success_description", "value": "Se eliminaron con éxito {{count}} de {{total}} contactos"}, {"key": "contacts.bulk_delete.partial_failure_title", "value": "Algunas eliminaciones fallaron"}, {"key": "contacts.bulk_delete.partial_failure_description", "value": "{{count}} contactos no pudieron ser eliminados"}, {"key": "contacts.bulk_delete.error_title", "value": "Fallo en la eliminación masiva"}, {"key": "contacts.facebook_messenger", "value": "Facebook Messenger"}, {"key": "contacts.instagram", "value": "Instagram"}, {"key": "contacts.no_contacts_found", "value": "No se encontraron contactos"}, {"key": "contacts.try_adjusting_filters", "value": "Intenta ajustar tu búsqueda o filtros"}, {"key": "contacts.add_first_contact", "value": "Agrega tu primer contacto para empezar"}, {"key": "contacts.table.name", "value": "Nombre"}, {"key": "contacts.table.contact_info", "value": "Información de Contacto"}, {"key": "contacts.table.company", "value": "Empresa"}, {"key": "contacts.table.channel", "value": "Canal"}, {"key": "contacts.table.tags", "value": "Etiquetas"}, {"key": "contacts.table.last_updated", "value": "Última Actualización"}, {"key": "contacts.table.actions", "value": "Acciones"}, {"key": "contacts.message", "value": "Men<PERSON><PERSON>"}, {"key": "contacts.delete_contact", "value": "Eliminar <PERSON>o"}, {"key": "contacts.delete_warning", "value": "Esto eliminará permanentemente este contacto y todas las conversaciones, mensajes y notas asociadas. Esta acción no se puede deshacer."}, {"key": "common.today", "value": "Hoy"}, {"key": "common.unknown_contact", "value": "Contacto Desconocido"}, {"key": "common.cancel", "value": "<PERSON><PERSON><PERSON>"}, {"key": "common.delete", "value": "Eliminar"}, {"key": "common.filter", "value": "Filtrar"}, {"key": "common.success", "value": "Éxito"}, {"key": "common.missing_information", "value": "Información faltante"}, {"key": "common.fill_required_fields", "value": "Por favor, complete todos los campos requeridos"}, {"key": "pipeline.stage_created_success", "value": "Etapa creada con éxito"}, {"key": "pipeline.stage_create_failed", "value": "Error al crear etapa: {{error}}"}, {"key": "pipeline.stage_updated_success", "value": "Etapa actualizada con éxito"}, {"key": "pipeline.stage_update_failed", "value": "Error al actualizar etapa: {{error}}"}, {"key": "pipeline.stage_deleted_success", "value": "Etapa eliminada con éxito"}, {"key": "pipeline.stage_delete_failed", "value": "Error al eliminar etapa: {{error}}"}, {"key": "pipeline.deal_update_failed", "value": "Error al actualizar la etapa de oportunidad: {{error}}"}, {"key": "pipeline.stage_name_required", "value": "El nombre de la etapa no puede estar vacío"}, {"key": "pipeline.add_stage", "value": "Agregar <PERSON>"}, {"key": "pipeline.add_deal", "value": "Agregar Oportunidad"}, {"key": "pipeline.no_stages", "value": "Sin Etapas de Pipeline"}, {"key": "pipeline.create_first_stage", "value": "<PERSON>rea tu primera etapa para empezar"}, {"key": "pipeline.add_first_stage", "value": "Agregar Primera Etapa"}, {"key": "pipeline.add_stage_title", "value": "Agregar Etapa de Pipeline"}, {"key": "pipeline.add_stage_description", "value": "Crea una nueva etapa para tu pipeline. Las etapas ayudan a organizar tus oportunidades y a seguir su progreso."}, {"key": "pipeline.stage_name", "value": "Nombre de la Etapa"}, {"key": "pipeline.stage_name_placeholder", "value": "ej., Descubrimiento, Negociación, Propuesta"}, {"key": "pipeline.stage_color", "value": "Color de la Etapa"}, {"key": "pipeline.create_stage", "value": "<PERSON><PERSON><PERSON>"}, {"key": "pipeline.edit_stage_title", "value": "Editar Etapa de Pipeline"}, {"key": "pipeline.edit_stage_description", "value": "Actualiza el nombre y el color de la etapa."}, {"key": "pipeline.update_stage", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "pipeline.delete_stage_title", "value": "Eliminar Etapa de Pipeline"}, {"key": "pipeline.stage_contains_deals", "value": "Esta etapa contiene {{count}} oportunidades. ¿A dónde te gustaría moverlas?"}, {"key": "pipeline.delete_stage_confirmation", "value": "¿Está seguro de que desea eliminar esta etapa? Esta acción no se puede deshacer."}, {"key": "pipeline.move_deals_to", "value": "Mover oportunidades a"}, {"key": "pipeline.select_stage", "value": "Seleccionar una etapa"}, {"key": "pipeline.select_target_stage_warning", "value": "Debes seleccionar una etapa de destino o las oportunidades en esta etapa se perderán."}, {"key": "pipeline.delete_stage", "value": "Eliminar Etapa"}, {"key": "calendar.event_created", "value": "<PERSON><PERSON>"}, {"key": "calendar.appointment_created_success", "value": "Su cita se ha creado correctamente"}, {"key": "calendar.event_create_failed", "value": "Error al crear evento: {{error}}"}, {"key": "calendar.event_updated", "value": "Evento Actualizado"}, {"key": "calendar.appointment_updated_success", "value": "Su cita se ha actualizado correctamente"}, {"key": "calendar.event_update_failed", "value": "Error al actualizar evento: {{error}}"}, {"key": "calendar.event_canceled", "value": "Evento Cancelado"}, {"key": "calendar.appointment_canceled_success", "value": "Su cita se ha cancelado correctamente"}, {"key": "calendar.event_cancel_failed", "value": "Error al cancelar evento: {{error}}"}, {"key": "calendar.not_connected", "value": "Google Calendar No Conectado"}, {"key": "calendar.connect_first", "value": "Por favor, conecte su Google Calendar en Configuración primero"}, {"key": "calendar.enter_schedule_name", "value": "Por favor, introduzca un nombre para el horario"}, {"key": "calendar.schedule_added", "value": "<PERSON><PERSON><PERSON>"}, {"key": "calendar.schedule_added_success", "value": "Se ha agregado el nuevo horario \"{{name}}\""}, {"key": "calendar.schedule_updated", "value": "Horario Actualizado"}, {"key": "calendar.schedule_updated_success", "value": "El horario \"{{name}}\" se ha actualizado"}, {"key": "calendar.schedule_deleted", "value": "<PERSON><PERSON><PERSON>"}, {"key": "calendar.schedule_deleted_success", "value": "El horario \"{{name}}\" ha sido eliminado"}, {"key": "calendar.more", "value": "más"}, {"key": "calendar.personal_teams", "value": "Personal, Equipos"}, {"key": "calendar.today", "value": "Hoy"}, {"key": "calendar.month", "value": "<PERSON><PERSON>"}, {"key": "calendar.week", "value": "Se<PERSON>"}, {"key": "calendar.day", "value": "Día"}, {"key": "calendar.my_schedules", "value": "<PERSON><PERSON>"}, {"key": "calendar.categories", "value": "Categorías"}, {"key": "calendar.sun", "value": "Dom"}, {"key": "calendar.mon", "value": "<PERSON>n"}, {"key": "calendar.tue", "value": "Mar"}, {"key": "calendar.wed", "value": "<PERSON><PERSON>"}, {"key": "calendar.thu", "value": "<PERSON><PERSON>"}, {"key": "calendar.fri", "value": "Vie"}, {"key": "calendar.sat", "value": "<PERSON><PERSON><PERSON>"}, {"key": "calendar.check_availability", "value": "Verificar Disponibilidad"}, {"key": "calendar.schedule_appointment", "value": "Programar Cita"}, {"key": "calendar.schedule_new_appointment", "value": "Programar Nueva Cita"}, {"key": "calendar.create_new_event", "value": "Crea un nuevo evento en tu calendario."}, {"key": "calendar.title", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "calendar.description", "value": "Descripción"}, {"key": "calendar.location", "value": "Ubicación"}, {"key": "calendar.category", "value": "Categoría"}, {"key": "calendar.select_category", "value": "Selecciona categoría"}, {"key": "campaigns.completed", "value": "Campaña <PERSON>tada"}, {"key": "campaigns.finished_processing", "value": "La campaña \"{{name}}\" ha terminado de procesarse."}, {"key": "campaigns.fetch_failed", "value": "Error al obtener campañas"}, {"key": "campaigns.stats_fetch_failed", "value": "Error al obtener estadísticas de la campaña"}, {"key": "campaigns.action_failed", "value": "Error al {{action}} la campaña"}, {"key": "campaigns.deleted_successfully", "value": "La campaña \"{{name}}\" se ha eliminado correctamente"}, {"key": "campaigns.delete_failed", "value": "Error al eliminar la campaña"}, {"key": "campaigns.stats_recalculated", "value": "Estadísticas Recalculadas"}, {"key": "campaigns.stats_updated", "value": "Las estadísticas de la campaña se han actualizado."}, {"key": "campaigns.stats_recalc_failed", "value": "Error al recalcular estadísticas: {{error}}"}, {"key": "campaigns.network_error", "value": "Error de red: {{error}}"}, {"key": "campaigns.dashboard_description", "value": "Gestiona y supervisa tus campañas de mensajería masiva"}, {"key": "campaigns.live_updates", "value": "Actualizaciones en vivo"}, {"key": "campaigns.polling_mode", "value": "<PERSON><PERSON> de sondeo"}, {"key": "campaigns.refresh", "value": "Actualizar"}, {"key": "campaigns.create_campaign", "value": "<PERSON><PERSON><PERSON>"}, {"key": "campaigns.total_campaigns", "value": "Campañas Totales"}, {"key": "campaigns.active_campaigns", "value": "Campañas Activas"}, {"key": "campaigns.total_recipients", "value": "Destinatarios Totales"}, {"key": "campaigns.messages_delivered", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "campaigns.delivery_rate", "value": "Tasa <PERSON>ga"}, {"key": "campaigns.no_campaigns_found", "value": "No se encontraron campañas"}, {"key": "campaigns.get_started_message", "value": "Comienza creando tu primera campaña de mensajería masiva"}, {"key": "campaigns.details", "value": "Detalles"}, {"key": "campaigns.recipients", "value": "Des<PERSON><PERSON><PERSON>"}, {"key": "campaigns.progress", "value": "Progreso"}, {"key": "campaigns.delivered", "value": "<PERSON><PERSON><PERSON>"}, {"key": "campaigns.failed", "value": "Fallido"}, {"key": "campaigns.processed", "value": "procesado"}, {"key": "campaigns.total", "value": "total"}, {"key": "campaigns.delete_campaign", "value": "Eliminar <PERSON>"}, {"key": "campaigns.delete_confirmation", "value": "¿Está seguro de que desea eliminar la campaña \"{{name}}\"? Esta acción no se puede deshacer y todos los datos de la campaña se eliminarán permanentemente."}, {"key": "campaigns.start", "value": "Iniciar"}, {"key": "campaigns.pause", "value": "Pausar"}, {"key": "campaigns.resume", "value": "<PERSON><PERSON><PERSON>"}, {"key": "campaigns.status.draft", "value": "<PERSON><PERSON><PERSON>"}, {"key": "campaigns.status.scheduled", "value": "Programado"}, {"key": "campaigns.status.running", "value": "En ejecución"}, {"key": "campaigns.status.paused", "value": "<PERSON><PERSON><PERSON>"}, {"key": "campaigns.status.completed", "value": "Completado"}, {"key": "campaigns.status.cancelled", "value": "Cancelado"}, {"key": "campaigns.status.failed", "value": "Fallido"}, {"key": "campaigns.filter.all", "value": "Todos"}, {"key": "campaigns.filter.draft", "value": "<PERSON><PERSON><PERSON>"}, {"key": "campaigns.filter.running", "value": "En ejecución"}, {"key": "campaigns.filter.paused", "value": "<PERSON><PERSON><PERSON>"}, {"key": "campaigns.filter.completed", "value": "Completado"}, {"key": "flows.flow_assigned", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flows.flow_assigned_success", "value": "El flujo se ha asignado al canal correctamente."}, {"key": "flows.error_assigning_flow", "value": "Error al asignar el flujo"}, {"key": "flows.assignment_updated", "value": "Asignación actualizada"}, {"key": "flows.assignment_updated_success", "value": "El estado de asignación del flujo se ha actualizado."}, {"key": "flows.error_updating_assignment", "value": "Error al actualizar la asignación"}, {"key": "flows.assign", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flows.manage_flow_assignments", "value": "Administrar Asignaciones de Flujo"}, {"key": "flows.assign_flow_description", "value": "Asigna \"{{flowName}}\" a canales y administra las asignaciones activas."}, {"key": "flows.active_assignments", "value": "Asignaciones Activas"}, {"key": "flows.channel", "value": "Canal"}, {"key": "flows.status", "value": "Estado"}, {"key": "flows.actions", "value": "Acciones"}, {"key": "flows.active", "value": "Activo"}, {"key": "flows.inactive", "value": "Inactivo"}, {"key": "flows.deactivate", "value": "Desactivar"}, {"key": "flows.activate", "value": "Activar"}, {"key": "flows.no_assignments_yet", "value": "Aún no hay asignaciones"}, {"key": "flows.available_channels", "value": "Canales Disponibles"}, {"key": "flows.no_channels_available", "value": "No hay canales disponibles"}, {"key": "flows.flow_deleted", "value": "<PERSON><PERSON><PERSON> eliminado"}, {"key": "flows.flow_deleted_success", "value": "El flujo se ha eliminado correctamente."}, {"key": "flows.error_deleting_flow", "value": "Error al eliminar el flujo"}, {"key": "flows.delete_flow", "value": "Eliminar Flujo"}, {"key": "flows.delete_flow_confirmation", "value": "¿Está seguro de que desea eliminar \"{{flowName}}\"? Esta acción no se puede deshacer."}, {"key": "flows.draft", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flows.archived", "value": "Archivado"}, {"key": "flows.page_description", "value": "Crea y administra flujos de conversación automatizados para tus canales."}, {"key": "flows.flows", "value": "flujos"}, {"key": "flows.limit_reached", "value": "Lí<PERSON>"}, {"key": "flows.contact_admin_upgrade", "value": "Contacta a tu administrador para actualizar tu plan"}, {"key": "flows.plan_limit_tooltip", "value": "Has alcanzado el límite de flujos de tu plan"}, {"key": "flows.new_flow", "value": "Nuevo Flujo"}, {"key": "flows.your_flows", "value": "<PERSON><PERSON>"}, {"key": "flows.flows_description", "value": "Los flujos se pueden asignar a uno o más canales para manejar conversaciones automáticamente."}, {"key": "flows.name", "value": "Nombre"}, {"key": "flows.updated", "value": "Actualizado"}, {"key": "flows.sorted_descending", "value": "Ordenado descendente"}, {"key": "flows.sorted_ascending", "value": "Ordenado ascendente"}, {"key": "flows.version", "value": "Versión"}, {"key": "flows.no_flows_yet", "value": "Aún no hay flujos"}, {"key": "flows.create_first_flow", "value": "Crea tu primer flujo para empezar a automatizar conversaciones."}, {"key": "flows.create_flow", "value": "<PERSON><PERSON><PERSON>"}, {"key": "common.language_selector.title", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"key": "common.language_selector.no_languages", "value": "No hay idiomas disponibles"}, {"key": "common.language_selector.changing", "value": "Cambiando idioma..."}, {"key": "admin.backup.title", "value": "Gestión de Copias de Seguridad"}, {"key": "admin.backup.loading", "value": "Cargando sistema de copias de seguridad..."}, {"key": "admin.backup.tabs.overview", "value": "Resumen"}, {"key": "admin.backup.tabs.backups", "value": "Copias de Seguridad"}, {"key": "admin.backup.tabs.schedules", "value": "<PERSON><PERSON><PERSON>"}, {"key": "admin.backup.tabs.settings", "value": "Configuración"}, {"key": "admin.backup.stats.total_backups", "value": "Copias de Seguridad Totales"}, {"key": "admin.backup.stats.total_size", "value": "Tamaño Total"}, {"key": "admin.backup.stats.local_backups", "value": "Copias de Seguridad Locales"}, {"key": "admin.backup.stats.cloud_backups", "value": "Copias de Seguridad en la Nube"}, {"key": "admin.backup.quick_actions.title", "value": "Acciones Rápidas"}, {"key": "admin.backup.quick_actions.description", "value": "Crea copias de seguridad manuales y administra tu sistema de copias de seguridad"}, {"key": "admin.backup.quick_actions.backup_description", "value": "Descripción de la Copia de Seguridad"}, {"key": "admin.backup.quick_actions.backup_description_placeholder", "value": "Introduce la descripción de la copia de seguridad..."}, {"key": "admin.backup.quick_actions.storage_locations", "value": "Ubicaciones de Almacenamiento"}, {"key": "admin.backup.storage.local", "value": "Almacenamiento Local"}, {"key": "admin.backup.storage.google_drive", "value": "Google Drive"}, {"key": "admin.backup.storage.drive", "value": "Drive"}, {"key": "admin.backup.storage.not_connected", "value": "(No Conectado)"}, {"key": "admin.backup.actions.create_backup", "value": "<PERSON><PERSON><PERSON> Seguridad"}, {"key": "admin.backup.history.title", "value": "Historial de Copias de Seguridad"}, {"key": "admin.backup.history.description", "value": "Ver y administrar todas las copias de seguridad de la base de datos"}, {"key": "admin.backup.table.filename", "value": "Nombre de Archivo"}, {"key": "admin.backup.table.type", "value": "Tipo"}, {"key": "admin.backup.table.size", "value": "<PERSON><PERSON><PERSON>"}, {"key": "admin.backup.table.status", "value": "Estado"}, {"key": "admin.backup.table.storage", "value": "Almacenamiento"}, {"key": "admin.backup.table.created", "value": "<PERSON><PERSON><PERSON>"}, {"key": "admin.backup.table.actions", "value": "Acciones"}, {"key": "admin.backup.status.creating", "value": "<PERSON><PERSON><PERSON>"}, {"key": "admin.backup.status.completed", "value": "Completado"}, {"key": "admin.backup.status.failed", "value": "Fallido"}, {"key": "admin.backup.status.uploading", "value": "Subiendo"}, {"key": "admin.backup.status.uploaded", "value": "Subido"}, {"key": "admin.backup.empty.title", "value": "No Se Encontraron Copias de Seguridad"}, {"key": "admin.backup.empty.description", "value": "Crea tu primera copia de seguridad para empezar"}, {"key": "admin.backup.messages.config_saved", "value": "Configuración de copia de seguridad guardada con éxito"}, {"key": "admin.backup.messages.backup_started", "value": "Creación de copia de seguridad iniciada con éxito"}, {"key": "admin.backup.schedules.title", "value": "Horarios de Copias de Seguridad"}, {"key": "admin.backup.schedules.description", "value": "Configura horarios automatizados de copias de seguridad"}, {"key": "admin.backup.schedules.new_schedule", "value": "Nuevo Horario"}, {"key": "admin.backup.schedules.active_title", "value": "Horarios Activos"}, {"key": "admin.backup.schedules.active_description", "value": "Administra tus horarios de copias de seguridad automatizadas"}, {"key": "admin.backup.schedules.table.name", "value": "Nombre del Horario"}, {"key": "admin.backup.schedules.table.frequency", "value": "Frecuencia"}, {"key": "admin.backup.schedules.table.time", "value": "<PERSON><PERSON>"}, {"key": "admin.backup.schedules.table.next_run", "value": "Próxima Ejecución"}, {"key": "admin.backup.schedules.table.storage", "value": "Almacenamiento"}, {"key": "admin.backup.schedules.table.status", "value": "Estado"}, {"key": "admin.backup.schedules.table.actions", "value": "Acciones"}, {"key": "admin.backup.schedules.status.enabled", "value": "Habilitado"}, {"key": "admin.backup.schedules.status.disabled", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"key": "admin.backup.schedules.empty.title", "value": "No Hay Horarios Configurados"}, {"key": "admin.backup.schedules.empty.description", "value": "Crea tu primer horario de copias de seguridad automatizadas para empezar"}, {"key": "admin.backup.schedules.actions.create", "value": "<PERSON><PERSON><PERSON>"}, {"key": "admin.backup.restore.title", "value": "Restauración de Base de Datos"}, {"key": "admin.backup.restore.warning_step", "value": "Revisa los detalles de la copia de seguridad y comprende las implicaciones"}, {"key": "admin.backup.restore.confirmation_step", "value": "Confirma tu intención de restaurar la base de datos"}, {"key": "admin.backup.restore.progress_step", "value": "Restauración de la base de datos en progreso"}, {"key": "admin.backup.restore.backup_info", "value": "Información de la Copia de Seguridad"}, {"key": "admin.backup.restore.filename", "value": "Nombre de Archivo"}, {"key": "admin.backup.restore.created", "value": "<PERSON><PERSON><PERSON>"}, {"key": "admin.backup.restore.size", "value": "<PERSON><PERSON><PERSON>"}, {"key": "admin.backup.restore.type", "value": "Tipo"}, {"key": "admin.backup.restore.description", "value": "Descripción"}, {"key": "admin.backup.restore.storage", "value": "Almacenamiento"}, {"key": "admin.backup.restore.warning_title", "value": "Advertencia Crítica"}, {"key": "admin.backup.restore.warning_replace", "value": "Esta acción reemplazará completamente tu base de datos actual"}, {"key": "admin.backup.restore.warning_data_lost", "value": "Todos los datos actuales se perderán permanentemente"}, {"key": "admin.backup.restore.warning_disconnect", "value": "Todos los usuarios serán desconectados durante la restauración"}, {"key": "admin.backup.restore.warning_undone", "value": "El proceso de restauración no se puede deshacer"}, {"key": "admin.backup.restore.warning_backup", "value": "Asegúrate de tener una copia de seguridad reciente del estado actual si es necesario"}, {"key": "admin.backup.restore.process_title", "value": "Proceso de Restauración"}, {"key": "admin.backup.restore.process_download", "value": "El archivo de copia de seguridad se descargará si se almacena en la nube"}, {"key": "admin.backup.restore.process_verify", "value": "Se verificará la integridad de la copia de seguridad"}, {"key": "admin.backup.restore.process_clean", "value": "Se limpiará la base de datos actual"}, {"key": "admin.backup.restore.process_restore", "value": "Se restaurarán los datos de la copia de seguridad"}, {"key": "admin.backup.restore.process_ready", "value": "El sistema estará listo para su uso"}, {"key": "admin.backup.restore.confirmation_title", "value": "Confirmación Final Requerida"}, {"key": "admin.backup.restore.confirmation_text", "value": "Para proceder con la restauración de la base de datos, por favor escriba el nombre del archivo de copia de seguridad exactamente como se muestra a continuación:"}, {"key": "admin.backup.restore.confirmation_label", "value": "Texto de Confirmación"}, {"key": "admin.backup.restore.confirmation_placeholder", "value": "Escribe \"{filename}\" para confirmar"}, {"key": "admin.backup.restore.confirmation_help", "value": "Esta confirmación asegura que comprende las consecuencias de esta acción."}, {"key": "admin.backup.restore.progress_title", "value": "Restauración en Progreso"}, {"key": "admin.backup.restore.progress_processing", "value": "Procesando..."}, {"key": "admin.backup.restore.completed_title", "value": "Restauración Completada"}, {"key": "admin.backup.restore.completed_message", "value": "La base de datos ha sido restaurada con éxito. El diálogo se cerrará automáticamente."}, {"key": "admin.backup.restore.failed_title", "value": "Restauración Fallida"}, {"key": "admin.backup.restore.cancel", "value": "<PERSON><PERSON><PERSON>"}, {"key": "admin.backup.restore.understand_continue", "value": "En<PERSON>do, Continuar"}, {"key": "admin.backup.restore.back", "value": "Atrás"}, {"key": "admin.backup.restore.restore_database", "value": "Restaurar Base de Datos"}, {"key": "admin.backup.restore.close", "value": "<PERSON><PERSON><PERSON>"}, {"key": "admin.backup.schedule_dialog.create_title", "value": "<PERSON><PERSON><PERSON> Copia de Seguridad"}, {"key": "admin.backup.schedule_dialog.edit_title", "value": "Editar Horario de Copia de Seguridad"}, {"key": "admin.backup.schedule_dialog.description", "value": "Configura los ajustes del horario de copias de seguridad automatizadas"}, {"key": "admin.backup.schedule_dialog.name_label", "value": "Nombre del Horario"}, {"key": "admin.backup.schedule_dialog.name_placeholder", "value": "ej., Copia Diaria, Archivo Semanal"}, {"key": "admin.backup.schedule_dialog.name_help", "value": "Un nombre descriptivo para este horario de copias de seguridad"}, {"key": "admin.backup.schedule_dialog.frequency_label", "value": "Frecuencia de Copia de Seguridad"}, {"key": "admin.backup.schedule_dialog.frequency_daily", "value": "Diariamente"}, {"key": "admin.backup.schedule_dialog.frequency_weekly", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "admin.backup.schedule_dialog.frequency_monthly", "value": "Mensualmente"}, {"key": "admin.backup.schedule_dialog.time_label", "value": "Hora de la Copia de Seguridad"}, {"key": "admin.backup.schedule_dialog.time_help", "value": "Hora en la que se ejecutará la copia de seguridad (formato 24 horas)"}, {"key": "admin.backup.schedule_dialog.day_of_week", "value": "Día de la Semana"}, {"key": "admin.backup.schedule_dialog.day_of_month", "value": "Día del Mes"}, {"key": "admin.backup.schedule_dialog.day_of_month_help", "value": "Día del mes en que se ejecutará la copia de seguridad (1-31)"}, {"key": "admin.backup.schedule_dialog.storage_label", "value": "Ubicaciones de Almacenamiento"}, {"key": "admin.backup.schedule_dialog.storage_help", "value": "Dónde almacenar los archivos de copia de seguridad"}, {"key": "admin.backup.schedule_dialog.enable_title", "value": "Habilitar <PERSON>"}, {"key": "admin.backup.schedule_dialog.enable_help", "value": "Inicia este horario inmediatamente después de la creación"}, {"key": "admin.backup.schedule_dialog.update_button", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "admin.backup.schedule_dialog.create_button", "value": "<PERSON><PERSON><PERSON>"}, {"key": "admin.backup.days.sunday", "value": "Domingo"}, {"key": "admin.backup.days.monday", "value": "<PERSON><PERSON>"}, {"key": "admin.backup.days.tuesday", "value": "<PERSON><PERSON>"}, {"key": "admin.backup.days.wednesday", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"key": "admin.backup.days.thursday", "value": "<PERSON><PERSON>"}, {"key": "admin.backup.days.friday", "value": "Viernes"}, {"key": "admin.backup.days.saturday", "value": "Sábado"}, {"key": "admin.backup.messages.schedule_created", "value": "<PERSON><PERSON><PERSON> creado con éxito"}, {"key": "admin.backup.messages.schedule_updated", "value": "Horario actualizado con éxito"}, {"key": "admin.backup.messages.schedule_deleted", "value": "<PERSON><PERSON>io eliminado con éxito"}, {"key": "admin.backup.messages.backup_deleted", "value": "Copia de seguridad eliminada con éxito"}, {"key": "admin.backup.messages.backup_valid", "value": "Copia de Seguridad Válida"}, {"key": "admin.backup.messages.backup_invalid", "value": "Copia de Seguridad Inválida"}, {"key": "admin.backup.messages.restore_successful", "value": "Restauración Exitosa"}, {"key": "admin.backup.messages.restore_failed", "value": "Fallo en la Restauración"}, {"key": "admin.backup.messages.restore_error", "value": "Error en la Restauración"}, {"key": "admin.backup.messages.settings_saved", "value": "Configuración guardada con éxito"}, {"key": "admin.backup.messages.connection_successful", "value": "Conexión Exitosa"}, {"key": "admin.backup.messages.connection_failed", "value": "Fallo en la Conexión"}, {"key": "admin.backup.actions.reset_defaults", "value": "Restablecer a Valores Predeterminados"}, {"key": "admin.backup.actions.save_settings", "value": "Guardar Configuración"}, {"key": "common.cancel", "value": "<PERSON><PERSON><PERSON>"}, {"key": "common.validation_error", "value": "Error de Validación"}, {"key": "common.file_size.bytes", "value": "Bytes"}, {"key": "common.file_size.kb", "value": "KB"}, {"key": "common.file_size.mb", "value": "MB"}, {"key": "common.file_size.gb", "value": "GB"}, {"key": "common.file_size.tb", "value": "TB"}, {"key": "admin.backup.messages.validation_successful", "value": "Validación Exitosa"}, {"key": "admin.backup.messages.validation_failed", "value": "Validación Fallida"}, {"key": "admin.backup.validation.schedule_name_required", "value": "Se requiere el nombre del horario"}, {"key": "admin.backup.validation.oauth_credentials_required", "value": "Se requieren ID de cliente y Secreto de Cliente"}, {"key": "admin.backup.default_description", "value": "Copia de seguridad manual"}, {"key": "admin.backup.restore.preparing", "value": "Preparando restauración..."}, {"key": "admin.backup.oauth.credentials_configured", "value": "Credenciales OAuth <PERSON>s"}, {"key": "admin.backup.oauth.client_id", "value": "ID de Cliente"}, {"key": "admin.backup.oauth.redirect_uri", "value": "URI de Redirección"}, {"key": "admin.backup.oauth.source", "value": "Fuente"}, {"key": "admin.backup.oauth.admin_interface", "value": "Interfaz de Administración"}, {"key": "admin.backup.oauth.environment_variables", "value": "Variables de Entorno"}, {"key": "admin.backup.oauth.configured", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "admin.backup.actions.download_backup", "value": "Descargar copia de seguridad"}, {"key": "admin.backup.actions.verify_backup", "value": "Verificar integridad de la copia de seguridad"}, {"key": "admin.backup.actions.restore_backup", "value": "Restaurar base de datos desde esta copia de seguridad"}, {"key": "admin.backup.actions.delete_backup", "value": "Eliminar copia de seguridad"}, {"key": "admin.backup.actions.edit_schedule", "value": "<PERSON><PERSON> ho<PERSON>"}, {"key": "admin.backup.actions.disable_schedule", "value": "Deshabilitar horario"}, {"key": "admin.backup.actions.enable_schedule", "value": "Habilitar horario"}, {"key": "admin.backup.actions.delete_schedule", "value": "Eliminar horario"}, {"key": "admin.backup.actions.upload_backup", "value": "Subir Copia de Seguridad"}, {"key": "admin.backup.upload.title", "value": "Subir Copia de Seguridad de Base de Datos"}, {"key": "admin.backup.upload.description", "value": "Sube un archivo de copia de seguridad de base de datos externo para añadirlo a tu colección de copias de seguridad. Formatos compatibles: .sql, .backup, .dump, .bak"}, {"key": "admin.backup.upload.file_label", "value": "Archivo de Copia de Seguridad"}, {"key": "admin.backup.upload.file_help", "value": "Tamaño máximo de archivo: 500MB. Formatos compatibles: .sql, .backup, .dump, .bak"}, {"key": "admin.backup.upload.description_label", "value": "Descripción (Opcional)"}, {"key": "admin.backup.upload.description_placeholder", "value": "Introduce una descripción para esta copia de seguridad..."}, {"key": "admin.backup.upload.storage_label", "value": "Ubicaciones de Almacenamiento"}, {"key": "admin.backup.upload.storage_help", "value": "Elige dónde almacenar la copia de seguridad subida"}, {"key": "admin.backup.upload.uploading", "value": "Subiendo..."}, {"key": "admin.backup.upload.upload_button", "value": "Subir Copia de Seguridad"}, {"key": "admin.backup.upload.no_file_selected", "value": "Por favor, selecciona un archivo de copia de seguridad para subir"}, {"key": "admin.backup.messages.backup_uploaded", "value": "Copia de seguridad subida con éxito"}, {"key": "admin.backup.dialogs.delete_backup_title", "value": "Eliminar Copia de Seguridad"}, {"key": "admin.backup.dialogs.delete_backup_description", "value": "¿Está seguro de que desea eliminar esta copia de seguridad? Esta acción no se puede deshacer."}, {"key": "admin.backup.dialogs.delete_schedule_title", "value": "Eliminar Horario"}, {"key": "admin.backup.dialogs.delete_schedule_description", "value": "¿Está seguro de que desea eliminar este horario de copias de seguridad? Esta acción no se puede deshacer."}, {"key": "admin.backup.settings.default_storage_locations", "value": "Ubicaciones de Almacenamiento Predeterminadas"}, {"key": "admin.backup.settings.default_storage_help", "value": "Ubicaciones de almacenamiento predeterminadas para nuevas copias de seguridad"}, {"key": "admin.backup.google_drive.title", "value": "Integración con Google Drive"}, {"key": "admin.backup.google_drive.description", "value": "Configura las credenciales de OAuth 2.0 y las opciones de copia de seguridad de almacenamiento en la nube"}, {"key": "admin.backup.google_drive.connection_title", "value": "Conexión con Google Drive"}, {"key": "admin.backup.google_drive.connection_description", "value": "Autoriza a {{appName}} a acceder a tu Google Drive para el almacenamiento de copias de seguridad."}, {"key": "admin.backup.google_drive.connect_drive", "value": "Conectar Google Drive"}, {"key": "admin.backup.google_drive.test_connection", "value": "Probar Conexión"}, {"key": "admin.backup.oauth.configuration_title", "value": "Configuración de OAuth 2.0"}, {"key": "admin.backup.oauth.configuration_description", "value": "Configura las credenciales de OAuth de Google para el acceso a Drive"}, {"key": "admin.backup.oauth.configured_status", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "admin.backup.oauth.not_configured_status", "value": "No Configurado"}, {"key": "admin.backup.oauth.validate_credentials", "value": "Validar Credenciales"}, {"key": "admin.backup.oauth.clear_credentials", "value": "Bo<PERSON>r <PERSON>"}, {"key": "admin.backup.oauth.setup_required", "value": "Configuración de OAuth Requerida"}, {"key": "admin.backup.oauth.setup_description", "value": "Para habilitar las copias de seguridad de Google Drive, necesitas configurar credenciales de OAuth 2.0 desde Google Cloud Console."}, {"key": "admin.backup.oauth.setup_instructions", "value": "Instrucciones de Configuración"}, {"key": "admin.backup.oauth.step_1", "value": "Ve a"}, {"key": "admin.backup.oauth.google_cloud_console", "value": "Google Cloud Console"}, {"key": "admin.backup.oauth.step_2", "value": "Crea un nuevo proyecto o selecciona uno existente"}, {"key": "admin.backup.oauth.step_3", "value": "Habilita la API de Google Drive"}, {"key": "admin.backup.oauth.step_4", "value": "Crea credenciales de OAuth 2.0 (Aplicación web)"}, {"key": "admin.backup.oauth.step_5", "value": "Añade tu URI de redirección a las URIs de redirección autorizadas"}, {"key": "admin.backup.oauth.step_6", "value": "Copia el ID de Cliente y el Secreto de Cliente"}, {"key": "admin.backup.oauth.configure_credentials", "value": "Configurar Credenciales OAuth"}, {"key": "admin.backup.oauth.client_id_label", "value": "ID de Cliente de Google"}, {"key": "admin.backup.oauth.client_id_placeholder", "value": "Introduce tu ID de Cliente OAuth de Google"}, {"key": "admin.backup.oauth.client_id_help", "value": "El ID de Cliente de las credenciales OAuth de tu Google Cloud Console"}, {"key": "admin.backup.oauth.client_secret_label", "value": "Secreto de Cliente de Google"}, {"key": "admin.backup.oauth.client_secret_placeholder", "value": "Introduce tu Secreto de Cliente OAuth de Google"}, {"key": "admin.backup.oauth.client_secret_help", "value": "El Secreto de Cliente de las credenciales OAuth de tu Google Cloud Console"}, {"key": "admin.backup.oauth.redirect_uri_label", "value": "URI de Redirección"}, {"key": "admin.backup.oauth.redirect_uri_help", "value": "Esta URI debe añadirse a tus URIs de redirección autorizadas de Google OAuth"}, {"key": "admin.backup.oauth.save_credentials", "value": "Guardar Credenciales"}, {"key": "admin.backup.dialogs.clear_oauth_title", "value": "Borrar Credenciales OAuth"}, {"key": "admin.backup.dialogs.clear_oauth_description", "value": "Esto eliminará todas las credenciales de Google OAuth almacenadas y deshabilitará la integración con Google Drive. Necesitarás reconfigurar las credenciales para usar las copias de seguridad de Google Drive."}, {"key": "common.delete", "value": "Eliminar"}, {"key": "admin.backup.storage.local", "value": "Almacenamiento Local"}, {"key": "admin.backup.storage.google_drive", "value": "Google Drive"}, {"key": "admin.backup.storage.not_connected", "value": "(No Conectado)"}, {"key": "admin.backup.oauth.credentials_form_title", "value": "Credenciales OAuth 2.0"}, {"key": "admin.backup.google_drive.enable_title", "value": "Habilitar Copias de Seguridad en Google Drive"}, {"key": "admin.backup.google_drive.enable_description", "value": "Almacena copias de seguridad en Google Drive para redundancia en la nube"}, {"key": "admin.backup.google_drive.connected_title", "value": "Google Drive Conectado"}, {"key": "admin.backup.google_drive.connected_description", "value": "Tu cuenta de Google Drive está conectada y lista para las copias de seguridad."}, {"key": "admin.backup.google_drive.oauth_required_title", "value": "Configuración OAuth Requerida"}, {"key": "admin.backup.google_drive.oauth_required_description", "value": "Por favor, configura las credenciales de OAuth 2.0 arriba antes de habilitar las copias de seguridad de Google Drive."}, {"key": "admin.backup.settings.general_title", "value": "Configuración General"}, {"key": "admin.backup.settings.general_description", "value": "Configura las preferencias globales del sistema de copias de seguridad"}, {"key": "admin.backup.settings.retention_label", "value": "Período de Retención de Copias de Seguridad (días)"}, {"key": "admin.backup.settings.retention_help", "value": "Las copias de seguridad más antiguas que esto se eliminarán automáticamente"}, {"key": "admin.backup.oauth.update_credentials", "value": "Act<PERSON><PERSON>r <PERSON>"}, {"key": "common.back", "value": "Atrás"}, {"key": "admin.company_deletion.title", "value": "Eliminar Empresa"}, {"key": "admin.company_deletion.messages.success_title", "value": "Empresa Eliminada"}, {"key": "admin.company_deletion.messages.error_title", "value": "Fallo en la Eliminación"}, {"key": "admin.company_deletion.steps.preview_description", "value": "Revisa lo que se eliminará permanentemente"}, {"key": "admin.company_deletion.steps.confirm_description", "value": "Confirma la eliminación escribiendo el nombre de la empresa"}, {"key": "admin.company_deletion.steps.final_description", "value": "Confirmación final - esta acción no se puede deshacer"}, {"key": "admin.company_deletion.data_types.users", "value": "Usuarios"}, {"key": "admin.company_deletion.data_types.conversations", "value": "Conversaciones"}, {"key": "admin.company_deletion.data_types.messages", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "admin.company_deletion.data_types.contacts", "value": "Contactos"}, {"key": "admin.company_deletion.data_types.flows", "value": "<PERSON><PERSON><PERSON>"}, {"key": "admin.company_deletion.data_types.deals", "value": "Oportunidades"}, {"key": "admin.company_deletion.data_types.payment_records", "value": "Registros de Pago"}, {"key": "admin.company_deletion.data_types.media_files", "value": "Archivos Multimedia"}, {"key": "admin.company_deletion.data_types.whatsapp_sessions", "value": "Sesiones de WhatsApp"}, {"key": "admin.company_deletion.preview.warning_title", "value": "Advertencia: Acción Irreversible"}, {"key": "admin.company_deletion.preview.warning_description", "value": "Esto eliminará permanentemente la empresa y TODOS los datos asociados. Esta acción no se puede deshacer."}, {"key": "admin.company_deletion.preview.data_title", "value": "Datos que se eliminarán permanentemente"}, {"key": "admin.company_deletion.preview.warnings_title", "value": "Advertencias Críticas"}, {"key": "admin.company_deletion.preview.load_error", "value": "Error al cargar la vista previa de eliminación"}, {"key": "admin.company_deletion.confirm.title", "value": "Escribe el Nombre de la Empresa para Continuar"}, {"key": "admin.company_deletion.confirm.description", "value": "Para confirmar la eliminación, escribe el nombre exacto de la empresa"}, {"key": "admin.company_deletion.confirm.label", "value": "Nombre de la Empresa"}, {"key": "admin.company_deletion.confirm.placeholder", "value": "Escribe \"{{name}}\" para confirmar"}, {"key": "admin.company_deletion.final.title", "value": "Confirmación Final"}, {"key": "admin.company_deletion.final.description", "value": "Estás a punto de eliminar permanentemente {{name}} y todos sus datos. Esta acción es irreversible y eliminará inmediatamente toda la información asociada."}, {"key": "admin.company_deletion.final.consequences_title", "value": "Qué sucede a continuación"}, {"key": "admin.company_deletion.final.consequence_users", "value": "Se eliminarán todas las cuentas de usuario"}, {"key": "admin.company_deletion.final.consequence_conversations", "value": "Se eliminarán todas las conversaciones y mensajes"}, {"key": "admin.company_deletion.final.consequence_contacts", "value": "Se eliminarán todos los contactos y sus datos"}, {"key": "admin.company_deletion.final.consequence_media", "value": "Se eliminarán permanentemente todos los archivos multimedia"}, {"key": "admin.company_deletion.final.consequence_whatsapp", "value": "Se terminarán todas las sesiones de WhatsApp"}, {"key": "admin.company_deletion.final.consequence_payments", "value": "Se eliminarán todos los registros de pago"}, {"key": "admin.company_deletion.final.consequence_company", "value": "La empresa se eliminará por completo del sistema"}, {"key": "admin.company_deletion.buttons.continue_to_confirmation", "value": "Continuar a la Confirmación"}, {"key": "admin.company_deletion.buttons.proceed_to_final", "value": "Continuar al Paso Final"}, {"key": "admin.company_deletion.buttons.deleting", "value": "Eliminando..."}, {"key": "admin.company_deletion.buttons.delete_permanently", "value": "Eliminar Empresa Permanentemente"}, {"key": "campaigns.builder.steps.basic", "value": "Información Básica"}, {"key": "campaigns.builder.steps.audience", "value": "Audiencia"}, {"key": "campaigns.builder.steps.content", "value": "Contenido"}, {"key": "campaigns.builder.steps.settings", "value": "Configuración"}, {"key": "campaigns.builder.steps.antiban", "value": "Anti-Baneo"}, {"key": "campaigns.builder.steps.review", "value": "Rev<PERSON><PERSON>"}, {"key": "campaigns.builder.messages.load_error", "value": "Error al cargar datos de la campaña"}, {"key": "campaigns.builder.messages.segment_created", "value": "Segmento \"{{name}}\" creado y seleccionado"}, {"key": "campaigns.builder.messages.template_created", "value": "Plant<PERSON> \"{{name}}\" creada y seleccionada"}, {"key": "campaigns.builder.messages.update_success", "value": "Campaña actualizada correctamente"}, {"key": "campaigns.builder.messages.save_success", "value": "Campaña guardada como borrador"}, {"key": "campaigns.builder.messages.update_error", "value": "Error al actualizar la campaña"}, {"key": "campaigns.builder.messages.save_error", "value": "Error al guardar la campaña"}, {"key": "campaigns.builder.messages.launch_success_title", "value": "¡Campaña Lanzada Exitosamente! 🚀"}, {"key": "campaigns.builder.messages.launch_success_description", "value": "\"{{name}}\" ahora está en ejecución y enviará mensajes a {{count}} contactos"}, {"key": "campaigns.builder.messages.launch_error_title", "value": "Fallo al Lanzar"}, {"key": "campaigns.builder.messages.launch_error_description", "value": "Fallo al lanzar la campaña. Por favor, inténtalo de nuevo."}, {"key": "campaigns.builder.validation.error_title", "value": "Error de Validación"}, {"key": "campaigns.builder.validation.name_required", "value": "Se requiere el nombre de la campaña"}, {"key": "campaigns.builder.validation.connection_required", "value": "Se requiere al menos una conexión de WhatsApp"}, {"key": "campaigns.builder.validation.segment_required", "value": "Se requiere un segmento de audiencia"}, {"key": "campaigns.builder.validation.content_required", "value": "Se requiere el contenido del mensaje"}, {"key": "campaigns.builder.validation.schedule_required", "value": "Se requiere fecha y hora programadas para campañas programadas"}, {"key": "campaigns.builder.basic.name_label", "value": "Nombre de la Campaña"}, {"key": "campaigns.builder.basic.name_placeholder", "value": "Introduce el nombre de la campaña"}, {"key": "campaigns.builder.basic.description_label", "value": "Descripción"}, {"key": "campaigns.builder.basic.description_placeholder", "value": "Describe tu campaña"}, {"key": "campaigns.builder.basic.connections_label", "value": "Conexiones de WhatsApp"}, {"key": "campaigns.builder.basic.connections_description", "value": "Selecciona múltiples cuentas de WhatsApp para una mejor distribución y protección anti-baneo"}, {"key": "campaigns.builder.basic.no_connections", "value": "No hay conexiones de WhatsApp disponibles"}, {"key": "campaigns.builder.basic.setup_connection", "value": "Por favor, configura una conexión de WhatsApp en Configuración > Conexiones de Canal primero"}, {"key": "campaigns.builder.basic.accounts_selected", "value": "cuenta(s) seleccionada(s) para distribución"}, {"key": "campaigns.builder.basic.select_all", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"key": "campaigns.builder.basic.clear_all", "value": "<PERSON><PERSON><PERSON>"}, {"key": "campaigns.builder.basic.type_label", "value": "Tipo de Campaña"}, {"key": "campaigns.builder.basic.type_immediate", "value": "Enviar Inmediatamente"}, {"key": "campaigns.builder.basic.type_scheduled", "value": "Programar para Más Tarde"}, {"key": "campaigns.builder.basic.type_drip", "value": "Campaña de Goteo"}, {"key": "campaigns.builder.basic.schedule_label", "value": "<PERSON><PERSON> y Hora Programadas"}, {"key": "campaigns.builder.audience.segment_label", "value": "Seleccionar Segmento de Audiencia"}, {"key": "campaigns.builder.audience.create_segment", "value": "Crear Nuevo Segmento"}, {"key": "campaigns.builder.audience.segment_placeholder", "value": "Elegir un segmento"}, {"key": "campaigns.builder.audience.contacts", "value": "contactos"}, {"key": "campaigns.builder.audience.will_receive", "value": "contactos recibirán esta campaña"}, {"key": "campaigns.builder.content.template_label", "value": "Usar Plantilla (Opcional)"}, {"key": "campaigns.builder.content.create_template", "value": "<PERSON><PERSON>r Nueva Plantilla"}, {"key": "campaigns.builder.content.template_placeholder", "value": "Elegir una plantilla"}, {"key": "campaigns.builder.content.message_label", "value": "Contenido del Mensaje"}, {"key": "campaigns.builder.content.message_placeholder", "value": "Introduce el contenido de tu mensaje. Haz clic en 'Insertar Variable' para agregar personalización..."}, {"key": "campaigns.builder.content.validation_title", "value": "Validación de Contenido"}, {"key": "campaigns.builder.content.validation_score", "value": "Puntuación"}, {"key": "campaigns.builder.content.validation_issues", "value": "Problemas"}, {"key": "campaigns.builder.header.edit", "value": "<PERSON><PERSON>"}, {"key": "campaigns.builder.header.create", "value": "<PERSON><PERSON><PERSON>"}, {"key": "campaigns.builder.header.step_progress", "value": "Paso {{current}} de {{total}}: {{title}}"}, {"key": "campaigns.builder.navigation.previous", "value": "Anterior"}, {"key": "campaigns.builder.navigation.next", "value": "Siguient<PERSON>"}, {"key": "campaigns.builder.navigation.save_draft", "value": "<PERSON><PERSON>"}, {"key": "campaigns.builder.navigation.launch", "value": "Lanzar <PERSON>"}, {"key": "campaigns.builder.navigation.launching", "value": "Lanzando..."}, {"key": "campaigns.builder.launch.confirmation_title", "value": "Confirmación de Lanzamiento de Campaña"}, {"key": "campaigns.builder.launch.confirmation_description", "value": "¿Está seguro de que desea lanzar esta campaña? Esta acción no se puede deshacer."}, {"key": "campaigns.builder.launch.summary_title", "value": "Resumen de la Campaña"}, {"key": "campaigns.builder.launch.summary_name", "value": "Nombre"}, {"key": "campaigns.builder.launch.summary_type", "value": "Tipo"}, {"key": "campaigns.builder.launch.summary_recipients", "value": "Des<PERSON><PERSON><PERSON>"}, {"key": "campaigns.builder.launch.summary_scheduled", "value": "Programado para"}, {"key": "campaigns.builder.launch.warning_title", "value": "Importante"}, {"key": "campaigns.builder.launch.warning_description", "value": "Una vez lanzada, esta campaña comenzará a enviar mensajes inmediatamente y no se podrá detener."}, {"key": "campaigns.builder.launch.confirm_button", "value": "Sí, Lanzar"}, {"key": "campaigns.builder.settings.rate_limiting_title", "value": "Configuración de Límites de Tasa y Anti-Baneo"}, {"key": "campaigns.builder.settings.messages_per_minute", "value": "Mensajes por <PERSON>"}, {"key": "campaigns.builder.settings.messages_per_hour", "value": "Mensajes por <PERSON>"}, {"key": "campaigns.builder.settings.delay_between_messages", "value": "Retraso Entre Mensajes (segundos)"}, {"key": "campaigns.builder.antiban.title", "value": "Protección Anti-Baneo"}, {"key": "campaigns.builder.antiban.enable_label", "value": "Habilitar Protección Anti-Baneo"}, {"key": "campaigns.builder.antiban.enable_description", "value": "Aplica automáticamente límites de tasa inteligentes y rotación de cuentas"}, {"key": "campaigns.builder.antiban.mode_label", "value": "Modo de Protección"}, {"key": "campaigns.builder.antiban.mode_description", "value": "Elige qué tan agresiva debe ser la protección anti-baneo"}, {"key": "campaigns.builder.antiban.mode_conservative", "value": "Conservador"}, {"key": "campaigns.builder.antiban.mode_conservative_desc", "value": "<PERSON><PERSON> lento, más seguro"}, {"key": "campaigns.builder.antiban.mode_moderate", "value": "Moderado"}, {"key": "campaigns.builder.antiban.mode_moderate_desc", "value": "Enfoque equilibrado"}, {"key": "campaigns.builder.antiban.mode_aggressive", "value": "Agresivo"}, {"key": "campaigns.builder.antiban.mode_aggressive_desc", "value": "<PERSON><PERSON>, mayor r<PERSON><PERSON>"}, {"key": "campaigns.builder.antiban.business_hours_label", "value": "Solo Horario Laboral"}, {"key": "campaigns.builder.antiban.business_hours_desc", "value": "Enviar solo durante horario de 9 AM a 6 PM"}, {"key": "campaigns.builder.antiban.respect_weekends_label", "value": "Respetar <PERSON>s de Semana"}, {"key": "campaigns.builder.antiban.respect_weekends_desc", "value": "Pausar los fines de semana"}, {"key": "campaigns.builder.antiban.randomize_delays_label", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "campaigns.builder.antiban.randomize_delays_desc", "value": "Añade variaciones similares a las humanas al tiempo de los mensajes"}, {"key": "campaigns.builder.antiban.min_delay_label", "value": "<PERSON><PERSON><PERSON> (segundos)"}, {"key": "campaigns.builder.antiban.max_delay_label", "value": "<PERSON><PERSON><PERSON> (segundos)"}, {"key": "campaigns.builder.antiban.account_rotation_label", "value": "Rotación Inteligente de Cuentas"}, {"key": "campaigns.builder.antiban.account_rotation_desc", "value": "Distribuye mensajes entre las cuentas seleccionadas"}, {"key": "campaigns.builder.antiban.cooldown_period_label", "value": "Período de Enfriamiento de Cuenta (minutos)"}, {"key": "campaigns.builder.antiban.cooldown_period_desc", "value": "Tiempo de descanso entre envíos de alto volumen para cada cuenta"}, {"key": "campaigns.builder.antiban.message_variation_label", "value": "Variación de Mensaje"}, {"key": "campaigns.builder.antiban.message_variation_desc", "value": "Añade ligeras variaciones para evitar contenido idéntico"}, {"key": "campaigns.builder.antiban.account_health_title", "value": "Estado de Salud de la Cuenta"}, {"key": "campaigns.builder.antiban.last_active", "value": "Última actividad"}, {"key": "campaigns.builder.antiban.account_status_healthy", "value": "Saludable"}, {"key": "campaigns.builder.review.campaign_summary_title", "value": "Resumen de la Campaña"}, {"key": "campaigns.builder.review.basic_info_title", "value": "Información Básica"}, {"key": "campaigns.builder.review.name_label", "value": "Nombre"}, {"key": "campaigns.builder.review.description_label", "value": "Descripción"}, {"key": "campaigns.builder.review.channel_label", "value": "Canal"}, {"key": "campaigns.builder.review.type_label", "value": "Tipo"}, {"key": "campaigns.builder.review.scheduled_for_label", "value": "Programado para"}, {"key": "campaigns.builder.review.whatsapp_accounts_title", "value": "Cuentas de WhatsApp"}, {"key": "campaigns.builder.review.accounts_selected_for_distribution", "value": "cuenta(s) seleccionada(s) para distribución"}, {"key": "campaigns.builder.review.single_account_label", "value": "Cuenta Única"}, {"key": "campaigns.builder.review.no_account_selected", "value": "Ninguna cuenta de WhatsApp seleccionada"}, {"key": "campaigns.builder.review.audience_title", "value": "Audiencia"}, {"key": "campaigns.builder.review.segment_label", "value": "Segmento"}, {"key": "campaigns.builder.review.no_segment_selected", "value": "Ningún segmento seleccionado"}, {"key": "campaigns.builder.review.antiban_title", "value": "Protección Anti-Baneo"}, {"key": "campaigns.builder.review.antiban_mode_label", "value": "Modo"}, {"key": "campaigns.builder.review.random_delays_badge", "value": "<PERSON><PERSON><PERSON>"}, {"key": "campaigns.builder.review.account_rotation_badge", "value": "Rotación de Cuentas"}, {"key": "campaigns.builder.review.antiban_disabled", "value": "Protección anti-baneo deshabilitada"}, {"key": "campaigns.builder.review.rate_limiting_title", "value": "Límites de Tasa"}, {"key": "campaigns.builder.review.per_minute_label", "value": "<PERSON><PERSON>"}, {"key": "campaigns.builder.review.per_hour_label", "value": "<PERSON><PERSON>"}, {"key": "campaigns.builder.review.per_day_label", "value": "Por D<PERSON>"}, {"key": "campaigns.builder.review.base_delay_label", "value": "Retraso Base"}, {"key": "campaigns.builder.review.messages_unit", "value": "mensa<PERSON><PERSON>"}, {"key": "campaigns.builder.review.seconds_unit", "value": "segundos"}, {"key": "campaigns.builder.review.message_content_title", "value": "Contenido del Mensaje"}, {"key": "campaigns.builder.review.media_attachments_label", "value": "Archivos Multimedia Adjuntos"}, {"key": "campaigns.builder.review.media_item_label", "value": "Multimedia"}, {"key": "campaigns.details.title", "value": "Detalles de la Campaña"}, {"key": "campaigns.details.fetch_failed", "value": "Error al obtener detalles de la campaña"}, {"key": "campaigns.details.export_failed", "value": "Error al exportar"}, {"key": "campaigns.details.export_success", "value": "Los datos de la campaña se exportaron a Excel"}, {"key": "campaigns.details.export_excel", "value": "Excel"}, {"key": "campaigns.details.search_placeholder", "value": "Busca por nombre de contacto o número de teléfono..."}, {"key": "campaigns.details.filter_by_status", "value": "Filtrar por estado"}, {"key": "campaigns.details.all_statuses", "value": "Todos los Estados"}, {"key": "campaigns.details.status.pending", "value": "Pendiente"}, {"key": "campaigns.details.status.sent", "value": "Enviado"}, {"key": "campaigns.details.status.delivered", "value": "<PERSON><PERSON><PERSON>"}, {"key": "campaigns.details.status.failed", "value": "Fallido"}, {"key": "campaigns.details.status.cancelled", "value": "Cancelado"}, {"key": "campaigns.details.no_data", "value": "No se encontraron datos de la campaña"}, {"key": "campaigns.details.no_results", "value": "No hay resultados que coincidan con tu criterio de búsqueda"}, {"key": "campaigns.details.table.contact_name", "value": "Nombre del Contacto"}, {"key": "campaigns.details.table.phone_number", "value": "Número de Teléfono"}, {"key": "campaigns.details.table.whatsapp_account", "value": "Cuenta de WhatsApp"}, {"key": "campaigns.details.table.status", "value": "Estado"}, {"key": "campaigns.details.table.sent_at", "value": "Enviado a las"}, {"key": "campaigns.details.table.message_content", "value": "Contenido del Mensaje"}, {"key": "campaigns.details.table.error", "value": "Error"}, {"key": "campaigns.details.pagination.showing", "value": "Mostrando {{start}} a {{end}} de {{total}} resultados"}, {"key": "campaigns.details.pagination.previous", "value": "Anterior"}, {"key": "campaigns.details.pagination.next", "value": "Siguient<PERSON>"}, {"key": "campaigns.details.pagination.page_info", "value": "Página {{current}} de {{total}}"}, {"key": "segments.create.title", "value": "<PERSON><PERSON><PERSON>"}, {"key": "segments.create.name_label", "value": "Nombre del Segmento"}, {"key": "segments.create.name_placeholder", "value": "ej., Clientes VIP, Nuevos Clientes Potenciales"}, {"key": "segments.create.description_label", "value": "Descripción (Opcional)"}, {"key": "segments.create.description_placeholder", "value": "Describe este segmento..."}, {"key": "segments.create.filter_criteria_title", "value": "Criterios de Filtrado"}, {"key": "segments.create.contact_tags_label", "value": "Etiquetas de Contacto"}, {"key": "segments.create.tag_placeholder", "value": "Introduce nombre de etiqueta"}, {"key": "segments.create.tags_description", "value": "Los contactos deben tener TODAS las etiquetas seleccionadas"}, {"key": "segments.create.created_after_label", "value": "<PERSON><PERSON><PERSON>"}, {"key": "segments.create.created_before_label", "value": "<PERSON><PERSON><PERSON>"}, {"key": "segments.create.contact_preview_title", "value": "Vista Previa de Contactos"}, {"key": "segments.create.showing_first_50", "value": "Mostrando los primeros 50 de"}, {"key": "segments.create.contacts", "value": "contactos"}, {"key": "segments.create.contacts_match_criteria", "value": "contactos coinciden con estos criterios"}, {"key": "segments.create.excluded", "value": "excluidos"}, {"key": "segments.create.loading_preview", "value": "Cargando vista previa de contactos..."}, {"key": "segments.create.table.contact_name", "value": "Nombre del Contacto"}, {"key": "segments.create.table.phone", "value": "Teléfono"}, {"key": "segments.create.table.email", "value": "Correo Electrónico"}, {"key": "segments.create.table.tags", "value": "Etiquetas"}, {"key": "segments.create.table.created", "value": "<PERSON><PERSON><PERSON>"}, {"key": "segments.create.table.last_activity", "value": "Última Actividad"}, {"key": "segments.create.table.actions", "value": "Acciones"}, {"key": "segments.create.table.unknown", "value": "Desconocido"}, {"key": "segments.create.table.no_activity", "value": "Sin actividad"}, {"key": "segments.create.exclude_contact_tooltip", "value": "Excluir contacto del segmento"}, {"key": "segments.create.all_contacts_excluded", "value": "Todos los contactos han sido excluidos de este segmento"}, {"key": "segments.create.restore_all_contacts", "value": "Restaurar todos los contactos"}, {"key": "segments.create.no_contacts_match", "value": "No hay contactos que coincidan con los criterios actuales"}, {"key": "segments.create.add_filter_criteria", "value": "Agrega criterios de filtro para previsualizar contactos"}, {"key": "segments.create.select_tags_or_dates", "value": "Selecciona etiquetas o rangos de fechas para ver contactos coincidentes"}, {"key": "segments.create.excluded_contacts_title", "value": "Contactos Excluidos"}, {"key": "segments.create.restore_all", "value": "<PERSON><PERSON><PERSON>"}, {"key": "segments.create.restore_contact_tooltip", "value": "Restaurar contacto"}, {"key": "segments.create.create_button", "value": "<PERSON><PERSON><PERSON>"}, {"key": "segments.create.contact_excluded_title", "value": "Contacto excluido"}, {"key": "segments.create.contact_excluded_desc", "value": "El contacto ha sido eliminado de esta vista previa del segmento"}, {"key": "segments.create.contact_restored_title", "value": "<PERSON><PERSON> restaurado"}, {"key": "segments.create.contact_restored_desc", "value": "El contacto ha sido agregado de nuevo a la vista previa del segmento"}, {"key": "segments.create.name_required", "value": "Por favor, introduce un nombre para el segmento"}, {"key": "segments.create.criteria_required", "value": "Por favor, agrega al menos un criterio de filtro"}, {"key": "segments.create.success", "value": "Segmento creado con éxito"}, {"key": "segments.create.failed", "value": "Error al crear segmento"}, {"key": "segments.edit.title", "value": "Editar Segmento de Contactos"}, {"key": "segments.edit.loading_segment", "value": "Cargando segmento..."}, {"key": "segments.edit.load_failed", "value": "Error al cargar segmento"}, {"key": "segments.edit.name_label", "value": "Nombre del Segmento"}, {"key": "segments.edit.name_placeholder", "value": "ej., Clientes VIP, Nuevos Clientes Potenciales"}, {"key": "segments.edit.description_label", "value": "Descripción (Opcional)"}, {"key": "segments.edit.description_placeholder", "value": "Describe este segmento..."}, {"key": "segments.edit.filter_criteria_title", "value": "Criterios de Filtrado"}, {"key": "segments.edit.contact_tags_label", "value": "Etiquetas de Contacto"}, {"key": "segments.edit.tag_placeholder", "value": "Agregar una etiqueta..."}, {"key": "segments.edit.created_after_label", "value": "<PERSON><PERSON><PERSON>"}, {"key": "segments.edit.created_before_label", "value": "<PERSON><PERSON><PERSON>"}, {"key": "segments.edit.contact_preview_title", "value": "Vista Previa de Contactos"}, {"key": "segments.edit.contact_excluded_title", "value": "Contacto excluido"}, {"key": "segments.edit.contact_excluded_desc", "value": "El contacto ha sido eliminado de esta vista previa del segmento"}, {"key": "segments.edit.contact_restored_title", "value": "<PERSON><PERSON> restaurado"}, {"key": "segments.edit.contact_restored_desc", "value": "El contacto ha sido agregado de nuevo a la vista previa del segmento"}, {"key": "segments.edit.validation_error", "value": "Error de Validación"}, {"key": "segments.edit.name_required", "value": "Se requiere el nombre del segmento"}, {"key": "segments.edit.update_success", "value": "Segmento actualizado con éxito"}, {"key": "segments.edit.update_failed", "value": "Error al actualizar segmento"}, {"key": "segments.edit.delete_success", "value": "Segmento eliminado con éxito"}, {"key": "segments.edit.delete_failed", "value": "Error al eliminar segmento"}, {"key": "segments.edit.delete_button", "value": "Eliminar Segmento"}, {"key": "segments.edit.updating", "value": "Actualizando..."}, {"key": "segments.edit.update_button", "value": "Actualizar <PERSON>"}, {"key": "segments.edit.delete_confirm_title", "value": "Eliminar Segmento"}, {"key": "segments.edit.delete_confirm_message", "value": "¿Está seguro de que desea eliminar este segmento? Esta acción no se puede deshacer."}, {"key": "segments.edit.delete_confirm_button", "value": "Eliminar Segmento"}, {"key": "segments.edit.contacts", "value": "contactos"}, {"key": "segments.edit.showing_first_50", "value": "Mostrando los primeros 50 de"}, {"key": "segments.edit.contacts_match_criteria", "value": "contactos coinciden con estos criterios"}, {"key": "segments.edit.excluded", "value": "excluidos"}, {"key": "segments.edit.loading_preview", "value": "Cargando vista previa de contactos..."}, {"key": "segments.edit.table.contact_name", "value": "Nombre del Contacto"}, {"key": "segments.edit.table.phone", "value": "Teléfono"}, {"key": "segments.edit.table.email", "value": "Correo Electrónico"}, {"key": "segments.edit.table.tags", "value": "Etiquetas"}, {"key": "segments.edit.table.created", "value": "<PERSON><PERSON><PERSON>"}, {"key": "segments.edit.table.last_activity", "value": "Última Actividad"}, {"key": "segments.edit.table.actions", "value": "Acciones"}, {"key": "segments.edit.table.unknown", "value": "Desconocido"}, {"key": "segments.edit.table.no_activity", "value": "Sin actividad"}, {"key": "segments.edit.exclude_contact_tooltip", "value": "Excluir contacto del segmento"}, {"key": "segments.edit.all_contacts_excluded", "value": "Todos los contactos han sido excluidos de este segmento"}, {"key": "segments.edit.restore_all_contacts", "value": "Restaurar todos los contactos"}, {"key": "segments.edit.no_contacts_match", "value": "No hay contactos que coincidan con los criterios actuales"}, {"key": "segments.edit.try_adjusting_filters", "value": "Intenta ajustar tus filtros"}, {"key": "segments.edit.add_filter_criteria", "value": "Agrega criterios de filtro para previsualizar contactos"}, {"key": "segments.edit.select_tags_or_dates", "value": "Selecciona etiquetas o rangos de fechas para ver contactos coincidentes"}, {"key": "segments.edit.excluded_contacts_title", "value": "Contactos Excluidos"}, {"key": "segments.edit.restore_all", "value": "<PERSON><PERSON><PERSON>"}, {"key": "segments.edit.restore_contact_tooltip", "value": "Restaurar contacto"}, {"key": "templates.create.title", "value": "<PERSON><PERSON><PERSON>"}, {"key": "templates.create.name_label", "value": "Nombre de la Plantilla"}, {"key": "templates.create.name_placeholder", "value": "ej., Mensaje de Bienvenida"}, {"key": "templates.create.category_label", "value": "Categoría"}, {"key": "templates.create.description_label", "value": "Descripción (Opcional)"}, {"key": "templates.create.description_placeholder", "value": "Describe esta plantilla..."}, {"key": "templates.create.content_label", "value": "Contenido de la Plantilla"}, {"key": "templates.create.content_placeholder", "value": "Introduce el contenido de tu plantilla. Haz clic en 'Insertar Variable' para agregar personalización..."}, {"key": "templates.create.media_files_label", "value": "Archivos Multimedia (Opcional)"}, {"key": "templates.create.add_media", "value": "Agregar Multimedia"}, {"key": "templates.create.supported_files", "value": "Soportados: Imágenes (JPEG, PNG, WebP), Videos (MP4, 3GP), Audio (MP3, AAC, OGG), Documentos (PDF, DOC, DOCX). Máximo 10MB por archivo."}, {"key": "templates.create.create_button", "value": "<PERSON><PERSON><PERSON>"}, {"key": "templates.create.file_size_error", "value": "El tamaño del archivo debe ser inferior a 10MB"}, {"key": "templates.create.image_type_error", "value": "Solo se permiten imágenes JPEG, PNG y WebP"}, {"key": "templates.create.video_type_error", "value": "Solo se permiten videos MP4 y 3GP"}, {"key": "templates.create.audio_type_error", "value": "Solo se permiten archivos de audio MP3, AAC y OGG"}, {"key": "templates.create.document_type_error", "value": "Solo se permiten documentos PDF, DOC y DOCX"}, {"key": "templates.create.invalid_file", "value": "Archivo Inválido"}, {"key": "templates.create.upload_failed", "value": "Fallo al subir"}, {"key": "templates.create.upload_file_failed", "value": "Error al subir {{filename}}"}, {"key": "templates.create.name_required", "value": "Por favor, introduce un nombre para la plantilla"}, {"key": "templates.create.content_required", "value": "Por favor, introduce el contenido de la plantilla"}, {"key": "templates.create.success", "value": "Plantilla creada con éxito"}, {"key": "templates.create.failed", "value": "Error al crear plantilla"}, {"key": "templates.edit.title", "value": "<PERSON><PERSON>"}, {"key": "templates.edit.loading_template", "value": "Cargando plantilla..."}, {"key": "templates.edit.load_failed", "value": "Error al cargar plantilla"}, {"key": "templates.edit.name_label", "value": "Nombre de la Plantilla"}, {"key": "templates.edit.name_placeholder", "value": "ej., Mensaje de Bienvenida, Alerta de Promoción"}, {"key": "templates.edit.description_label", "value": "Descripción (Opcional)"}, {"key": "templates.edit.description_placeholder", "value": "Describe esta plantilla..."}, {"key": "templates.edit.category_label", "value": "Categoría"}, {"key": "templates.edit.category.general", "value": "General"}, {"key": "templates.edit.category.marketing", "value": "Marketing"}, {"key": "templates.edit.category.support", "value": "Soporte"}, {"key": "templates.edit.category.notification", "value": "Notificación"}, {"key": "templates.edit.category.welcome", "value": "Bienvenida"}, {"key": "templates.edit.content_label", "value": "Contenido del Mensaje"}, {"key": "templates.edit.content_placeholder", "value": "Introduce aquí el contenido de tu mensaje... Haz clic en 'Insertar Variable' para agregar personalización..."}, {"key": "templates.edit.attached_media", "value": "Multimedia Adjunto"}, {"key": "templates.edit.validation_error", "value": "Error de Validación"}, {"key": "templates.edit.name_content_required", "value": "Se requiere nombre y contenido"}, {"key": "templates.edit.update_success", "value": "Plantilla actualizada con éxito"}, {"key": "templates.edit.update_failed", "value": "Error al actualizar plantilla"}, {"key": "templates.edit.delete_success", "value": "Plantilla eliminada con éxito"}, {"key": "templates.edit.delete_failed", "value": "Error al eliminar plantilla"}, {"key": "templates.edit.delete_button", "value": "Eliminar Plantilla"}, {"key": "templates.edit.updating", "value": "Actualizando..."}, {"key": "templates.edit.update_button", "value": "Actualizar <PERSON>illa"}, {"key": "templates.edit.delete_confirm_title", "value": "Eliminar Plantilla"}, {"key": "templates.edit.delete_confirm_message", "value": "¿Está seguro de que desea eliminar esta plantilla? Esta acción no se puede deshacer."}, {"key": "templates.edit.delete_confirm_button", "value": "Eliminar Plantilla"}, {"key": "variables.contact_name", "value": "Nombre del Contacto"}, {"key": "variables.contact_name_desc", "value": "Nombre completo del contacto"}, {"key": "variables.phone_number", "value": "Número de Teléfono"}, {"key": "variables.phone_number_desc", "value": "Número de teléfono del contacto"}, {"key": "variables.email_address", "value": "Dirección de Correo Electrónico"}, {"key": "variables.email_address_desc", "value": "Dirección de correo electrónico del contacto"}, {"key": "variables.company_name", "value": "Nombre de la Empresa"}, {"key": "variables.company_name_desc", "value": "Empresa u organización del contacto"}, {"key": "variables.current_date", "value": "Fecha Actual"}, {"key": "variables.current_date_desc", "value": "<PERSON><PERSON>"}, {"key": "variables.current_time", "value": "Hora Actual"}, {"key": "variables.current_time_desc", "value": "<PERSON><PERSON> actual"}, {"key": "variables.custom_variable_desc", "value": "Variable personalizada: {{variable}}"}, {"key": "variables.category.contact", "value": "Información de Contacto"}, {"key": "variables.category.custom", "value": "Variables Personalizadas"}, {"key": "variables.category.system", "value": "Variables del Sistema"}, {"key": "variables.category.other", "value": "<PERSON><PERSON>"}, {"key": "variables.insert_variable", "value": "Insertar Variable"}, {"key": "variables.search_placeholder", "value": "Buscar variables..."}, {"key": "variables.no_variables_found", "value": "No se encontraron variables."}, {"key": "variables.help_text", "value": "Usa variables como"}, {"key": "variables.for_personalization", "value": "para personalización"}, {"key": "contacts.avatar.refresh_tooltip", "value": "Actualizar foto de perfil de WhatsApp"}, {"key": "contacts.edit.title", "value": "<PERSON><PERSON>"}, {"key": "contacts.edit.description", "value": "Realiza cambios en la información del contacto a continuación."}, {"key": "contacts.edit.contact_id_missing", "value": "Falta el ID del contacto"}, {"key": "contacts.edit.update_failed", "value": "Error al actualizar contacto"}, {"key": "contacts.edit.success_title", "value": "<PERSON>o <PERSON>"}, {"key": "contacts.edit.success_description", "value": "El contacto se ha actualizado correctamente."}, {"key": "contacts.edit.error_title", "value": "Fallo en la actualización"}, {"key": "contacts.edit.name_label", "value": "Nombre"}, {"key": "contacts.edit.email_label", "value": "Correo Electrónico"}, {"key": "contacts.edit.phone_label", "value": "Teléfono"}, {"key": "contacts.edit.company_label", "value": "Empresa"}, {"key": "contacts.edit.channel_label", "value": "Canal"}, {"key": "contacts.edit.select_channel_placeholder", "value": "Selecciona canal"}, {"key": "contacts.edit.channel.whatsapp_official", "value": "WhatsApp Oficial"}, {"key": "contacts.edit.channel.whatsapp_unofficial", "value": "WhatsApp No Oficial"}, {"key": "contacts.edit.channel.messenger", "value": "Facebook Messenger"}, {"key": "contacts.edit.channel.instagram", "value": "Instagram"}, {"key": "contacts.edit.channel_identifier_label", "value": "Identificador de Canal"}, {"key": "contacts.edit.channel_identifier_placeholder", "value": "Número de teléfono o ID"}, {"key": "contacts.edit.tags_label", "value": "Etiquetas (separadas por comas)"}, {"key": "contacts.edit.tags_placeholder", "value": "cliente potencial, cliente, etc."}, {"key": "contacts.edit.notes_label", "value": "Notas"}, {"key": "contacts.edit.saving", "value": "Guardando..."}, {"key": "contacts.edit.save_changes", "value": "Guardar Cambios"}, {"key": "agents.fetch_failed", "value": "Error al obtener agentes: {{status}} {{error}}"}, {"key": "agents.assign_failed", "value": "Error al asignar conversación"}, {"key": "agents.unassign_failed", "value": "Error al desasignar conversación"}, {"key": "agents.assigned_title", "value": "Conversación Asignada"}, {"key": "agents.unassigned_title", "value": "Conversación Desasignada"}, {"key": "agents.assigned_description", "value": "Conversación asignada a {{agentName}}"}, {"key": "agents.unassigned_description", "value": "La conversación ha sido desasignada"}, {"key": "agents.assignment_failed_title", "value": "Fallo en la Asignación"}, {"key": "agents.assignment_failed_description", "value": "Error al actualizar la asignación"}, {"key": "agents.reassign_conversation", "value": "Reasignar Conversación"}, {"key": "agents.assign_conversation", "value": "Asignar Conversación"}, {"key": "agents.loading_agents", "value": "Cargando agentes..."}, {"key": "agents.error_loading_agents", "value": "Error al cargar agentes"}, {"key": "agents.no_agents_available", "value": "No hay agentes disponibles"}, {"key": "agents.unassign", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "agents.unassigned", "value": "No asignado"}, {"key": "agents.assign", "value": "<PERSON><PERSON><PERSON>"}, {"key": "contacts.details.contact_updated_title", "value": "<PERSON>o <PERSON>"}, {"key": "contacts.details.contact_updated_description", "value": "La información del contacto se ha actualizado."}, {"key": "contacts.details.fetch_notes_failed", "value": "Error al obtener notas"}, {"key": "contacts.details.save_note_failed", "value": "Error al guardar nota"}, {"key": "contacts.details.note_saved_successfully", "value": "Nota guardada correctamente"}, {"key": "contacts.details.channel.whatsapp", "value": "WhatsApp"}, {"key": "contacts.details.channel.whatsapp_business", "value": "WhatsApp Business"}, {"key": "contacts.details.channel.whatsapp_unofficial", "value": "WhatsApp (No Oficial)"}, {"key": "contacts.details.channel.messenger", "value": "<PERSON>"}, {"key": "contacts.details.channel.instagram", "value": "Instagram"}, {"key": "contacts.details.channel.chat", "value": "Cha<PERSON>"}, {"key": "contacts.details.unknown", "value": "Desconocido"}, {"key": "contacts.details.close_details", "value": "Ce<PERSON>r de<PERSON> del contacto"}, {"key": "contacts.details.show_details", "value": "Mostrar detalles del contacto"}, {"key": "contacts.details.title", "value": "Detalles del Contacto"}, {"key": "contacts.details.contact_information", "value": "Información del Contacto"}, {"key": "contacts.details.full_name", "value": "Nombre Completo"}, {"key": "contacts.details.phone", "value": "Teléfono"}, {"key": "contacts.details.email", "value": "Correo Electrónico"}, {"key": "contacts.details.company", "value": "Empresa"}, {"key": "contacts.details.not_provided", "value": "No proporcionado"}, {"key": "contacts.details.edit_details", "value": "<PERSON><PERSON>"}, {"key": "contacts.details.tags", "value": "Etiquetas"}, {"key": "contacts.details.no_tags_added", "value": "No hay etiquetas agregadas"}, {"key": "contacts.details.conversation_details", "value": "Detalles de la Conversación"}, {"key": "contacts.details.first_contacted", "value": "Primer contacto"}, {"key": "contacts.details.channel", "value": "Canal"}, {"key": "messages.input.microphone_access_error", "value": "No se pudo acceder al micrófono. Por favor, comprueba los permisos."}, {"key": "messages.input.microphone_denied", "value": "Acceso al micrófono denegado. Por favor, permite el acceso al micrófono e inténtalo de nuevo."}, {"key": "messages.input.microphone_not_found", "value": "No se encontró micrófono. Por favor, conecta un micrófono e inténtalo de nuevo."}, {"key": "messages.input.recording_error", "value": "Error de Grabación"}, {"key": "messages.input.no_recording", "value": "No hay grabación para enviar"}, {"key": "messages.input.recording_too_short", "value": "La grabación es demasiado corta o está vacía"}, {"key": "messages.input.voice_message_sent", "value": "Mensaje de voz enviado"}, {"key": "messages.input.voice_message_failed", "value": "Error al enviar mensaje de voz"}, {"key": "messages.input.send_message_failed", "value": "Error al enviar mensaje"}, {"key": "messages.input.bot_disabled", "value": "<PERSON><PERSON>"}, {"key": "messages.input.bot_enabled", "value": "Bot Habilitado"}, {"key": "messages.input.messages_as_you", "value": "Los mensajes ahora se enviarán como tú"}, {"key": "messages.input.messages_as_bot", "value": "Los mensajes ahora se enviarán como el bot asistente"}, {"key": "messages.input.file_too_large", "value": "Archivo Demasiado Grande"}, {"key": "messages.input.max_file_size", "value": "El tamaño máximo del archivo es 10MB"}, {"key": "messages.input.record_voice_message", "value": "Grabar mensaje de voz"}, {"key": "messages.input.type_message", "value": "Escribe un mensaje..."}, {"key": "messages.input.resume", "value": "<PERSON><PERSON><PERSON>"}, {"key": "messages.input.pause", "value": "Pausar"}, {"key": "messages.input.stop", "value": "Detener"}, {"key": "messages.input.send", "value": "Enviar"}, {"key": "conversations.item.channel.whatsapp", "value": "WhatsApp"}, {"key": "conversations.item.channel.messenger", "value": "<PERSON>"}, {"key": "conversations.item.channel.instagram", "value": "Instagram"}, {"key": "conversations.item.channel.email", "value": "Correo Electrónico"}, {"key": "conversations.item.channel.sms", "value": "SMS"}, {"key": "conversations.item.channel.web_chat", "value": "Chat Web"}, {"key": "conversations.item.channel.chat", "value": "Cha<PERSON>"}, {"key": "conversations.item.yesterday", "value": "Ayer"}, {"key": "conversations.item.no_messages_yet", "value": "Aún no hay mensajes"}, {"key": "conversations.item.sent_image", "value": "📷 Enviado una imagen"}, {"key": "conversations.item.image", "value": "📷 Imagen"}, {"key": "conversations.item.sent_video", "value": "🎥 Enviado un video"}, {"key": "conversations.item.video", "value": "🎥 Video"}, {"key": "conversations.item.sent_audio", "value": "🎵 Enviado un audio"}, {"key": "conversations.item.audio", "value": "🎵 Audio"}, {"key": "conversations.item.sent_document", "value": "📄 Enviado un documento"}, {"key": "conversations.item.document", "value": "📄 Documento"}, {"key": "conversations.item.conversation_with", "value": "Conversación con"}, {"key": "conversations.item.unread_messages", "value": "mensajes no leídos"}, {"key": "conversations.item.more", "value": "más"}, {"key": "conversations.item.new_lead", "value": "Nuevo Cliente Potencial"}, {"key": "conversations.item.bot_active", "value": "Bot activo"}, {"key": "conversations.item.awaiting_reply", "value": "Esperando respuesta"}, {"key": "conversations.item.bot", "value": "Bot"}, {"key": "conversations.item.waiting", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "conversations.view.channel.whatsapp", "value": "WhatsApp"}, {"key": "conversations.view.channel.messenger", "value": "<PERSON>"}, {"key": "conversations.view.channel.instagram", "value": "Instagram"}, {"key": "conversations.view.channel.email", "value": "Correo Electrónico"}, {"key": "conversations.view.channel.sms", "value": "SMS"}, {"key": "conversations.view.channel.web_chat", "value": "Chat Web"}, {"key": "conversations.view.channel.chat", "value": "Cha<PERSON>"}, {"key": "conversations.view.add_contact", "value": "<PERSON>g<PERSON><PERSON>"}, {"key": "conversations.view.start_call", "value": "<PERSON><PERSON><PERSON>"}, {"key": "conversations.view.schedule", "value": "Programar"}, {"key": "conversations.view.more_options", "value": "Más opciones"}, {"key": "contacts.edit_dialog.contact_id_missing", "value": "Falta el ID del contacto"}, {"key": "contacts.edit_dialog.update_failed", "value": "Fallo al actualizar contacto"}, {"key": "contacts.edit_dialog.success_title", "value": "<PERSON>o <PERSON>"}, {"key": "contacts.edit_dialog.success_description", "value": "El contacto se ha actualizado correctamente."}, {"key": "contacts.edit_dialog.error_title", "value": "Fallo en la actualización"}, {"key": "contacts.edit_dialog.invalid_file_type", "value": "Tipo de archivo inválido"}, {"key": "contacts.edit_dialog.invalid_file_type_desc", "value": "<PERSON>r favor, selecciona un archivo de imagen válido (JPEG, PNG, GIF o WebP)."}, {"key": "contacts.edit_dialog.file_too_large", "value": "Archivo demasiado grande"}, {"key": "contacts.edit_dialog.file_too_large_desc", "value": "El tamaño del archivo es {{sizeMB}}MB. Por favor, selecciona una imagen menor a 5MB."}, {"key": "contacts.edit_dialog.image_too_large", "value": "Imagen demasiado grande"}, {"key": "contacts.edit_dialog.image_too_large_desc", "value": "Las dimensiones de la imagen son {{width}}x{{height}}. Por favor, usa una imagen menor a {{maxDimension}}x{{maxDimension}} píxeles."}, {"key": "contacts.edit_dialog.image_selected", "value": "Imagen se<PERSON>cci<PERSON>"}, {"key": "contacts.edit_dialog.image_selected_desc", "value": "La foto de perfil se ha seleccionado correctamente."}, {"key": "contacts.edit_dialog.error_reading_file", "value": "Error al leer archivo"}, {"key": "contacts.edit_dialog.error_reading_file_desc", "value": "Fallo al leer el archivo de imagen seleccionado."}, {"key": "contacts.edit_dialog.invalid_image", "value": "Imagen inválida"}, {"key": "contacts.edit_dialog.invalid_image_desc", "value": "El archivo seleccionado no es una imagen válida."}, {"key": "contacts.edit_dialog.unexpected_error", "value": "Ocurrió un error inesperado al procesar la imagen."}, {"key": "contacts.edit_dialog.image_removed", "value": "Imagen eliminada"}, {"key": "contacts.edit_dialog.image_removed_desc", "value": "La foto de perfil ha sido eliminada."}, {"key": "contacts.edit_dialog.upload_disabled", "value": "Carga deshabilitada"}, {"key": "contacts.edit_dialog.upload_disabled_desc", "value": "La carga de archivos está actualmente deshabilitada."}, {"key": "contacts.edit_dialog.upload_error", "value": "<PERSON><PERSON>r de carga"}, {"key": "contacts.edit_dialog.upload_not_available", "value": "La carga de archivos no está disponible. Por favor, intenta refrescar la página."}, {"key": "contacts.edit_dialog.upload_failed", "value": "Fallo al abrir el selector de archivos. Por favor, inténtalo de nuevo."}, {"key": "contacts.edit_dialog.validation_error", "value": "Error de validación"}, {"key": "contacts.edit_dialog.name_required", "value": "Se requiere el nombre del contacto."}, {"key": "contacts.edit_dialog.invalid_email", "value": "Por favor, introduce una dirección de correo electrónico válida."}, {"key": "contacts.edit_dialog.invalid_phone", "value": "Por favor, introduce un número de teléfono válido (7-20 dígitos)."}, {"key": "contacts.edit_dialog.title", "value": "Editar Detalles del Contacto"}, {"key": "contacts.edit_dialog.processing", "value": "Procesando..."}, {"key": "contacts.edit_dialog.upload_photo", "value": "Subir Foto"}, {"key": "contacts.edit_dialog.sync_from_whatsapp", "value": "Sincronizar desde WhatsApp"}, {"key": "contacts.edit_dialog.full_name_required", "value": "Nombre Completo *"}, {"key": "contacts.edit_dialog.enter_full_name", "value": "Introduce el nombre completo"}, {"key": "contacts.edit_dialog.email", "value": "Correo Electrónico"}, {"key": "contacts.edit_dialog.enter_email", "value": "Introduce la dirección de correo electrónico"}, {"key": "contacts.edit_dialog.phone_number", "value": "Número de Teléfono"}, {"key": "contacts.edit_dialog.enter_phone", "value": "Introduce el número de teléfono"}, {"key": "contacts.edit_dialog.company", "value": "Empresa"}, {"key": "contacts.edit_dialog.enter_company", "value": "Introduce el nombre de la empresa"}, {"key": "contacts.edit_dialog.primary_channel", "value": "Canal Principal"}, {"key": "contacts.edit_dialog.select_channel", "value": "Selecciona canal"}, {"key": "contacts.edit_dialog.whatsapp_official", "value": "WhatsApp Oficial"}, {"key": "contacts.edit_dialog.whatsapp_unofficial", "value": "WhatsApp No Oficial"}, {"key": "contacts.edit_dialog.facebook_messenger", "value": "Facebook Messenger"}, {"key": "contacts.edit_dialog.instagram", "value": "Instagram"}, {"key": "contacts.edit_dialog.channel_identifier", "value": "Identificador de Canal"}, {"key": "contacts.edit_dialog.phone_username_id", "value": "Número de teléfono, nombre de usuario o ID"}, {"key": "contacts.edit_dialog.tags", "value": "Etiquetas"}, {"key": "contacts.edit_dialog.enter_tags", "value": "Introduce etiquetas separadas por comas (ej., cliente potencial, cliente, vip)"}, {"key": "contacts.edit_dialog.tags_help", "value": "Separa múltiples etiquetas con comas"}, {"key": "contacts.edit_dialog.notes", "value": "Notas"}, {"key": "contacts.edit_dialog.add_notes", "value": "Agrega cualquier nota adicional sobre este contacto..."}, {"key": "contacts.edit_dialog.saving", "value": "Guardando..."}, {"key": "contacts.edit_dialog.save_changes", "value": "Guardar Cambios"}, {"key": "contacts.edit_dialog.unsaved_changes", "value": "Cam<PERSON>s sin guardar"}, {"key": "contacts.edit_dialog.unsaved_changes_message", "value": "¿Está seguro de que desea cerrar sin guardar? Tiene cambios sin guardar."}, {"key": "contacts.edit_dialog.continue_editing", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "contacts.edit_dialog.discard_changes", "value": "Descartar <PERSON>"}, {"key": "contacts.edit_dialog.upload_profile_picture_failed", "value": "Fallo al subir la foto de perfil"}, {"key": "contacts.edit_dialog.profile_picture_updated", "value": "Foto de perfil actualizada"}, {"key": "contacts.edit_dialog.profile_picture_uploaded", "value": "La foto de perfil se ha subido correctamente."}, {"key": "contacts.edit_dialog.profile_picture_upload_failed", "value": "Fallo al subir la foto de perfil"}, {"key": "contacts.edit_dialog.contact_updated_upload_failed", "value": "El contacto se actualizó pero la foto de perfil falló al subirse."}, {"key": "contacts.edit_dialog.profile_preview", "value": "Vista previa del perfil"}, {"key": "media_upload.no_file_selected", "value": "No se seleccionó ningún archivo multimedia"}, {"key": "media_upload.sent_successfully", "value": "Mensaje multimedia enviado con éxito"}, {"key": "media_upload.error_sending", "value": "Error al Enviar Multimedia"}, {"key": "media_upload.send_failed", "value": "Error al enviar mensaje multimedia"}, {"key": "media_upload.image_preview", "value": "Vista previa de imagen"}, {"key": "media_upload.file_size_kb", "value": "{{size}} KB"}, {"key": "media_upload.title", "value": "Enviar Multimedia"}, {"key": "media_upload.description", "value": "Previsualiza y envía archivos multimedia a través de WhatsApp"}, {"key": "media_upload.caption_placeholder", "value": "Agrega un pie de foto (opcional)"}, {"key": "media_upload.sending", "value": "Enviando..."}, {"key": "media_upload.send", "value": "Enviar"}, {"key": "message_bubble.download_failed", "value": "Error al descargar multimedia"}, {"key": "message_bubble.media_downloaded", "value": "Multimedia descargado"}, {"key": "message_bubble.download_success", "value": "El multimedia se ha descargado correctamente"}, {"key": "message_bubble.download_failed_title", "value": "Fallo en la descarga"}, {"key": "message_bubble.image_message", "value": "<PERSON><PERSON><PERSON>"}, {"key": "message_bubble.downloading", "value": "Descargando..."}, {"key": "message_bubble.download_image", "value": "<PERSON><PERSON><PERSON>n"}, {"key": "message_bubble.download_video", "value": "Descargar Video"}, {"key": "message_bubble.video_not_supported", "value": "Tu navegador no soporta el elemento de video."}, {"key": "message_bubble.audio_not_supported", "value": "Tu navegador no soporta el elemento de audio."}, {"key": "message_bubble.audio_message", "value": "Mensaje de Audio"}, {"key": "message_bubble.loading", "value": "Cargando..."}, {"key": "message_bubble.download", "value": "<PERSON><PERSON><PERSON>"}, {"key": "message_bubble.document", "value": "Documento"}, {"key": "message_bubble.open", "value": "Abrir"}, {"key": "message_bubble.reply", "value": "Responder a este mensaje"}, {"key": "message_bubble.delete", "value": "Eliminar este mensaje"}, {"key": "message_bubble.delete_failed", "value": "Fallo al eliminar mensaje"}, {"key": "message_bubble.delete_failed_title", "value": "Fallo en la eliminación"}, {"key": "message_bubble.message_deleted", "value": "<PERSON><PERSON><PERSON> eliminado"}, {"key": "message_bubble.delete_success", "value": "El mensaje se ha eliminado correctamente"}, {"key": "message_bubble.confirm_delete_title", "value": "Eli<PERSON><PERSON>"}, {"key": "message_bubble.confirm_delete_message", "value": "¿Está seguro de que desea eliminar este mensaje? Esta acción no se puede deshacer."}, {"key": "message_bubble.media_message", "value": "Mensaje Multimedia"}, {"key": "message_bubble.deleting", "value": "Eliminando..."}, {"key": "message_bubble.delete_confirm", "value": "Eliminar"}, {"key": "message_input.replying_to", "value": "Respondiendo a"}, {"key": "message_input.cancel_reply", "value": "<PERSON><PERSON><PERSON> respuesta"}, {"key": "message_input.reply_failed", "value": "Fallo al enviar respuesta"}, {"key": "message_input.reply_sent", "value": "Respuesta enviada"}, {"key": "message_input.reply_success", "value": "Tu respuesta se ha enviado correctamente"}, {"key": "channel.reply_not_supported", "value": "Las respuestas no son compatibles con este canal"}, {"key": "channel.delete_not_supported", "value": "La eliminación de mensajes no es compatible con este canal"}, {"key": "channel.message_too_old", "value": "El mensaje es demasiado antiguo para ser eliminado"}, {"key": "channel.whatsapp_delete_limit", "value": "Los mensajes de WhatsApp solo se pueden eliminar dentro de los 3 días"}, {"key": "message_bubble.sticker", "value": "Pegatina"}, {"key": "message_bubble.link_preview", "value": "Vista previa de enlace"}, {"key": "message_bubble.contact_avatar", "value": "Avatar de contacto"}, {"key": "message_bubble.bot", "value": "Bot"}, {"key": "message_bubble.powerchat_assistant", "value": "<PERSON><PERSON><PERSON> de {{appName}}"}, {"key": "clear_history.confirm_message", "value": "¿Está seguro de que desea borrar todo el historial de chat con \"{{conversationName}}\"? Esto eliminará permanentemente todos los mensajes y archivos multimedia de {{appName}}."}, {"key": "clear_history.confirm_group_message", "value": "¿Está seguro de que desea borrar todo el historial de chat de \"{{conversationName}}\"? Esto eliminará permanentemente todos los mensajes y archivos multimedia de {{appName}}."}, {"key": "clear_history.warning_title", "value": "Importante:"}, {"key": "clear_history.warning_irreversible", "value": "Esta acción no se puede deshacer"}, {"key": "clear_history.warning_local_only", "value": "Los mensajes solo se eliminarán de {{appName}}, no de WhatsApp"}, {"key": "clear_history.warning_media", "value": "Todos los archivos multimedia asociados se eliminarán permanentemente"}, {"key": "new_conversation.name_required", "value": "Se requiere el nombre del contacto"}, {"key": "new_conversation.phone_required", "value": "Se requiere el número de teléfono"}, {"key": "new_conversation.phone_invalid", "value": "Por favor, introduce un número de teléfono válido con al menos 10 dígitos"}, {"key": "new_conversation.connection_required", "value": "Por favor, selecciona una conexión de WhatsApp"}, {"key": "new_conversation.no_connections", "value": "No se encontraron conexiones activas de WhatsApp. Por favor, conecta WhatsApp en Configuración primero."}, {"key": "new_conversation.create_failed", "value": "Fallo al crear conversación"}, {"key": "new_conversation.success_message", "value": "Conversación de WhatsApp iniciada correctamente."}, {"key": "new_conversation.create_error", "value": "Fallo al crear conversación. Por favor, inténtalo de nuevo."}, {"key": "new_conversation.no_connection_selected", "value": "No Hay Conexión Seleccionada"}, {"key": "new_conversation.select_connection", "value": "Por favor, selecciona una conexión de WhatsApp."}, {"key": "new_conversation.title", "value": "Iniciar Nueva Conversación de WhatsApp"}, {"key": "new_conversation.description", "value": "Introduce los detalles del contacto para iniciar una nueva conversación de WhatsApp."}, {"key": "new_conversation.contact_name_required", "value": "Nombre del Contacto *"}, {"key": "new_conversation.enter_contact_name", "value": "Introduce el nombre completo del contacto"}, {"key": "new_conversation.phone_number_required", "value": "Número de Teléfono *"}, {"key": "new_conversation.enter_phone_number", "value": "Introduce el número de teléfono (ej., +1234567890)"}, {"key": "new_conversation.include_country_code", "value": "Incluye el código de país para números internacionales"}, {"key": "new_conversation.whatsapp_connection_required", "value": "Conexión de WhatsApp *"}, {"key": "new_conversation.loading_connections", "value": "Cargando conexiones..."}, {"key": "new_conversation.select_whatsapp_connection", "value": "Selecciona la conexión de WhatsApp"}, {"key": "new_conversation.no_connections_available", "value": "No hay conexiones activas de WhatsApp disponibles"}, {"key": "new_conversation.initial_message_optional", "value": "Mensaje <PERSON> (Opcional)"}, {"key": "new_conversation.enter_initial_message", "value": "Introduce un primer mensaje opcional para enviar..."}, {"key": "new_conversation.initial_message_help", "value": "Este mensaje se enviará inmediatamente después de crear la conversación"}, {"key": "new_conversation.creating", "value": "Creando..."}, {"key": "new_conversation.start_conversation", "value": "Iniciar Conversación"}, {"key": "inbox.failed_load_messages", "value": "Error al cargar mensajes de la conversación"}, {"key": "inbox.conversation_assigned", "value": "Conversación Asignada"}, {"key": "inbox.conversation_assigned_to_agent", "value": "La conversación ha sido asignada a un agente"}, {"key": "inbox.conversation_unassigned", "value": "Conversación Desasignada"}, {"key": "inbox.conversation_unassigned_desc", "value": "La conversación ha sido desasignada"}, {"key": "inbox.new_conversation", "value": "Nueva Conversación"}, {"key": "inbox.not_connected", "value": "No Conectado"}, {"key": "inbox.cannot_send_message", "value": "No se puede enviar mensaje, no conectado al servidor"}, {"key": "header.connection_active", "value": "Conexión en tiempo real activa"}, {"key": "header.connection_lost", "value": "Conexión en tiempo real perdida"}, {"key": "header.connected", "value": "Conectado"}, {"key": "header.disconnected", "value": "Desconectado"}, {"key": "sidebar.expand", "value": "Expandir barra lateral"}, {"key": "sidebar.collapse", "value": "Colapsar barra lateral"}, {"key": "auth.password_reset_successful", "value": "Restablecimiento de contraseña exitoso"}, {"key": "auth.error", "value": "Error"}, {"key": "auth.invalid_reset_token", "value": "Token de restablecimiento inválido"}, {"key": "auth.fill_all_fields", "value": "Por favor, complete todos los campos"}, {"key": "auth.passwords_do_not_match", "value": "Las contraseñas no coinciden"}, {"key": "auth.password_min_length", "value": "La contraseña debe tener al menos 6 caracteres"}, {"key": "auth.validating_token", "value": "Validando token de restablecimiento..."}, {"key": "auth.invalid_reset_link", "value": "Enlace de Restablecimiento Inválido"}, {"key": "auth.reset_link_expired", "value": "Este enlace de restablecimiento de contraseña es inválido o ha expirado."}, {"key": "auth.reset_link_invalid", "value": "El enlace de restablecimiento ya no es válido."}, {"key": "auth.request_new_reset_link", "value": "Solicitar nuevo enlace de restablecimiento"}, {"key": "auth.back_to_login", "value": "Volver al inicio de sesión"}, {"key": "auth.password_reset_success_title", "value": "Restablecimiento de Contraseña Exitoso"}, {"key": "auth.password_reset_success_desc", "value": "Tu contraseña se ha restablecido correctamente. Ahora puedes iniciar sesión con tu nueva contraseña."}, {"key": "auth.password_reset_success_message", "value": "Ahora puedes usar tu nueva contraseña para acceder a tu cuenta."}, {"key": "auth.continue_to_login", "value": "Continuar al inicio de sesión"}, {"key": "auth.reset_your_password", "value": "Restablece tu contraseña"}, {"key": "auth.enter_new_password", "value": "Introduce una nueva contraseña para tu cuenta."}, {"key": "auth.new_password", "value": "Nueva contraseña"}, {"key": "auth.new_password_placeholder", "value": "Introduce tu nueva contraseña"}, {"key": "auth.confirm_new_password", "value": "Confirmar nueva contraseña"}, {"key": "auth.confirm_new_password_placeholder", "value": "Confirma tu nueva contraseña"}, {"key": "auth.reset_password_button", "value": "Restablecer contraseña"}, {"key": "admin.translations.create_key_failed", "value": "Error al crear clave de traducción"}, {"key": "admin.translations.key_created", "value": "Clave de traducción creada"}, {"key": "admin.translations.key_created_desc", "value": "La clave de traducción se ha creado correctamente."}, {"key": "admin.translations.add_key", "value": "<PERSON>g<PERSON><PERSON><PERSON> Traducci<PERSON>"}, {"key": "admin.translations.add_new_key", "value": "Agregar Nueva Clave de Traducción"}, {"key": "admin.translations.add_key_desc", "value": "Agrega una nueva clave de traducción al espacio de nombres seleccionado."}, {"key": "admin.translations.key_label", "value": "Clave"}, {"key": "admin.translations.key_placeholder", "value": "mensaje_bienvenida"}, {"key": "admin.translations.description_label", "value": "Descripción"}, {"key": "admin.translations.description_placeholder", "value": "Mensaje de bienvenida mostrado en la página de inicio"}, {"key": "admin.translations.creating", "value": "Creando..."}, {"key": "admin.translations.create_key_button", "value": "<PERSON><PERSON><PERSON>"}, {"key": "admin.languages.active_label", "value": "Activo"}, {"key": "admin.languages.enable_language", "value": "Habilitar este idioma"}, {"key": "admin.languages.updating", "value": "Actualizando..."}, {"key": "admin.languages.update_language", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "admin.users.super_admin", "value": "Super Administrador"}, {"key": "admin.users.system", "value": "Sistema"}, {"key": "admin.users.actions", "value": "Acciones"}, {"key": "admin.users.edit_user", "value": "<PERSON><PERSON>"}, {"key": "admin.users.reset_password", "value": "Restable<PERSON>"}, {"key": "settings.whatsapp.settings_saved", "value": "Configuración Guardada"}, {"key": "settings.whatsapp.settings_saved_desc", "value": "La configuración del comportamiento de WhatsApp se ha actualizado correctamente."}, {"key": "settings.whatsapp.save_failed", "value": "Error al guardar la configuración del comportamiento de WhatsApp. Por favor, inténtalo de nuevo."}, {"key": "settings.whatsapp.typing_indicators", "value": "Indicadores de Escritura"}, {"key": "settings.whatsapp.typing_indicators_desc", "value": "Configura indicadores de escritura similares a los humanos que aparecen antes de enviar mensajes"}, {"key": "settings.whatsapp.enable_typing", "value": "Habilitar Indicadores de Escritura"}, {"key": "settings.whatsapp.enable_typing_desc", "value": "Muestra los indicadores \"Escribiendo...\" y \"Grabando...\" antes de enviar mensajes"}, {"key": "settings.whatsapp.preview", "value": "Vista Previa"}, {"key": "settings.whatsapp.preview_desc", "value": "Mira cómo tu configuración afectará la entrega de mensajes"}, {"key": "settings.whatsapp.sample_message", "value": "<PERSON><PERSON><PERSON>"}, {"key": "settings.whatsapp.sample_message_placeholder", "value": "Introduce un mensaje de muestra para ver cómo se dividirá y programará..."}, {"key": "common.success", "value": "Éxito"}, {"key": "common.error", "value": "Error"}, {"key": "common.cancel", "value": "<PERSON><PERSON><PERSON>"}, {"key": "common.edit", "value": "<PERSON><PERSON>"}, {"key": "common.save", "value": "Guardar"}, {"key": "common.loading", "value": "Cargando..."}, {"key": "common.please_wait", "value": "Por favor, espere..."}, {"key": "common.back", "value": "Atrás"}, {"key": "common.delete_item", "value": "Eliminar Elemento"}, {"key": "common.delete_confirmation", "value": "¿Está seguro de que desea eliminar este elemento? Esta acción no se puede deshacer."}, {"key": "common.delete", "value": "Eliminar"}, {"key": "common.deleting", "value": "Eliminando..."}, {"key": "payment.processing", "value": "Procesando <PERSON>"}, {"key": "payment.successful", "value": "<PERSON><PERSON>"}, {"key": "payment.failed", "value": "Pago Fallido"}, {"key": "payment.verification_failed", "value": "Fallo en la Verificación del Pago"}, {"key": "payment.verifying", "value": "Estamos verificando tu pago..."}, {"key": "payment.subscription_activated", "value": "Tu suscripción se ha activado correctamente."}, {"key": "payment.could_not_process", "value": "No se pudo procesar tu pago. Por favor, inténtalo de nuevo."}, {"key": "payment.contact_support", "value": "No pudimos verificar tu pago. Por favor, contacta con soporte."}, {"key": "payment.processed_successfully", "value": "Tu pago se ha procesado correctamente."}, {"key": "payment.missing_info", "value": "Información de pago faltante. Por favor, contacta con soporte si completaste un pago."}, {"key": "payment.details", "value": "Detalles del Pago"}, {"key": "payment.payment_id", "value": "ID de Pago:"}, {"key": "payment.transaction_id", "value": "ID de Transacción:"}, {"key": "payment.payment_method", "value": "Método de Pago:"}, {"key": "payment.status", "value": "Estado:"}, {"key": "payment.try_again", "value": "Intentar de Nuevo"}, {"key": "payment.return_to_settings", "value": "Volver a Configuración"}, {"key": "payment.go_to_inbox", "value": "<PERSON>r a Bandeja de Entrada"}, {"key": "subdomain.company_not_found", "value": "Empresa No Encontrada"}, {"key": "subdomain.no_company_found", "value": "No se encontró ninguna empresa para el subdominio \"{{subdomain}}\""}, {"key": "subdomain.company_not_exist", "value": "La empresa que intentas acceder no existe o ha sido eliminada."}, {"key": "subdomain.account_inactive", "value": "Cuenta de Empresa Inactiva"}, {"key": "subdomain.account_inactive_desc", "value": "La cuenta de esta empresa está actualmente inactiva"}, {"key": "subdomain.account_disabled", "value": "La cuenta de la empresa ha sido deshabilitada temporalmente. Por favor, contacta con soporte para obtener ayuda."}, {"key": "subdomain.invalid_subdomain", "value": "Subdominio Inválido"}, {"key": "subdomain.invalid_subdomain_desc", "value": "\"{{subdomain}}\" no es un subdominio válido"}, {"key": "subdomain.subdomain_rules", "value": "Los subdominios solo pueden contener letras, números y guiones."}, {"key": "subdomain.connection_error", "value": "Error de Conexión"}, {"key": "subdomain.unable_verify", "value": "No se puede verificar la información de la empresa"}, {"key": "subdomain.server_problem", "value": "Hubo un problema al conectarse al servidor. Por favor, inténtalo de nuevo."}, {"key": "subdomain.access_error", "value": "Error de Acceso"}, {"key": "subdomain.unable_access", "value": "No se puede acceder a esta empresa"}, {"key": "subdomain.unexpected_error", "value": "Ocurrió un error inesperado."}, {"key": "subdomain.subdomain_label", "value": "Subdominio:"}, {"key": "subdomain.url_label", "value": "URL:"}, {"key": "subdomain.go_to_main_site", "value": "<PERSON><PERSON> <PERSON>"}, {"key": "subdomain.go_back", "value": "Volver"}, {"key": "subdomain.different_company_help", "value": "¿Buscas una empresa diferente? Asegúrate de tener la URL de subdominio correcta."}, {"key": "subdomain.admin_contact_help", "value": "Si eres administrador de la empresa, por favor contacta a soporte para reactivar tu cuenta."}, {"key": "whatsapp_360dialog.validation_error", "value": "Error de Validación"}, {"key": "whatsapp_360dialog.api_key_required", "value": "Se requieren la Clave API y el Número de Teléfono para la validación."}, {"key": "whatsapp_360dialog.credentials_valid", "value": "Credenciales Válidas"}, {"key": "whatsapp_360dialog.validation_success", "value": "Clave API de 360Dialog validada correctamente para el número de teléfono: {{phoneNumber}}"}, {"key": "whatsapp_360dialog.invalid_credentials", "value": "Credenciales inválidas"}, {"key": "whatsapp_360dialog.validation_failed", "value": "Fallo en la Validación"}, {"key": "whatsapp_360dialog.validation_failed_desc", "value": "Fallo al validar las credenciales de 360Dialog."}, {"key": "whatsapp_360dialog.success", "value": "Éxito"}, {"key": "whatsapp_360dialog.connection_created", "value": "¡Conexión de WhatsApp 360Dialog creada exitosamente!"}, {"key": "whatsapp_360dialog.connection_failed", "value": "Fallo al crear conexión"}, {"key": "whatsapp_360dialog.connection_error", "value": "Error de Conexión"}, {"key": "whatsapp_360dialog.connection_error_desc", "value": "Fallo al conectarse a WhatsApp 360Dialog"}, {"key": "whatsapp_360dialog.connect_title", "value": "Conectar WhatsApp 360Dialog"}, {"key": "whatsapp_360dialog.connection_name", "value": "Nombre de la Conexión"}, {"key": "whatsapp_360dialog.connection_name_placeholder", "value": "Mi WhatsApp 360Dialog"}, {"key": "whatsapp_360dialog.connection_name_help", "value": "Un nombre amigable para esta conexión"}, {"key": "whatsapp_360dialog.api_key", "value": "Clave API"}, {"key": "whatsapp_360dialog.api_key_placeholder", "value": "Tu clave API de 360Dialog..."}, {"key": "whatsapp_360dialog.api_key_help", "value": "Tu clave API de 360Dialog desde el panel del Hub"}, {"key": "whatsapp_360dialog.phone_number", "value": "Número de Teléfono de WhatsApp"}, {"key": "whatsapp_360dialog.phone_number_placeholder", "value": "+1234567890"}, {"key": "whatsapp_360dialog.phone_number_help", "value": "Tu número de teléfono de WhatsApp Business registrado con 360Dialog"}, {"key": "whatsapp_360dialog.setup_title", "value": "Configuración de WhatsApp 360Dialog"}, {"key": "whatsapp_360dialog.setup_steps", "value": "1. Crea una cuenta de 360Dialog y obtén la aprobación de WhatsApp Business\n2. Genera una clave API en el Hub de 360Dialog\n3. Configura la URL del webhook en tu configuración de 360Dialog\n4. Prueba la conexión para asegurarte de que todo funciona"}, {"key": "whatsapp_360dialog.validating", "value": "Validando..."}, {"key": "whatsapp_360dialog.test", "value": "Probar"}, {"key": "whatsapp_360dialog.webhook_url", "value": "URL de Webhook"}, {"key": "whatsapp_360dialog.webhook_url_placeholder", "value": "https://tudominio.com/api/webhooks/360dialog-whatsapp"}, {"key": "whatsapp_360dialog.webhook_url_help", "value": "URL donde 360Dialog enviará notificaciones de webhook"}, {"key": "whatsapp_360dialog.connecting", "value": "Conectando..."}, {"key": "whatsapp_360dialog.connect", "value": "Conectar"}, {"key": "admin.languages.create_failed", "value": "Fallo al crear idioma"}, {"key": "admin.languages.language_created", "value": "Idioma creado"}, {"key": "admin.languages.language_created_desc", "value": "El idioma se ha creado correctamente."}, {"key": "admin.languages.add_language", "value": "Agregar <PERSON>"}, {"key": "admin.languages.add_new_language", "value": "Agregar Nuevo Idioma"}, {"key": "admin.languages.add_language_desc", "value": "Crea un nuevo idioma para la aplicación."}, {"key": "admin.languages.code_label", "value": "Código"}, {"key": "admin.languages.code_placeholder", "value": "es"}, {"key": "admin.languages.name_label", "value": "Nombre"}, {"key": "admin.languages.name_placeholder", "value": "Español"}, {"key": "admin.languages.native_name_label", "value": "Nombre Nativo"}, {"key": "admin.languages.native_name_placeholder", "value": "Español"}, {"key": "admin.languages.flag_icon_label", "value": "Icono de Bandera"}, {"key": "admin.languages.flag_icon_placeholder", "value": "🇪🇸"}, {"key": "admin.languages.direction_label", "value": "Dirección"}, {"key": "admin.languages.select_direction", "value": "Seleccionar <PERSON>"}, {"key": "admin.languages.ltr", "value": "De Izquierda a Derecha"}, {"key": "admin.languages.rtl", "value": "De Derecha a Izquierda"}, {"key": "admin.languages.default_label", "value": "Predeterminado"}, {"key": "admin.languages.set_default", "value": "Establecer como idioma predeterminado"}, {"key": "admin.languages.creating", "value": "Creando..."}, {"key": "admin.languages.create_language", "value": "<PERSON><PERSON><PERSON>"}, {"key": "admin.languages.edit_language", "value": "<PERSON><PERSON>"}, {"key": "admin.languages.edit_language_desc", "value": "Actualice la configuración del idioma."}, {"key": "admin.languages.update_failed", "value": "Fallo al actualizar idioma"}, {"key": "admin.languages.language_updated", "value": "Idioma actualizado"}, {"key": "admin.languages.language_updated_desc", "value": "El idioma se ha actualizado correctamente."}, {"key": "admin.namespaces.create_failed", "value": "Fallo al crear espacio de nombres"}, {"key": "admin.namespaces.namespace_created", "value": "Espacio de nombres creado"}, {"key": "admin.namespaces.namespace_created_desc", "value": "El espacio de nombres se ha creado correctamente."}, {"key": "admin.namespaces.add_namespace", "value": "Agregar E<PERSON><PERSON><PERSON> Nombres"}, {"key": "admin.namespaces.add_new_namespace", "value": "Agregar Nuevo Espacio de Nombres"}, {"key": "admin.namespaces.add_namespace_desc", "value": "Crea un nuevo espacio de nombres para organizar traducciones."}, {"key": "admin.namespaces.name_placeholder", "value": "común"}, {"key": "admin.namespaces.description_placeholder", "value": "Traducciones comunes utilizadas en toda la aplicación"}, {"key": "admin.namespaces.create_namespace", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "admin.translations.export_array", "value": "Exportar Matriz"}, {"key": "admin.translations.export_nested", "value": "Exportar Anidado"}, {"key": "admin.translations.import_translations", "value": "Importar Traducciones"}, {"key": "admin.translations.import_translations_desc", "value": "Importa traducciones desde un archivo JSON. Admite formato de matriz y formato anidado."}, {"key": "admin.translations.select_language_file", "value": "Por favor, selecciona un idioma y un archivo"}, {"key": "admin.translations.import_failed", "value": "Fallo al importar traducciones"}, {"key": "admin.translations.translations_imported", "value": "Traducciones importadas"}, {"key": "admin.translations.translations_imported_desc", "value": "Las traducciones se han importado correctamente."}, {"key": "admin.translations.language_label", "value": "Idioma"}, {"key": "admin.translations.select_language", "value": "Seleccionar idioma"}, {"key": "admin.translations.file_label", "value": "Archivo"}, {"key": "admin.analytics.dashboard_title", "value": "Panel de Analíticas"}, {"key": "admin.analytics.time_range", "value": "<PERSON><PERSON> Tiempo:"}, {"key": "admin.analytics.select_time_range", "value": "Selecciona el rango de tiempo"}, {"key": "admin.analytics.last_7_days", "value": "Últimos 7 Días"}, {"key": "admin.analytics.last_30_days", "value": "Últimos 30 Días"}, {"key": "admin.analytics.last_90_days", "value": "Últimos 90 Días"}, {"key": "admin.analytics.last_year", "value": "<PERSON>lt<PERSON>"}, {"key": "admin.analytics.all_time", "value": "Todo el Tiempo"}, {"key": "admin.analytics.error_loading", "value": "Error al Cargar Analíticas"}, {"key": "admin.analytics.failed_load_data", "value": "Error al cargar datos de analíticas"}, {"key": "admin.analytics.retry", "value": "Reintentar"}, {"key": "admin.analytics.total_users", "value": "Usuarios Totales"}, {"key": "admin.analytics.users_last_period", "value": "+{{count}} en el último período"}, {"key": "admin.analytics.companies", "value": "Empresas"}, {"key": "admin.analytics.active_companies", "value": "{{count}} empresas activas"}, {"key": "admin.analytics.messages", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "admin.analytics.across_conversations", "value": "En {{count}} conversaciones"}, {"key": "admin.analytics.contacts", "value": "Contactos"}, {"key": "admin.analytics.in_contact_database", "value": "En la base de datos de contactos"}, {"key": "admin.analytics.user_growth", "value": "Crecimiento de Usuarios"}, {"key": "admin.analytics.user_growth_chart", "value": "Visualización del gráfico de crecimiento de usuarios"}, {"key": "admin.analytics.channel_distribution", "value": "Distribución por Canal"}, {"key": "admin.analytics.channel_distribution_chart", "value": "Visualización del gráfico de distribución por canal"}, {"key": "admin.analytics.company_conversations", "value": "Conversaciones de la Empresa"}, {"key": "admin.analytics.company_conversations_chart", "value": "Visualización del gráfico de conversaciones de la empresa"}, {"key": "admin.analytics.active_users", "value": "Usuarios Activos"}, {"key": "admin.analytics.active_users_chart", "value": "Visualización del gráfico de usuarios activos"}, {"key": "flow_builder.translation_node.hide", "value": "Ocultar"}, {"key": "flow_builder.translation_node.edit", "value": "<PERSON><PERSON>"}, {"key": "flow_builder.translation_node.openai_translation", "value": "Traducción OpenAI"}, {"key": "flow_builder.translation_node.translation_enabled", "value": "Traducción habilitada"}, {"key": "flow_builder.translation_node.translation_disabled", "value": "Traducción deshabilitada"}, {"key": "flow_builder.translation_node.target_label", "value": "Objetivo:"}, {"key": "flow_builder.translation_node.mode_label", "value": "Modo:"}, {"key": "flow_builder.translation_node.auto_detect", "value": "Detección Automática"}, {"key": "flow_builder.translation_node.configuration", "value": "Configuración de Traducción"}, {"key": "flow_builder.translation_node.enable_translation", "value": "Habilitar <PERSON>"}, {"key": "flow_builder.translation_node.enable_desc", "value": "Traduce automáticamente los mensajes usando OpenAI"}, {"key": "flow_builder.translation_node.openai_api_key", "value": "Clave API de OpenAI"}, {"key": "flow_builder.translation_node.api_key_placeholder", "value": "Introduce tu clave API de OpenAI"}, {"key": "flow_builder.translation_node.get_api_key", "value": "Obtén tu clave API aquí"}, {"key": "flow_builder.translation_node.target_language", "value": "Idioma de Destino"}, {"key": "flow_builder.translation_node.select_language", "value": "Seleccionar idioma..."}, {"key": "flow_builder.translation_node.translation_mode", "value": "<PERSON><PERSON>"}, {"key": "flow_builder.translation_node.select_mode", "value": "Seleccionar modo..."}, {"key": "flow_builder.translation_node.auto_language_detection", "value": "Detección Automática de Idioma"}, {"key": "flow_builder.translation_node.auto_detect_desc", "value": "Traduce solo si origen ≠ destino"}, {"key": "flow_builder.translation_node.separate_message", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flow_builder.translation_node.separate_desc", "value": "Envía la traducción como un mensaje de seguimiento separado"}, {"key": "flow_builder.translation_node.append_original", "value": "Adjun<PERSON> al Original"}, {"key": "flow_builder.translation_node.append_desc", "value": "Añade la traducción al final del mensaje original"}, {"key": "flow_builder.translation_node.replace_original", "value": "Reemplazar Original"}, {"key": "flow_builder.translation_node.replace_desc", "value": "Reemplaza el mensaje original con la traducción"}, {"key": "flow_builder.typebot_node.start_conversation", "value": "Iniciar Conversación"}, {"key": "flow_builder.typebot_node.start_conversation_desc", "value": "Inicializar nueva sesión de chat"}, {"key": "flow_builder.typebot_node.send_message", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flow_builder.typebot_node.send_message_desc", "value": "Enviar mensaje del usuario al bot"}, {"key": "flow_builder.typebot_node.get_response", "value": "Obtener Respuesta"}, {"key": "flow_builder.typebot_node.get_response_desc", "value": "Recuperar respuesta del bot"}, {"key": "flow_builder.typebot_node.manage_session", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "flow_builder.typebot_node.manage_session_desc", "value": "<PERSON><PERSON><PERSON>/actualizar/cerrar se<PERSON>"}, {"key": "flow_builder.typebot_node.webhook_event", "value": "Evento Webhook"}, {"key": "flow_builder.typebot_node.webhook_event_desc", "value": "<PERSON><PERSON><PERSON> webhook entrante"}, {"key": "flow_builder.typebot_node.get_analytics", "value": "Obtener Analíticas"}, {"key": "flow_builder.typebot_node.get_analytics_desc", "value": "Recuperar analíticas de la conversación"}, {"key": "flow_builder.typebot_node.create_session", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flow_builder.typebot_node.update_session", "value": "Actualizar <PERSON>"}, {"key": "flow_builder.typebot_node.close_session", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flow_builder.typebot_node.get_status", "value": "Obtener Estado"}, {"key": "flow_builder.typebot_node.text_input", "value": "Entrada de Texto"}, {"key": "flow_builder.typebot_node.number_input", "value": "Entrada de Número"}, {"key": "flow_builder.typebot_node.email_input", "value": "Entrada de Correo Electrónico"}, {"key": "flow_builder.typebot_node.url_input", "value": "Entrada de URL"}, {"key": "flow_builder.typebot_node.date_input", "value": "Entrada de Fecha"}, {"key": "flow_builder.typebot_node.phone_input", "value": "Entrada de Teléfono"}, {"key": "flow_builder.typebot_node.multiple_choice", "value": "Opción Múltiple"}, {"key": "flow_builder.typebot_node.file_upload", "value": "Carga de Archivos"}, {"key": "flow_builder.typebot_node.start_new_chat", "value": "Iniciar <PERSON>"}, {"key": "flow_builder.typebot_node.send_user_message", "value": "Enviar Men<PERSON>"}, {"key": "flow_builder.typebot_node.get_bot_response", "value": "Obtener Respuesta del Bot"}, {"key": "flow_builder.typebot_node.close_conversation", "value": "Cerrar Conversación"}, {"key": "flow_builder.typebot_node.typebot_integration", "value": "Integración Typebot"}, {"key": "flow_builder.typebot_node.no_bot_selected", "value": "Ningún bot seleccionado"}, {"key": "flow_builder.typebot_node.bot_label", "value": "Bot:"}, {"key": "flow_builder.typebot_node.config_label", "value": "configuración"}, {"key": "flow_builder.typebot_node.configs_label", "value": "configuraciones"}, {"key": "flow_builder.typebot_node.mapping_label", "value": "mapeo"}, {"key": "flow_builder.typebot_node.mappings_label", "value": "mapeos"}, {"key": "flow_builder.typebot_node.api_connected", "value": "API Conectada"}, {"key": "flow_builder.typebot_node.timeout_label", "value": "Tiempo de espera: {{timeout}}s"}, {"key": "flow_builder.typebot_node.quick_templates", "value": "Plant<PERSON><PERSON>"}, {"key": "flow_builder.typebot_node.choose_operation", "value": "Elige una operación de Typebot..."}, {"key": "flow_builder.typebot_node.api_configuration", "value": "Configuración API Typebot"}, {"key": "flow_builder.typebot_node.api_token", "value": "Token API"}, {"key": "flow_builder.typebot_node.workspace_id", "value": "ID del Espacio de Trabajo (Opcional)"}, {"key": "flow_builder.typebot_node.typebot_id", "value": "ID de Typebot"}, {"key": "flow_builder.typebot_node.session_timeout", "value": "Tiempo de espera de sesión (segundos)"}, {"key": "flow_builder.typebot_node.test_connection", "value": "Probar"}, {"key": "flow_builder.typebot_node.test_connection_title", "value": "Probar la conexión a tu Typebot"}, {"key": "flow_builder.typebot_node.testing", "value": "Probando..."}, {"key": "flow_builder.typebot_node.typebot_operation", "value": "Operación Typebot"}, {"key": "flow_builder.typebot_node.select_operation", "value": "Seleccionar operación"}, {"key": "flow_builder.typebot_node.operation_configuration", "value": "Configuración de Operación"}, {"key": "flow_builder.typebot_node.enabled", "value": "Habilitado"}, {"key": "flow_builder.typebot_node.disabled", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"key": "flow_builder.typebot_node.variable_syntax", "value": "Usa la sintaxis {{variable}} para valores dinámicos"}, {"key": "flow_builder.typebot_node.response_variable_mapping", "value": "Mapeo de Variables de Respuesta"}, {"key": "flow_builder.typebot_node.add_mapping", "value": "Agregar"}, {"key": "flow_builder.typebot_node.mapping_description", "value": "Mapea los campos de respuesta de Typebot a variables de flujo para usarlos en nodos subsiguientes"}, {"key": "flow_builder.main.flow_name", "value": "Nombre del flujo"}, {"key": "flow_builder.main.auto_arrange", "value": "Auto-Organizar"}, {"key": "flow_builder.main.arranging", "value": "Organizando..."}, {"key": "flow_builder.main.auto_arrange_tooltip", "value": "Organiza automáticamente todos los nodos en un diseño jerárquico limpio"}, {"key": "flow_builder.main.auto_arrange_shortcut", "value": "Atajo: Ctrl+Shift+A"}, {"key": "flow_builder.main.undo_arrange", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flow_builder.main.undo_arrange_tooltip", "value": "Restaura los nodos a sus posiciones anteriores"}, {"key": "flow_builder.main.undo_arrange_shortcut", "value": "Atajo: Ctrl+Z"}, {"key": "flow_builder.main.node_selection", "value": "Selección de Nodo"}, {"key": "flow_builder.main.nodes_auto_arranged", "value": "Nodos Auto-Organizados"}, {"key": "flow_builder.main.nodes_arranged_desc", "value": "{{count}} nodos organizados en {{levels}} niveles con espaciado adecuado. ¡Sin superposiciones garantizadas!"}, {"key": "flow_builder.main.auto_arrange_undone", "value": "Auto-Organización Deshecha"}, {"key": "flow_builder.main.nodes_restored", "value": "Los nodos se han restaurado a sus posiciones anteriores."}, {"key": "flow_builder.node_types.whatsapp_message", "value": "<PERSON><PERSON><PERSON>sApp"}, {"key": "flow_builder.node_types.text_message", "value": "Mensaje de <PERSON>o"}, {"key": "flow_builder.node_types.quick_reply_options", "value": "Opciones de Respuesta Rápida"}, {"key": "flow_builder.node_types.follow_up_message", "value": "Mensaje de Seguimiento"}, {"key": "flow_builder.node_types.image_message", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flow_builder.node_types.video_message", "value": "Mensaje de <PERSON>"}, {"key": "flow_builder.node_types.audio_message", "value": "Mensaje de Audio"}, {"key": "flow_builder.node_types.document_message", "value": "Mensaje de Documento"}, {"key": "flow_builder.node_types.condition", "value": "Condición"}, {"key": "flow_builder.node_types.wait", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flow_builder.node_types.ai_assistant", "value": "Asistente IA"}, {"key": "flow_builder.node_types.translation", "value": "Traducción"}, {"key": "flow_builder.node_types.pipeline", "value": "Pipeline"}, {"key": "flow_builder.node_types.agent_handoff", "value": "Transferencia a Agente"}, {"key": "flow_builder.node_types.n8n", "value": "n8n"}, {"key": "flow_builder.node_types.make_com", "value": "Make.com"}, {"key": "flow_builder.node_types.http_request", "value": "Solicitud HTTP"}, {"key": "flow_builder.node_types.google_sheets", "value": "Google Sheets"}, {"key": "flow_builder.node_types.webhook", "value": "Webhook"}, {"key": "flow_builder.node_types.shopify", "value": "Shopify"}, {"key": "flow_builder.node_types.woocommerce", "value": "WooCommerce"}, {"key": "flow_builder.node_types.typebot", "value": "Typebot"}, {"key": "flow_builder.node_types.flowise", "value": "Flowise"}, {"key": "flow_builder.node_types.documind_pdf_chat", "value": "Chat PDF Documind"}, {"key": "flow_builder.node_types.chat_pdf_ai", "value": "Chat PDF IA"}, {"key": "flow_builder.sections.triggers", "value": "Disparadores"}, {"key": "flow_builder.sections.messages", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "flow_builder.sections.flow_control", "value": "Control de Flujo"}, {"key": "flow_builder.sections.integrations", "value": "Integraciones"}, {"key": "flow_builder.condition_types.message_contains", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flow_builder.condition_types.exact_match", "value": "Coincidencia Exacta"}, {"key": "flow_builder.condition_types.regex_match", "value": "Coincidencia Regex"}, {"key": "flow_builder.condition_types.message_starts_with", "value": "Mensaje <PERSON> con"}, {"key": "flow_builder.condition_types.message_ends_with", "value": "Mensaje <PERSON>"}, {"key": "flow_builder.condition_types.has_media", "value": "Tiene Multimedia"}, {"key": "flow_builder.condition_types.media_type_is", "value": "Tipo de Multimedia Es"}, {"key": "flow_builder.condition_types.time_condition", "value": "Condición de Tiempo"}, {"key": "flow_builder.condition_types.contact_attribute", "value": "Atributo de Contacto"}, {"key": "flow_builder.condition_types.custom_expression", "value": "Expresión Personalizada"}, {"key": "flow_builder.media_types.image", "value": "imagen"}, {"key": "flow_builder.media_types.video", "value": "video"}, {"key": "flow_builder.media_types.audio", "value": "audio"}, {"key": "flow_builder.media_types.document", "value": "documento"}, {"key": "flow_builder.media_types.sticker", "value": "pegatina"}, {"key": "flow_builder.file_types.pdf_document", "value": "Documento PDF"}, {"key": "flow_builder.file_types.word_document", "value": "Documento de Word"}, {"key": "flow_builder.file_types.excel_spreadsheet", "value": "Hoja de Cálculo Excel"}, {"key": "flow_builder.file_types.powerpoint_presentation", "value": "Presentación de PowerPoint"}, {"key": "flow_builder.file_types.text_file", "value": "Archivo de Texto"}, {"key": "flow_builder.file_types.archive_file", "value": "Archivo Comprimido"}, {"key": "flow_builder.file_types.image_file", "value": "Archivo de Imagen"}, {"key": "flow_builder.file_types.document", "value": "Documento"}, {"key": "flow_builder.singleton_errors.typebot_exists", "value": "Solo se permite un nodo Typebot por flujo"}, {"key": "flow_builder.singleton_errors.flowise_exists", "value": "Solo se permite un nodo Flowise por flujo"}, {"key": "campaigns.dashboard_description", "value": "Gestiona y supervisa tus campañas de mensajería masiva"}, {"key": "campaigns.live_updates", "value": "Actualizaciones en vivo"}, {"key": "campaigns.polling_mode", "value": "<PERSON><PERSON> de sondeo"}, {"key": "campaigns.refresh", "value": "Actualizar"}, {"key": "campaigns.total_campaigns", "value": "Campañas Totales"}, {"key": "campaigns.active_campaigns", "value": "Campañas Activas"}, {"key": "campaigns.total_recipients", "value": "Destinatarios Totales"}, {"key": "campaigns.messages_delivered", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "campaigns.delivery_rate", "value": "Tasa <PERSON>ga"}, {"key": "campaigns.status.draft", "value": "<PERSON><PERSON><PERSON>"}, {"key": "campaigns.status.scheduled", "value": "Programado"}, {"key": "campaigns.status.running", "value": "En ejecución"}, {"key": "campaigns.status.paused", "value": "<PERSON><PERSON><PERSON>"}, {"key": "campaigns.status.completed", "value": "Completado"}, {"key": "campaigns.status.cancelled", "value": "Cancelado"}, {"key": "campaigns.status.failed", "value": "Fallido"}, {"key": "campaigns.start", "value": "Iniciar"}, {"key": "campaigns.pause", "value": "Pausar"}, {"key": "campaigns.resume", "value": "<PERSON><PERSON><PERSON>"}, {"key": "campaigns.details", "value": "Detalles"}, {"key": "campaigns.recipients", "value": "Des<PERSON><PERSON><PERSON>"}, {"key": "campaigns.progress", "value": "Progreso"}, {"key": "campaigns.delivered", "value": "<PERSON><PERSON><PERSON>"}, {"key": "campaigns.failed", "value": "Fallido"}, {"key": "campaigns.processed", "value": "procesado"}, {"key": "campaigns.total", "value": "total"}, {"key": "campaigns.delete_campaign", "value": "Eliminar <PERSON>"}, {"key": "campaigns.delete_confirmation", "value": "¿Está seguro de que desea eliminar la campaña \"{{name}}\"? Esta acción no se puede deshacer y todos los datos de la campaña se eliminarán permanentemente."}, {"key": "campaigns.completed", "value": "Campaña <PERSON>tada"}, {"key": "campaigns.finished_processing", "value": "La campaña \"{{name}}\" ha terminado de procesarse."}, {"key": "campaigns.fetch_failed", "value": "Error al obtener campañas"}, {"key": "campaigns.stats_fetch_failed", "value": "Error al obtener estadísticas de la campaña"}, {"key": "campaigns.action_failed", "value": "Error al {{action}} la campaña"}, {"key": "campaigns.deleted_successfully", "value": "La campaña \"{{name}}\" se ha eliminado correctamente"}, {"key": "campaigns.delete_failed", "value": "Error al eliminar la campaña"}, {"key": "campaigns.stats_recalculated", "value": "Estadísticas Recalculadas"}, {"key": "campaigns.stats_updated", "value": "Las estadísticas de la campaña se han actualizado."}, {"key": "campaigns.stats_recalc_failed", "value": "Error al recalcular estadísticas: {{error}}"}, {"key": "campaigns.network_error", "value": "Error de red: {{error}}"}, {"key": "settings.whatsapp.typing_indicators", "value": "Indicadores de Escritura"}, {"key": "settings.whatsapp.typing_indicators_desc", "value": "Configura indicadores de escritura similares a los humanos que aparecen antes de enviar mensajes"}, {"key": "settings.whatsapp.enable_typing", "value": "Habilitar Indicadores de Escritura"}, {"key": "settings.whatsapp.enable_typing_desc", "value": "Muestra los indicadores \"Escribiendo...\" y \"Grabando...\" antes de enviar mensajes"}, {"key": "settings.whatsapp.typing_speed", "value": "Velocidad de Escritura (PPM)"}, {"key": "settings.whatsapp.randomness_factor", "value": "Factor de Aleatoriedad"}, {"key": "settings.whatsapp.consistent", "value": "Consistente"}, {"key": "settings.whatsapp.random", "value": "Aleat<PERSON>"}, {"key": "settings.whatsapp.min_delay", "value": "<PERSON><PERSON><PERSON> (segundos)"}, {"key": "settings.whatsapp.min_delay_desc", "value": "Tiempo mínimo para mostrar el indicador de escritura (0.5-10 segundos)"}, {"key": "settings.whatsapp.max_delay", "value": "<PERSON><PERSON><PERSON> (segundos)"}, {"key": "settings.whatsapp.max_delay_desc", "value": "Tiempo máximo para mostrar el indicador de escritura (1-30 segundos)"}, {"key": "settings.whatsapp.recording_min_delay", "value": "Retraso Mínimo de Grabación (segundos)"}, {"key": "settings.whatsapp.recording_min_delay_desc", "value": "Tiempo mínimo para mostrar el indicador de grabación (1-10 segundos)"}, {"key": "settings.whatsapp.recording_max_delay", "value": "Retraso Máximo de Grabación (segundos)"}, {"key": "settings.whatsapp.recording_max_delay_desc", "value": "Tiempo máximo para mostrar el indicador de grabación (2-15 segundos)"}, {"key": "settings.whatsapp.message_splitting", "value": "División de Mensajes"}, {"key": "settings.whatsapp.message_splitting_desc", "value": "Divide automáticamente los mensajes largos en partes más pequeñas y naturales"}, {"key": "settings.whatsapp.enable_splitting", "value": "Habilitar División de Mensajes"}, {"key": "settings.whatsapp.enable_splitting_desc", "value": "Divide las respuestas largas del bot en múltiples mensajes para una mejor legibilidad"}, {"key": "settings.whatsapp.max_message_length", "value": "<PERSON><PERSON><PERSON> Máxi<PERSON> del Mensaje"}, {"key": "settings.whatsapp.split_method", "value": "Método de División"}, {"key": "settings.whatsapp.by_sentences", "value": "Por Oraciones"}, {"key": "settings.whatsapp.by_paragraphs", "value": "<PERSON><PERSON>"}, {"key": "settings.whatsapp.by_characters", "value": "Por Caracteres"}, {"key": "settings.whatsapp.delay_between_chunks", "value": "Retraso Entre Fragmentos de Mensaje (segundos)"}, {"key": "settings.whatsapp.delay_between_chunks_desc", "value": "Tiempo entre fragmentos de mensajes divididos (0.5-10 segundos)"}, {"key": "settings.whatsapp.random_delay_factor", "value": "Factor de Retraso Aleatorio"}, {"key": "settings.whatsapp.min_chunk_size", "value": "<PERSON><PERSON><PERSON>"}, {"key": "settings.whatsapp.min_chunk_size_desc", "value": "Caracteres mínimos por fragmento para evitar mensajes muy cortos"}, {"key": "settings.whatsapp.preserve_formatting", "value": "Preservar <PERSON>ato"}, {"key": "settings.whatsapp.preserve_formatting_desc", "value": "Mantiene el formato markdown y de texto al dividir mensajes"}, {"key": "settings.whatsapp.smart_boundary", "value": "Detección de Límites Inteligente"}, {"key": "settings.whatsapp.smart_boundary_desc", "value": "Divide inteligentemente en los límites de oraciones y cláusulas"}, {"key": "settings.whatsapp.prioritize_sentences", "value": "Priorizar Límites de Oraciones"}, {"key": "settings.whatsapp.prioritize_sentences_desc", "value": "Prefiere dividir en finales de oración en lugar de límites de caracteres"}, {"key": "settings.whatsapp.preview", "value": "Vista Previa"}, {"key": "settings.whatsapp.preview_desc", "value": "Mira cómo tu configuración afectará la entrega de mensajes"}, {"key": "settings.whatsapp.sample_message", "value": "<PERSON><PERSON><PERSON>"}, {"key": "settings.whatsapp.sample_message_placeholder", "value": "Introduce un mensaje de muestra para ver cómo se dividirá y programará..."}, {"key": "settings.whatsapp.message_preview", "value": "Vista Previa del Mensaje"}, {"key": "settings.whatsapp.typing_time", "value": "Escribiendo ~{{time}}s"}, {"key": "settings.whatsapp.messages_count", "value": "{{count}} men<PERSON><PERSON>s"}, {"key": "settings.whatsapp.message_number", "value": "Mensaje {{current}} de {{total}}"}, {"key": "settings.whatsapp.characters_count", "value": "{{count}} caracteres"}, {"key": "settings.whatsapp.typing_delay", "value": "Retraso al escribir: ~{{time}}s"}, {"key": "settings.whatsapp.reset_defaults", "value": "Restablecer a Valores Predeterminados"}, {"key": "settings.whatsapp.reload_config", "value": "Recargar Configuración"}, {"key": "settings.whatsapp.save_settings", "value": "Guardar Configuración"}, {"key": "settings.whatsapp.settings_saved", "value": "Configuración Guardada"}, {"key": "settings.whatsapp.settings_saved_desc", "value": "La configuración del comportamiento de WhatsApp se ha actualizado correctamente."}, {"key": "settings.whatsapp.save_failed", "value": "Error al guardar la configuración del comportamiento de WhatsApp. Por favor, inténtalo de nuevo."}, {"key": "contacts.edit.contact_id_missing", "value": "Falta el ID del contacto"}, {"key": "contacts.edit.update_failed", "value": "Fallo al actualizar contacto"}, {"key": "contacts.edit.success_title", "value": "<PERSON>o <PERSON>"}, {"key": "contacts.edit.success_description", "value": "El contacto se ha actualizado correctamente."}, {"key": "contacts.edit.error_title", "value": "Fallo en la actualización"}, {"key": "contacts.edit.title", "value": "<PERSON><PERSON>"}, {"key": "contacts.edit.description", "value": "Realiza cambios en la información del contacto a continuación."}, {"key": "contacts.edit.name_label", "value": "Nombre"}, {"key": "contacts.edit.email_label", "value": "Correo Electrónico"}, {"key": "contacts.edit.phone_label", "value": "Teléfono"}, {"key": "contacts.edit.company_label", "value": "Empresa"}, {"key": "contacts.edit.channel_label", "value": "Canal"}, {"key": "contacts.edit.select_channel_placeholder", "value": "Selecciona canal"}, {"key": "contacts.edit.channel.whatsapp_official", "value": "WhatsApp Oficial"}, {"key": "contacts.edit.channel.whatsapp_unofficial", "value": "WhatsApp No Oficial"}, {"key": "contacts.edit.channel.messenger", "value": "Facebook Messenger"}, {"key": "contacts.edit.channel.instagram", "value": "Instagram"}, {"key": "contacts.edit.channel_identifier_label", "value": "Identificador de Canal"}, {"key": "contacts.edit.channel_identifier_placeholder", "value": "Número de teléfono o ID"}, {"key": "contacts.edit.tags_label", "value": "Etiquetas (separadas por comas)"}, {"key": "contacts.edit.tags_placeholder", "value": "cliente potencial, cliente, etc."}, {"key": "contacts.edit.notes_label", "value": "Notas"}, {"key": "contacts.edit.saving", "value": "Guardando..."}, {"key": "contacts.edit.save_changes", "value": "Guardar Cambios"}, {"key": "contacts.delete_contact", "value": "Eliminar <PERSON>o"}, {"key": "contacts.delete_warning", "value": "Esto eliminará permanentemente este contacto y todas las conversaciones, mensajes y notas asociadas. Esta acción no se puede deshacer."}, {"key": "contacts.message", "value": "Men<PERSON><PERSON>"}, {"key": "contacts.bulk_actions.selected_count", "value": "{{count}} contactos seleccionados"}, {"key": "contacts.bulk_actions.clear_selection", "value": "<PERSON><PERSON><PERSON>"}, {"key": "contacts.bulk_actions.deleting", "value": "Eliminando..."}, {"key": "contacts.bulk_actions.delete_selected", "value": "Eliminar Seleccionados"}, {"key": "contacts.bulk_actions.select_all", "value": "Seleccionar todos los contactos"}, {"key": "contacts.bulk_actions.select_contact", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON> contacto {{name}}"}, {"key": "contacts.bulk_delete.title", "value": "Eliminar {{count}} <PERSON><PERSON>"}, {"key": "contacts.bulk_delete.warning", "value": "Esto eliminará permanentemente estos contactos y todas las conversaciones, mensajes y notas asociadas. Esta acción no se puede deshacer."}, {"key": "contacts.bulk_delete.deleting", "value": "Eliminando..."}, {"key": "contacts.bulk_delete.confirm", "value": "Eliminar {{count}} <PERSON><PERSON>"}, {"key": "contacts.add.avatar_upload", "value": "Subir foto de contacto"}, {"key": "contacts.add.avatar_optional", "value": "Opcional - JPG, PNG hasta 5MB"}, {"key": "contacts.add.choose_photo", "value": "<PERSON>eg<PERSON>"}, {"key": "contacts.add.name_label", "value": "Nombre"}, {"key": "contacts.add.name_placeholder", "value": "Introduce el nombre del contacto"}, {"key": "contacts.add.email_label", "value": "Correo Electrónico"}, {"key": "contacts.add.email_placeholder", "value": "Introduce la dirección de correo electrónico"}, {"key": "contacts.add.phone_label", "value": "Teléfono"}, {"key": "contacts.add.phone_placeholder", "value": "+1234567890"}, {"key": "contacts.add.company_label", "value": "Empresa"}, {"key": "contacts.add.company_placeholder", "value": "Introduce el nombre de la empresa"}, {"key": "contacts.add.channel_label", "value": "Canal"}, {"key": "contacts.add.select_channel_placeholder", "value": "Selecciona canal"}, {"key": "contacts.add.channel.whatsapp_official", "value": "WhatsApp Oficial"}, {"key": "contacts.add.channel.whatsapp_unofficial", "value": "WhatsApp No Oficial"}, {"key": "contacts.add.channel.messenger", "value": "Facebook Messenger"}, {"key": "contacts.add.channel.instagram", "value": "Instagram"}, {"key": "contacts.add.channel_identifier_label", "value": "Identificador de Canal"}, {"key": "contacts.add.channel_identifier_placeholder", "value": "Número de teléfono o ID"}, {"key": "contacts.add.tags_label", "value": "Etiquetas"}, {"key": "contacts.add.tags_placeholder", "value": "Escribe etiquetas separadas por comas..."}, {"key": "contacts.add.notes_label", "value": "Notas"}, {"key": "contacts.add.notes_placeholder", "value": "Notas adicionales sobre este contacto..."}, {"key": "contacts.add.creating", "value": "Creando..."}, {"key": "contacts.add.create_button", "value": "<PERSON><PERSON><PERSON>"}, {"key": "contacts.add.name_required", "value": "Se requiere el nombre del contacto"}, {"key": "contacts.add.duplicate_phone", "value": "Ya existe un contacto con este número de teléfono: {{name}}"}, {"key": "contacts.import.title", "value": "Importar Contactos desde CSV"}, {"key": "contacts.import.description", "value": "Sube un archivo CSV para importar varios contactos a la vez. Descarga la plantilla para ver el formato requerido."}, {"key": "contacts.import.download_template", "value": "Descargar Plantilla"}, {"key": "contacts.import.file_label", "value": "Archivo CSV"}, {"key": "contacts.import.file_help", "value": "Tamaño máximo de archivo: 10MB. Solo se admiten archivos CSV."}, {"key": "contacts.import.preview_label", "value": "Vista Previa (primeras 5 filas)"}, {"key": "contacts.import.duplicate_handling_label", "value": "Man<PERSON>o <PERSON>"}, {"key": "contacts.import.duplicate.skip", "value": "<PERSON><PERSON><PERSON>"}, {"key": "contacts.import.duplicate.update", "value": "Actualizar existentes"}, {"key": "contacts.import.duplicate.create", "value": "<PERSON><PERSON><PERSON> nue<PERSON>"}, {"key": "contacts.import.duplicate_help", "value": "Cómo manejar contactos con direcciones de correo electrónico duplicadas"}, {"key": "contacts.import.importing", "value": "Importando..."}, {"key": "contacts.import.results_label", "value": "Resultados de Importación"}, {"key": "contacts.import.successful", "value": "Importados con éxito: {{count}}"}, {"key": "contacts.import.failed", "value": "Fallo al importar: {{count}}"}, {"key": "contacts.import.errors", "value": "Errores:"}, {"key": "contacts.import.more_errors", "value": "Y {{count}} errores más..."}, {"key": "contacts.import.import_button", "value": "Importar Contactos"}, {"key": "contacts.import.no_file_selected", "value": "Por favor, selecciona un archivo CSV para importar"}, {"key": "auth.signin_company_title", "value": "Inicia sesión en {{companyName}}"}, {"key": "auth.signin_company_description", "value": "Accede al espacio de trabajo de tu empresa en {{appName}}"}, {"key": "auth.signin_title", "value": "Accede como una sombra"}, {"key": "auth.signin_description", "value": "Unifica tus conversaciones con clientes en todos los canales en un CRM potente."}, {"key": "auth.get_started", "value": "<PERSON><PERSON><PERSON>"}, {"key": "auth.new_to_platform", "value": "¿Nuevo en la plataforma?"}, {"key": "auth.or_sign_in_with", "value": "O inicia sesión con"}, {"key": "auth.forgot_password_title", "value": "¿Olvidaste tu contraseña?"}, {"key": "auth.forgot_password_description", "value": "Introduce tu dirección de correo electrónico y te enviaremos un enlace para restablecer tu contraseña."}, {"key": "auth.email_address", "value": "Dirección de Correo Electrónico"}, {"key": "auth.email_address_placeholder", "value": "Introduce tu dirección de correo electrónico"}, {"key": "auth.send_reset_link", "value": "Enviar enlace de restablecimiento"}, {"key": "auth.back_to_login", "value": "Volver al inicio de sesión"}, {"key": "auth.check_email_title", "value": "Revisa tu correo electrónico"}, {"key": "auth.check_email_description", "value": "Hemos enviado un enlace para restablecer tu contraseña a tu dirección de correo electrónico."}, {"key": "auth.reset_email_sent_message", "value": "Si existe una cuenta con esa dirección de correo electrónico, recibirás un enlace de restablecimiento de contraseña en breve."}, {"key": "auth.didnt_receive_email", "value": "¿No recibiste el correo electrónico?"}, {"key": "auth.resend_email", "value": "Reenviar correo electrónico"}, {"key": "auth.reset_email_sent", "value": "Correo de restablecimiento enviado"}, {"key": "auth.failed_send_reset", "value": "Fallo al enviar correo de restablecimiento"}, {"key": "auth.enter_email_error", "value": "Por favor, introduce tu dirección de correo electrónico"}, {"key": "registration.error.register_failed", "value": "Error al registrar la empresa"}, {"key": "registration.success_title", "value": "¡Registro Exitoso!"}, {"key": "registration.approval_required", "value": "El registro de su empresa ha sido enviado para aprobación. Recibirá un correo electrónico una vez aprobado."}, {"key": "registration.success_desc", "value": "Su empresa ha sido registrada correctamente. Ahora puede iniciar sesión."}, {"key": "registration.failed_title", "value": "Fallo en el Registro"}, {"key": "registration.registering", "value": "Registrando..."}, {"key": "registration.register_button", "value": "Registrar Empresa"}, {"key": "admin.forgot_password_title", "value": "Restablecimiento de Contraseña de Administrador"}, {"key": "admin.forgot_password_description", "value": "Introduce tu dirección de correo electrónico de administrador y te enviaremos un enlace seguro de restablecimiento."}, {"key": "admin.email_address", "value": "Dirección de Correo Electrónico del Administrador"}, {"key": "admin.email_placeholder", "value": "Introduce tu dirección de correo electrónico de administrador"}, {"key": "admin.send_reset_link", "value": "Enviar enlace de restablecimiento de administrador"}, {"key": "admin.back_to_login", "value": "Volver al inicio de sesión del administrador"}, {"key": "admin.reset_email_sent_message", "value": "Por razones de seguridad, los enlaces de restablecimiento de contraseña de administrador solo se envían a direcciones de correo electrónico de administrador verificadas."}, {"key": "inbox.individual", "value": "Individual"}, {"key": "inbox.groups", "value": "Grupos"}, {"key": "inbox.conversations", "value": "Conversaciones"}, {"key": "inbox.filtered_by_channel", "value": "Filtrado por canal"}, {"key": "inbox.clear_channel_filter", "value": "Borrar filtro de canal"}, {"key": "inbox.loading_more_conversations", "value": "Cargando más conversaciones..."}, {"key": "inbox.load_more_conversations", "value": "<PERSON><PERSON>ás Conversaciones"}, {"key": "inbox.all_conversations_loaded", "value": "Todas las conversaciones cargadas"}, {"key": "messages.input.type_message", "value": "Escribe un mensaje..."}, {"key": "messages.input.send_message", "value": "<PERSON><PERSON><PERSON> men<PERSON>"}, {"key": "message_bubble.contact_avatar", "value": "Avatar de contacto"}, {"key": "message_bubble.download_file_failed", "value": "Error al descargar archivo al dispositivo"}, {"key": "message_bubble.delete_failed", "value": "Fallo al eliminar mensaje"}, {"key": "message_bubble.message_deleted", "value": "<PERSON><PERSON><PERSON> eliminado"}, {"key": "message_bubble.delete_success", "value": "El mensaje se ha eliminado correctamente"}, {"key": "message_bubble.delete_failed_title", "value": "Fallo en la eliminación"}, {"key": "new_conversation.channel_unofficial", "value": "No Oficial"}, {"key": "new_conversation.channel_official", "value": "API de Negocios"}, {"key": "new_conversation.name_required", "value": "Se requiere el nombre del contacto"}, {"key": "new_conversation.phone_required", "value": "Se requiere el número de teléfono"}, {"key": "new_conversation.phone_invalid", "value": "Por favor, introduce un número de teléfono válido con al menos 10 dígitos"}, {"key": "new_conversation.connection_required", "value": "Por favor, selecciona una conexión de WhatsApp"}, {"key": "new_conversation.no_connections", "value": "No se encontraron conexiones activas de WhatsApp. Por favor, conecta WhatsApp en Configuración primero."}, {"key": "new_conversation.create_failed", "value": "Fallo al crear conversación"}, {"key": "new_conversation.success_message", "value": "Conversación de WhatsApp iniciada correctamente."}, {"key": "new_conversation.create_error", "value": "Fallo al crear conversación. Por favor, inténtalo de nuevo."}, {"key": "new_conversation.no_connection_selected", "value": "No Hay Conexión Seleccionada"}, {"key": "new_conversation.select_connection", "value": "Por favor, selecciona una conexión de WhatsApp."}, {"key": "new_conversation.title", "value": "Iniciar Nueva Conversación de WhatsApp"}, {"key": "new_conversation.description", "value": "Introduce los detalles del contacto para iniciar una nueva conversación de WhatsApp."}, {"key": "new_conversation.contact_name_required", "value": "Nombre del Contacto *"}, {"key": "new_conversation.enter_contact_name", "value": "Introduce el nombre completo del contacto"}, {"key": "new_conversation.phone_number_required", "value": "Número de Teléfono *"}, {"key": "new_conversation.enter_phone_number", "value": "Introduce el número de teléfono (ej., +1234567890)"}, {"key": "new_conversation.include_country_code", "value": "Incluye el código de país para números internacionales"}, {"key": "new_conversation.whatsapp_connection_required", "value": "Conexión de WhatsApp *"}, {"key": "new_conversation.loading_connections", "value": "Cargando conexiones..."}, {"key": "new_conversation.select_whatsapp_connection", "value": "Selecciona la conexión de WhatsApp"}, {"key": "new_conversation.no_connections_available", "value": "No hay conexiones activas de WhatsApp disponibles"}, {"key": "new_conversation.initial_message_optional", "value": "Mensaje <PERSON> (Opcional)"}, {"key": "new_conversation.enter_initial_message", "value": "Introduce un primer mensaje opcional para enviar..."}, {"key": "new_conversation.initial_message_help", "value": "Este mensaje se enviará inmediatamente después de crear la conversación"}, {"key": "new_conversation.creating", "value": "Creando..."}, {"key": "new_conversation.start_conversation", "value": "Iniciar Conversación"}, {"key": "messages.input.recording_too_short", "value": "La grabación es demasiado corta o está vacía"}, {"key": "messages.input.voice_message_sent", "value": "Mensaje de voz enviado"}, {"key": "messages.input.voice_message_failed", "value": "Error al enviar mensaje de voz"}, {"key": "message_input.reply_failed", "value": "Fallo al enviar respuesta"}, {"key": "messages.input.recording_permission_denied", "value": "Permiso de micrófono denegado"}, {"key": "messages.input.recording_not_supported", "value": "La grabación de voz no es compatible en este navegador"}, {"key": "messages.input.recording_error", "value": "Error al acceder al micrófono"}, {"key": "messages.input.start_recording", "value": "Comenzar a grabar"}, {"key": "messages.input.stop_recording", "value": "Detener grabación"}, {"key": "messages.input.cancel_recording", "value": "Cancelar grabación"}, {"key": "messages.input.send_recording", "value": "Enviar grabación"}, {"key": "messages.input.recording_duration", "value": "Grabación: {{duration}}"}, {"key": "messages.input.emoji_picker", "value": "Abrir selector de emojis"}, {"key": "messages.input.attach_media", "value": "Adjuntar multimedia"}, {"key": "messages.input.quick_replies", "value": "Respuestas rápidas"}, {"key": "messages.input.cancel_reply", "value": "<PERSON><PERSON><PERSON> respuesta"}, {"key": "messages.input.replying_to", "value": "Respondiendo a {{name}}"}, {"key": "settings.google_calendar_disconnected", "value": "Google Calendar Desconectado"}, {"key": "settings.google_calendar_disconnect_success", "value": "Tu cuenta de Google Calendar se ha desconectado correctamente."}, {"key": "settings.google_calendar_disconnect_error", "value": "Error al desconectar Google Calendar: {{error}}"}, {"key": "settings.google_calendar_credentials_updated", "value": "Credenciales de Google Calendar Actualizadas"}, {"key": "settings.google_calendar_credentials_success", "value": "Tus credenciales de Google OAuth se han actualizado correctamente."}, {"key": "settings.google_calendar_credentials_error", "value": "Error al actualizar las credenciales de Google Calendar: {{error}}"}, {"key": "settings.account_updated", "value": "Cuenta Actualizada"}, {"key": "settings.account_updated_success", "value": "La configuración de tu cuenta se ha guardado correctamente"}, {"key": "settings.api_key_empty_error", "value": "La clave API no puede estar vacía"}, {"key": "settings.api_key_saved", "value": "Clave API Guardada"}, {"key": "settings.api_key_updated", "value": "Tu clave API se ha actualizado"}, {"key": "settings.subscription_updated", "value": "Suscripción Actualizada"}, {"key": "settings.subscription_updated_success", "value": "Tu suscripción se ha actualizado correctamente"}, {"key": "settings.whatsapp_connected", "value": "<PERSON>s<PERSON><PERSON>"}, {"key": "settings.whatsapp_connected_success", "value": "¡Tu cuenta de WhatsApp se ha conectado correctamente!"}, {"key": "settings.connection_error", "value": "Error de Conexión"}, {"key": "settings.tabs.channel_connections", "value": "Conexiones de Canal"}, {"key": "settings.tabs.channels", "value": "Canales"}, {"key": "settings.tabs.inbox_settings", "value": "Configuración de Bandeja de Entrada"}, {"key": "settings.tabs.inbox", "value": "Bandeja de Entrada"}, {"key": "settings.tabs.whatsapp_behavior", "value": "Comportamiento de WhatsApp"}, {"key": "settings.tabs.whatsapp", "value": "WhatsApp"}, {"key": "settings.tabs.billing", "value": "Facturación"}, {"key": "settings.tabs.team_members", "value": "Miembros del Equipo"}, {"key": "settings.tabs.team", "value": "Equipo"}, {"key": "settings.tabs.api_access", "value": "Acceso API"}, {"key": "settings.tabs.api", "value": "API"}, {"key": "settings.tabs.ai_credentials", "value": "Credenciales IA"}, {"key": "settings.tabs.ai_keys", "value": "Claves IA"}, {"key": "settings.tabs.ai_usage", "value": "Uso IA"}, {"key": "settings.tabs.usage", "value": "<PERSON><PERSON>"}, {"key": "settings.tabs.platform", "value": "Plataforma"}, {"key": "settings.channel_connections.title", "value": "Conexiones de Canal"}, {"key": "settings.channel_connections.description", "value": "Conecta y gestiona tus canales de comunicación"}, {"key": "settings.billing.title", "value": "Facturación y Suscripción"}, {"key": "settings.billing.description", "value": "Gestiona tu plan de suscripción y métodos de pago"}, {"key": "settings.team.title", "value": "Miembros del Equipo"}, {"key": "settings.team.description", "value": "Gestiona los miembros del equipo y sus permisos"}, {"key": "settings.platform.title", "value": "Configuración de Plataforma"}, {"key": "settings.platform.description", "value": "Configura integraciones de API de socios y credenciales de Proveedor Técnico a nivel de plataforma"}, {"key": "flow_builder.http_request", "value": "Solicitud HTTP"}, {"key": "flow_builder.http_auth", "value": "Autenticación"}, {"key": "flow_builder.http_header", "value": "encabezado"}, {"key": "flow_builder.http_body_configured", "value": "<PERSON><PERSON><PERSON> configurado"}, {"key": "flow_builder.http_retry", "value": "Reintentar"}, {"key": "flow_builder.http_mapping", "value": "mapeo"}, {"key": "flow_builder.http_quick_templates", "value": "Plant<PERSON><PERSON>"}, {"key": "flow_builder.http_choose_template", "value": "Elegir una plantilla..."}, {"key": "flow_builder.http_method", "value": "Método HTTP"}, {"key": "flow_builder.http_select_method", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"key": "flow_builder.http_request_url", "value": "URL de Solicitud"}, {"key": "flow_builder.webhook", "value": "Webhook"}, {"key": "flow_builder.webhook_no_url", "value": "No hay URL configurada"}, {"key": "flow_builder.webhook_timeout_error", "value": "La solicitud se agotó después de {{timeout}} segundos"}, {"key": "flow_builder.webhook_unknown_error", "value": "Ocurrió un error desconocido"}, {"key": "flow_builder.ai_elevenlabs_api_key", "value": "Clave API de ElevenLabs"}, {"key": "flow_builder.ai_elevenlabs_api_key_placeholder", "value": "Introduce la clave API de ElevenLabs..."}, {"key": "flow_builder.ai_elevenlabs_required", "value": "Requerido para ElevenLabs TTS"}, {"key": "flow_builder.ai_elevenlabs_model", "value": "<PERSON><PERSON>"}, {"key": "flow_builder.ai_elevenlabs_select_model", "value": "Selecciona modelo..."}, {"key": "flow_builder.ai_elevenlabs_stability", "value": "Estabilidad ({{value}})"}, {"key": "flow_builder.ai_elevenlabs_similarity", "value": "Similitud ({{value}})"}, {"key": "flow_builder.ai_elevenlabs_speaker_boost", "value": "Impulso <PERSON>"}, {"key": "flow_builder.ai_voice_response_mode_placeholder", "value": "Selecciona modo..."}, {"key": "flow_builder.ai_share_document_name", "value": "Compartir Documento"}, {"key": "flow_builder.ai_share_document_desc", "value": "Comparte un documento o archivo con el usuario cuando lo solicite"}, {"key": "flow_builder.ai_book_appointment_name", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flow_builder.ai_book_appointment_desc", "value": "Reserva un nuevo evento en Google Calendar"}, {"key": "flow_builder.ai_function_book_appointment", "value": "Crea un nuevo evento/cita en Google Calendar. Úsalo cuando el usuario quiera programar una reunión o cita."}, {"key": "flow_builder.ai_function_param_title", "value": "Título/resumen de la cita"}, {"key": "flow_builder.ai_function_param_description", "value": "Descripción detallada de la cita"}, {"key": "flow_builder.ai_function_param_start_datetime", "value": "Fecha y hora de inicio en formato ISO (AAAA-MM-DDTHH:MM:SS)"}, {"key": "flow_builder.ai_function_param_end_datetime", "value": "Fecha y hora de finalización en formato ISO (AAAA-MM-DDTHH:MM:SS)"}, {"key": "flow_builder.ai_function_param_attendee_emails", "value": "Direcciones de correo electrónico de los asistentes (opcional)"}, {"key": "flow_builder.ai_function_param_location", "value": "Ubicación de la cita (opcional)"}, {"key": "flow_builder.ai_function_param_document_type", "value": "Tipo de documento solicitado (folleto, manual, catálogo, etc.)"}, {"key": "flow_builder.ai_function_param_user_request", "value": "La solicitud original del usuario para el documento"}, {"key": "flow_builder.ai_task_active", "value": "Activo"}, {"key": "flow_builder.ai_task_inactive", "value": "Inactivo"}, {"key": "flow_builder.ai_task_name_label", "value": "Nombre de la Tarea"}, {"key": "flow_builder.ai_task_name_placeholder", "value": "ej., Compartir Folleto de Producto"}, {"key": "flow_builder.ai_function_name_label", "value": "Nombre de la Función"}, {"key": "flow_builder.ai_function_name_placeholder", "value": "ej., compartir_documento"}, {"key": "flow_builder.ai_task_description_label", "value": "Descripción de la Tarea"}, {"key": "flow_builder.ai_task_description_placeholder", "value": "Describe qué hace esta tarea y cuándo debe activarse"}, {"key": "flow_builder.ai_function_description_label", "value": "Descripción de la Función de IA"}, {"key": "flow_builder.ai_function_description_placeholder", "value": "Instrucciones detalladas para el modelo de IA sobre cuándo llamar a esta función. Sé específico sobre los requisitos de intención del usuario."}, {"key": "flow_builder.ai_function_description_tip", "value": "💡 Consejo: Usa frases como \"SOLO llama cuando el usuario solicite explícitamente...\" para evitar activaciones falsas"}, {"key": "flow_builder.ai_output_handle_label", "value": "ID del Manejador de Salida"}, {"key": "flow_builder.ai_output_handle_placeholder", "value": "ej., tarea_folleto"}, {"key": "flow_builder.ai_add_task", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "flow_builder.ai_no_tasks_configured", "value": "No hay tareas configuradas"}, {"key": "flow_builder.ai_no_tasks_help", "value": "Agrega tareas para habilitar las llamadas a funciones de IA y el enrutamiento de flujo"}, {"key": "flow_builder.ai_tasks_tip", "value": "💡 Consejo: Cada tarea activa crea un manejador de salida para el enrutamiento de flujo. Usa descripciones específicas para evitar activaciones falsas."}, {"key": "flow_builder.ai_task_execution_disabled_help", "value": "Habilita la ejecución de tareas para permitir llamadas a funciones de IA y enrutamiento de flujo avanzado"}, {"key": "flow_builder.ai_google_calendar_integration", "value": "Integración con Google Calendar"}, {"key": "flow_builder.ai_checking_connection", "value": "Comprobando conexión..."}, {"key": "flow_builder.ai_google_calendar_connected", "value": "Google Calendar Conectado"}, {"key": "flow_builder.ai_switch_account", "value": "Cambiar <PERSON>"}, {"key": "flow_builder.ai_switching", "value": "Cambiando..."}, {"key": "flow_builder.ai_disconnect", "value": "Desconectar"}, {"key": "flow_builder.ai_google_calendar_not_connected", "value": "Google Calendar No Conectado"}, {"key": "flow_builder.ai_connect_google_calendar", "value": "Conectar Google Calendar"}, {"key": "flow_builder.ai_connecting", "value": "Conectando..."}, {"key": "flow_builder.ai_calendar_features_available", "value": "La IA ahora puede: reservar citas, verificar disponibilidad, listar eventos, actualizar eventos y cancelar eventos."}, {"key": "flow_builder.ai_calendar_system_prompt_note", "value": "Nota: El comportamiento del calendario se controla mediante el prompt del sistema principal anterior."}, {"key": "flow_builder.ai_business_hours_start", "value": "<PERSON><PERSON><PERSON>al"}, {"key": "flow_builder.ai_business_hours_end", "value": "Fin Horario Laboral"}, {"key": "flow_builder.ai_appointment_duration", "value": "Duración de la Cita (minutos)"}, {"key": "flow_builder.ai_timezone", "value": "Zona Horaria"}, {"key": "flow_builder.ai_timezone_placeholder", "value": "Seleccionar zona horaria..."}, {"key": "flow_builder.ai_knowledge_base", "value": "Base de Conocimientos"}, {"key": "flow_builder.ai_rag_enhancement", "value": "Mejora RAG"}, {"key": "flow_builder.ai_knowledge_base_disabled_help", "value": "Habilita la base de conocimientos para mejorar las respuestas de IA con contexto basado en documentos utilizando RAG (Generación Aumentada por Recuperación)"}, {"key": "flow_builder.ai_google_calendar_disabled_help", "value": "Habilita la integración de Google Calendar para permitir que la IA administre citas y verifique la disponibilidad"}, {"key": "flow_builder.ai_tts_provider", "value": "Proveedor TTS"}, {"key": "flow_builder.ai_tts_provider_placeholder", "value": "Seleccionar proveedor TTS..."}, {"key": "flow_builder.ai_tts_provider_help", "value": "Elige tu proveedor de texto a voz"}, {"key": "flow_builder.ai_voice_selection", "value": "Selección de Voz"}, {"key": "flow_builder.ai_voice_selection_placeholder", "value": "Seleccionar voz..."}, {"key": "flow_builder.ai_voice_selection_help", "value": "Elige la voz para las respuestas de audio de la IA"}, {"key": "flow_builder.ai_custom_voice_id", "value": "ID de Voz Personalizada"}, {"key": "flow_builder.ai_custom_voice_id_placeholder", "value": "Pega aquí tu ID de voz de ElevenLabs..."}, {"key": "flow_builder.ai_custom_voice_id_help", "value": "Introduce un ID de voz personalizado de tu cuenta de ElevenLabs (ej., \"pNInz6obpgDQGcFmaJgB\")"}, {"key": "flow_builder.ai_voice_id_warning", "value": "⚠️ El ID de voz parece corto. Los IDs de voz de ElevenLabs suelen tener más de 20 caracteres."}, {"key": "flow_builder.ai_voice_id_tip", "value": "💡 Consejo: Puedes encontrar los IDs de voz en tu panel de ElevenLabs en \"Voces\" → Haz clic en una voz → Copia el ID de Voz"}, {"key": "flow_builder.ai_voice_processing_elevenlabs", "value": "Speech-to-Text usa OpenAI Whisper, Text-to-Speech usa API de ElevenLabs"}, {"key": "flow_builder.ai_voice_processing_openai", "value": "Tanto Speech-to-Text como Text-to-Speech usan APIs de OpenAI"}, {"key": "flow_builder.ai_voice_processing_label", "value": "Procesamiento de Voz:"}, {"key": "flow_builder.ai_audio_limits", "value": "Límites de Procesamiento de Audio"}, {"key": "flow_builder.ai_max_audio_duration", "value": "Duración Máxima de Audio (segundos)"}, {"key": "flow_builder.ai_max_audio_duration_help", "value": "Los mensajes de audio más largos que este límite no se transcribirán ni generarán respuestas TTS para ahorrar costos de API"}, {"key": "flow_builder.ai_max_duration_exceeded", "value": "La duración máxima permitida es de 30 segundos"}, {"key": "flow_builder.ai_min_duration_error", "value": "La duración mínima es de 1 segundo"}, {"key": "flow_builder.ai_cost_optimization_tip", "value": "💰 Optimización de Costos: Limitar la duración del audio evita llamadas API costosas para mensajes de voz largos. Los usuarios recibirán una respuesta de texto pidiéndoles que envíen mensajes más cortos."}, {"key": "flow_builder.ai_task_execution", "value": "Ejecución de Tareas"}, {"key": "flow_builder.ai_voice_processing_openai", "value": "Speech-to-Text y Text-to-Speech usan ambas APIs de OpenAI"}, {"key": "flow_builder.ai_default_system_prompt", "value": "Eres un asistente útil. Responde a las preguntas del usuario de forma concisa y precisa. Realiza acciones específicas solo cuando el usuario las solicita explícitamente."}, {"key": "flow_builder.ai_voice_only_mode", "value": "Solo Voz a Voz"}, {"key": "flow_builder.ai_voice_only_description", "value": "Genera respuestas de voz solo cuando el usuario envía un mensaje de voz"}, {"key": "flow_builder.ai_share_document_task_description", "value": "Cuando el usuario solicita compartir un documento, folleto o archivo"}, {"key": "flow_builder.ai_function_label", "value": "Función:"}, {"key": "flow_builder.ai_tts_openai_name", "value": "OpenAI"}, {"key": "flow_builder.ai_tts_openai_description", "value": "OpenAI TTS con Whisper STT"}, {"key": "flow_builder.ai_tts_elevenlabs_name", "value": "ElevenLabs"}, {"key": "flow_builder.ai_tts_elevenlabs_description", "value": "ElevenLabs TTS con OpenAI Whisper STT"}, {"key": "flow_builder.ai_voice_mode_always", "value": "Siempre"}, {"key": "flow_builder.ai_voice_mode_always_description", "value": "Genera respuestas de voz para todos los mensajes (texto y voz)"}, {"key": "flow_builder.ai_voice_mode_voice_only", "value": "Solo Voz a Voz"}, {"key": "flow_builder.ai_voice_mode_never", "value": "Nunca"}, {"key": "flow_builder.ai_voice_mode_never_description", "value": "Deshabilita las respuestas de voz (solo texto)"}, {"key": "flow_builder.ai_summary_history", "value": "Historial:"}, {"key": "flow_builder.ai_summary_tasks", "value": "Ta<PERSON><PERSON>:"}, {"key": "flow_builder.ai_summary_tts", "value": "TTS:"}, {"key": "flow_builder.ai_summary_audio", "value": "Audio:"}, {"key": "flow_builder.ai_summary_audio_max", "value": "{{duration}}s máx."}, {"key": "flow_builder.ai_history_help", "value": "Mensajes anteriores a incluir para el contexto"}, {"key": "flow_builder.ai_tts_help", "value": "Convierte las respuestas de IA en mensajes de voz"}, {"key": "flow_builder.n8n_name", "value": "n8n"}, {"key": "flow_builder.n8n_description", "value": "Ejecuta el flujo de trabajo de IA de chat n8n con soporte multimedia"}, {"key": "flow_builder.n8n_tooltip", "value": "Ejecuta un flujo de trabajo n8n con capacidades de chat de IA. Soporta texto, imágenes, videos, audio y documentos. Perfecto para agentes de IA, chatbots y flujos de trabajo conversacionales."}, {"key": "flow_builder.n8n_delete_node", "value": "Eliminar nodo"}, {"key": "flow_builder.n8n_workflow_integration", "value": "Integración de Automatización de Flujos de Trabajo n8n"}, {"key": "flow_builder.n8n_field_required", "value": "Este campo es obligatorio"}, {"key": "flow_builder.n8n_invalid_url", "value": "Por favor, introduce una URL válida (ej., https://tu-instancia-n8n.com)"}, {"key": "flow_builder.n8n_timeout_range", "value": "El tiempo de espera debe estar entre 1 y 300 segundos"}, {"key": "flow_builder.n8n_instance_url_required", "value": "Por favor, introduce la URL de tu instancia de n8n"}, {"key": "flow_builder.n8n_invalid_url_format", "value": "Formato de URL inválido. Por favor, introduce una URL válida (ej., https://tu-instancia.app.n8n.cloud)"}, {"key": "flow_builder.n8n_webhook_or_workflow_required", "value": "Se requiere la URL del webhook o (nombre del flujo de trabajo + clave API) para ejecutar el flujo de trabajo"}, {"key": "flow_builder.n8n_test_connection_failed", "value": "Fallo al probar la conexión. Por favor, revisa tu configuración e inténtalo de nuevo."}, {"key": "flow_builder.n8n_list_workflows_required", "value": "Por favor, introduce la URL de la instancia y la clave API para listar flujos de trabajo"}, {"key": "flow_builder.n8n_no_workflows_found", "value": "No se encontraron flujos de trabajo en tu instancia de n8n"}, {"key": "flow_builder.n8n_list_workflows_failed", "value": "Fallo al listar flujos de trabajo. Por favor, revisa tu configuración e inténtalo de nuevo."}, {"key": "flow_builder.n8n_workflow_selected", "value": "✓ Flujo de trabajo seleccionado: \"{{name}}\" (ID: {{id}})"}, {"key": "flow_builder.n8n_copy_failed", "value": "Fallo al copiar texto: "}, {"key": "flow_builder.n8n_configuration_completeness", "value": "Completitud de la configuración: {{progress}}%"}, {"key": "flow_builder.n8n_complete_required_fields", "value": "Completa los campos requeridos para alcanzar el 70%"}, {"key": "flow_builder.n8n_configuration_ready", "value": "¡Configuración lista!"}, {"key": "flow_builder.n8n_hide", "value": "Ocultar"}, {"key": "flow_builder.n8n_edit", "value": "<PERSON><PERSON>"}, {"key": "flow_builder.n8n_hide_configuration_panel", "value": "Ocultar panel de configuración"}, {"key": "flow_builder.n8n_show_configuration_panel", "value": "Mostrar panel de configuración"}, {"key": "flow_builder.n8n_configured", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "flow_builder.n8n_not_configured", "value": "No configurado"}, {"key": "flow_builder.n8n_connection_configured", "value": "La conexión n8n está configurada correctamente"}, {"key": "flow_builder.n8n_configure_connection", "value": "Por favor, configura la URL de la instancia de n8n y los detalles del flujo de trabajo"}, {"key": "flow_builder.n8n_workflow_label", "value": "Flujo de Trabajo:"}, {"key": "flow_builder.n8n_target_workflow", "value": "Flujo de trabajo de destino: {{name}}"}, {"key": "flow_builder.n8n_multimedia_support", "value": "Soporte Multimedia Habilitado"}, {"key": "flow_builder.n8n_supports", "value": "Soporta: {{types}}"}, {"key": "flow_builder.n8n_max_size", "value": "<PERSON><PERSON><PERSON> máximo: {{size}}MB"}, {"key": "flow_builder.n8n_api_configuration", "value": "Configuración API n8n"}, {"key": "flow_builder.n8n_configure_connection_help", "value": "Configura la conexión a tu instancia de n8n"}, {"key": "flow_builder.n8n_required_fields_marked", "value": "Los campos requeridos están marcados con *"}, {"key": "flow_builder.n8n_instance_url_label", "value": "URL de la Instancia *"}, {"key": "flow_builder.n8n_instance_url_help_title", "value": "La URL de tu instancia de n8n"}, {"key": "flow_builder.n8n_instance_url_examples", "value": "Ejemplos:"}, {"key": "flow_builder.n8n_instance_url_example1", "value": "• https://tu-n8n.ejemplo.com"}, {"key": "flow_builder.n8n_instance_url_example2", "value": "• https://n8n.tuempresa.com"}, {"key": "flow_builder.n8n_instance_url_example3", "value": "• http://localhost:5678 (para local)"}, {"key": "flow_builder.n8n_instance_url_placeholder", "value": "https://tu-instancia-n8n.com"}, {"key": "flow_builder.n8n_copied", "value": "¡Copiado!"}, {"key": "flow_builder.n8n_copy_url_clipboard", "value": "Copiar URL al portapapeles"}, {"key": "flow_builder.n8n_api_key_label", "value": "Clave API"}, {"key": "flow_builder.n8n_optional", "value": "Opcional"}, {"key": "flow_builder.n8n_api_key_help_title", "value": "Clave API de n8n (opcional)"}, {"key": "flow_builder.n8n_api_key_help_find", "value": "Cómo encontrar tu clave API:"}, {"key": "flow_builder.n8n_api_key_step1", "value": "1. Ve a Configuración de n8n → API"}, {"key": "flow_builder.n8n_api_key_step2", "value": "2. <PERSON>rea una nueva clave API"}, {"key": "flow_builder.n8n_api_key_step3", "value": "3. <PERSON><PERSON> y pégala aquí"}, {"key": "flow_builder.n8n_api_key_required_private", "value": "Requerido para instancias privadas"}, {"key": "flow_builder.n8n_api_key_placeholder", "value": "n8n_api_key_..."}, {"key": "flow_builder.n8n_workflow_id_name_label", "value": "ID/Nombre del Flujo de Trabajo *"}, {"key": "flow_builder.n8n_workflow_identifier_help_title", "value": "Identificador del flujo de trabajo de destino"}, {"key": "flow_builder.n8n_workflow_identifier_help_use", "value": "Puedes usar cualquiera de los dos:"}, {"key": "flow_builder.n8n_workflow_identifier_id", "value": "• ID de Flujo de Trabajo (numérico)"}, {"key": "flow_builder.n8n_workflow_identifier_name", "value": "• Nombre de Flujo de Trabajo (texto)"}, {"key": "flow_builder.n8n_workflow_identifier_find", "value": "Encuentra esto en tu lista de flujos de trabajo de n8n"}, {"key": "flow_builder.n8n_workflow_placeholder", "value": "mi-flujo o 123"}, {"key": "flow_builder.n8n_chat_webhook_url_label", "value": "URL de Webhook de Chat"}, {"key": "flow_builder.n8n_webhook_help_title", "value": "URL directa al webhook de activación de chat de tu n8n"}, {"key": "flow_builder.n8n_webhook_help_use", "value": "Úsalo si la detección automática de webhook falla"}, {"key": "flow_builder.n8n_webhook_help_example", "value": "Ejemplo: https://n8n.com/webhook/tu-webhook-id/chat"}, {"key": "flow_builder.n8n_webhook_help_priority", "value": "Tiene prioridad sobre la detección automática"}, {"key": "flow_builder.n8n_webhook_placeholder", "value": "https://n8n.com/webhook/tu-webhook-id/chat"}, {"key": "flow_builder.n8n_webhook_direct_url", "value": "✓ Se utilizará la URL directa del webhook"}, {"key": "flow_builder.n8n_timeout_label", "value": "Tiempo de Espera (segundos)"}, {"key": "flow_builder.n8n_timeout_help_title", "value": "Tiempo de espera de la solicitud"}, {"key": "flow_builder.n8n_timeout_help_description", "value": "Tiempo máximo para esperar la respuesta de n8n"}, {"key": "flow_builder.n8n_timeout_help_range", "value": "Rango: 1-300 segundos"}, {"key": "flow_builder.n8n_timeout_help_default", "value": "Predeterminado: 30 segundos"}, {"key": "flow_builder.n8n_executing", "value": "Ejecutando..."}, {"key": "flow_builder.n8n_execute_workflow", "value": "Ejecutar Flujo de Trabajo"}, {"key": "flow_builder.n8n_execute_workflow_help", "value": "Ejecuta tu flujo de trabajo n8n"}, {"key": "flow_builder.n8n_enter_url_to_enable", "value": "Introduce la URL de tu instancia de n8n para habilitar la ejecución"}, {"key": "flow_builder.n8n_execute_via_webhook", "value": "Ejecutar flujo de trabajo a través de URL de webhook"}, {"key": "flow_builder.n8n_execute_via_api", "value": "Ejecutar flujo de trabajo a través de API de n8n"}, {"key": "flow_builder.n8n_enter_workflow_api_key", "value": "Introduce el nombre del flujo de trabajo y la clave API para ejecutar a través de API"}, {"key": "flow_builder.n8n_list_workflows_help", "value": "Lista los flujos de trabajo para encontrar el ID/nombre correcto"}, {"key": "flow_builder.n8n_open_api_docs", "value": "Abrir documentación de la API de n8n"}, {"key": "flow_builder.n8n_media_configuration", "value": "Configuración Multimedia"}, {"key": "flow_builder.n8n_multimedia_support_help_title", "value": "Soporte de Mensajes Multimedia"}, {"key": "flow_builder.n8n_multimedia_support_help_description", "value": "Configura cómo tu flujo de trabajo n8n maneja imágenes, videos, audio y documentos de usuarios de WhatsApp."}, {"key": "flow_builder.n8n_input_support_title", "value": "📥 Soporte de Entrada:"}, {"key": "flow_builder.n8n_input_support_receive", "value": "• Recibe archivos multimedia con URLs y metadatos"}, {"key": "flow_builder.n8n_input_support_automatic", "value": "• Detección y validación automática del tipo de archivo"}, {"key": "flow_builder.n8n_input_support_enhanced", "value": "• Payload de mensaje mejorado con información multimedia"}, {"key": "flow_builder.n8n_output_support_title", "value": "📤 Soporte de Salida:"}, {"key": "flow_builder.n8n_output_support_return", "value": "• Devuelve URLs multimedia en la respuesta de n8n"}, {"key": "flow_builder.n8n_output_support_automatic", "value": "• Generación automática de mensajes multimedia"}, {"key": "flow_builder.n8n_enable_media_support", "value": "Habilitar Soporte Multimedia"}, {"key": "flow_builder.n8n_enable_media_support_help", "value": "Permite que el flujo de trabajo n8n reciba y procese mensajes multimedia"}, {"key": "flow_builder.n8n_supported_media_types", "value": "Tipos de Multimedia Soportados"}, {"key": "flow_builder.n8n_input_support_validation", "value": "• Validación de tipo de archivo y límites de tamaño"}, {"key": "flow_builder.n8n_output_support_multiple", "value": "• Soporte para múltiples archivos multimedia adjuntos"}, {"key": "flow_builder.n8n_output_support_delivery", "value": "• Entrega automática de multimedia de WhatsApp"}, {"key": "flow_builder.n8n_max_file_size_label", "value": "Tamaño Máximo de Archivo (MB)"}, {"key": "flow_builder.n8n_media_processing_mode_label", "value": "Modo de Procesamiento Multimedia"}, {"key": "flow_builder.n8n_url_only", "value": "Solo URL"}, {"key": "flow_builder.n8n_url_only_description", "value": "Envía la URL multimedia al flujo de trabajo n8n"}, {"key": "flow_builder.n8n_url_metadata", "value": "URL + Metadatos"}, {"key": "flow_builder.n8n_url_metadata_description", "value": "Incluye tamaño de archivo, tipo y metadatos"}, {"key": "flow_builder.n8n_include_file_metadata", "value": "Incluir Metadatos del Archivo"}, {"key": "flow_builder.n8n_include_file_metadata_help", "value": "Incluye tipo MIME, tamaño de archivo y nombre de archivo original"}, {"key": "flow_builder.n8n_integration_examples", "value": "📋 Ejemplos de Integración"}, {"key": "flow_builder.n8n_input_payload_structure", "value": "Estructura del Payload de Entrada:"}, {"key": "flow_builder.n8n_response_format", "value": "Formato de Respuesta:"}, {"key": "flow_builder.n8n_select_workflow", "value": "Seleccionar Flujo de Trabajo"}, {"key": "flow_builder.n8n_workflow_id_name", "value": "ID: {{id}} - Nombre: {{name}}"}, {"key": "flow_builder.n8n_workflow_active", "value": "Activo: {{status}}"}, {"key": "flow_builder.n8n_yes", "value": "Sí"}, {"key": "flow_builder.n8n_no", "value": "No"}, {"key": "flow_builder.n8n_no_workflows_found_title", "value": "No se encontraron flujos de trabajo"}, {"key": "flow_builder.n8n_create_workflows_first", "value": "Crea primero flujos de trabajo en tu instancia de n8n"}, {"key": "flow_builder.n8n_click_workflow_select", "value": "Haz clic en un flujo de trabajo para seleccionarlo y completar el campo ID/Nombre del Flujo de Trabajo."}, {"key": "flow_builder.n8n_ensure_instance_running", "value": "Asegúrate de que tu instancia de n8n esté en ejecución y contenga flujos de trabajo."}, {"key": "flow_builder.n8n_connection_successful", "value": "Conexión Exitosa"}, {"key": "flow_builder.n8n_connection_failed", "value": "Fallo en la Conexión"}, {"key": "flow_builder.trigger_default_reset_message", "value": "El bot ha sido reactivado. Iniciando nueva conversación..."}, {"key": "flow_builder.trigger_delete_node", "value": "Eliminar nodo"}, {"key": "flow_builder.trigger_channel_whatsapp_unofficial", "value": "WhatsApp (No Oficial)"}, {"key": "flow_builder.trigger_channel_whatsapp_official", "value": "WhatsApp (Oficial)"}, {"key": "flow_builder.trigger_channel_whatsapp_twilio", "value": "WhatsApp (Twilio)"}, {"key": "flow_builder.trigger_channel_whatsapp_360dialog", "value": "WhatsApp (360Dialog)"}, {"key": "flow_builder.trigger_channel_messenger", "value": "Facebook Messenger"}, {"key": "flow_builder.trigger_channel_instagram", "value": "Instagram"}, {"key": "flow_builder.trigger_channel_email", "value": "Correo Electrónico"}, {"key": "flow_builder.trigger_channel_telegram", "value": "Telegram"}, {"key": "flow_builder.trigger_more_keywords", "value": "+{{count}} más"}, {"key": "flow_builder.trigger_condition_type", "value": "Tipo de Condición"}, {"key": "flow_builder.trigger_word_phrase", "value": "Palabra o Frase"}, {"key": "flow_builder.trigger_exact_text", "value": "Texto Exacto"}, {"key": "flow_builder.trigger_multiple_keywords", "value": "<PERSON><PERSON><PERSON><PERSON> Palabras <PERSON>"}, {"key": "flow_builder.trigger_pattern", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flow_builder.trigger_keywords_help", "value": "Introduce palabras clave separadas por comas. El disparador se activará cuando se detecte alguna de estas palabras clave en un mensaje."}, {"key": "flow_builder.trigger_keywords_label", "value": "Palabras Clave:"}, {"key": "flow_builder.trigger_keywords_required", "value": "Se requiere al menos una palabra clave"}, {"key": "flow_builder.trigger_case_sensitive", "value": "Coincidencia sensible a mayúsculas/minúsculas"}, {"key": "flow_builder.trigger_enable", "value": "Habilitar"}, {"key": "flow_builder.trigger_minutes", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flow_builder.trigger_hours", "value": "<PERSON><PERSON>"}, {"key": "flow_builder.trigger_days", "value": "Días"}, {"key": "flow_builder.trigger_condition_contains", "value": "contiene"}, {"key": "flow_builder.trigger_condition_exactly_matches", "value": "coincide exactamente con"}, {"key": "flow_builder.trigger_condition_contains_any", "value": "contiene cualquiera de"}, {"key": "flow_builder.trigger_condition_matches_pattern", "value": "coincide con el patrón"}, {"key": "flow_builder.trigger_condition_has_media", "value": "tiene archivo multimedia"}, {"key": "flow_builder.trigger_placeholder_contains", "value": "ayuda, soporte, etc."}, {"key": "flow_builder.trigger_placeholder_exact", "value": "<PERSON>la mundo"}, {"key": "flow_builder.trigger_placeholder_keywords", "value": "Introduce palabras clave separadas por comas (ej., ayuda, soporte, agente)"}, {"key": "flow_builder.trigger_placeholder_regex", "value": "\\b\\w+\\b"}, {"key": "flow_builder.flowise_delete_node", "value": "Eliminar nodo"}, {"key": "flow_builder.flowise_integration", "value": "Integración Flowise"}, {"key": "flow_builder.flowise_no_chatflow_selected", "value": "Ningún flujo de chat seleccionado"}, {"key": "flow_builder.flowise_flow_label", "value": "Flujo:"}, {"key": "flow_builder.flowise_config_single", "value": "{{count}} configuración"}, {"key": "flow_builder.flowise_config_plural", "value": "{{count}} configuraciones"}, {"key": "flow_builder.flowise_mapping_single", "value": "{{count}} mapeo"}, {"key": "flow_builder.flowise_mapping_plural", "value": "{{count}} mapeos"}, {"key": "flow_builder.flowise_api_connected", "value": "API Conectada"}, {"key": "flow_builder.flowise_timeout_label", "value": "Tiempo de espera: {{timeout}}s"}, {"key": "flow_builder.flowise_quick_templates", "value": "Plant<PERSON><PERSON>"}, {"key": "flow_builder.flowise_choose_operation", "value": "Elige una operación de Flowise..."}, {"key": "flow_builder.flowise_template_start_ai_chat", "value": "Iniciar <PERSON>"}, {"key": "flow_builder.flowise_template_send_query", "value": "Enviar Consulta IA"}, {"key": "flow_builder.flowise_template_get_response", "value": "Obtener Respuesta IA"}, {"key": "flow_builder.flowise_template_stream_chat", "value": "Transmitir Chat IA"}, {"key": "flow_builder.make_delete_node", "value": "Eliminar nodo"}, {"key": "flow_builder.make_integration", "value": "Integración de Automatización de Flujos de Trabajo Make.com"}, {"key": "flow_builder.make_configuration_completeness", "value": "Completitud de la configuración: {{progress}}%"}, {"key": "flow_builder.make_complete_required_fields", "value": "Completa los campos requeridos para alcanzar el 70%"}, {"key": "flow_builder.make_configuration_ready", "value": "¡Configuración lista!"}, {"key": "flow_builder.make_name", "value": "Make.com"}, {"key": "flow_builder.make_description", "value": "Ejecuta el escenario Make.com con soporte multimedia"}, {"key": "flow_builder.make_tooltip", "value": "Ejecuta un escenario Make.com con capacidades de chat de IA. Soporta texto, imágenes, videos, audio y documentos. Perfecto para agentes de IA, chatbots y flujos de trabajo conversacionales."}, {"key": "flow_builder.make_copy_failed", "value": "Fallo al copiar texto: "}, {"key": "flow_builder.make_field_required", "value": "Este campo es obligatorio"}, {"key": "flow_builder.make_invalid_url", "value": "Por favor, introduce una URL válida (ej., https://hook.make.com/...)"}, {"key": "flow_builder.make_timeout_range", "value": "El tiempo de espera debe estar entre 1 y 300 segundos"}, {"key": "flow_builder.make_api_token_required_test", "value": "Se requiere token API para probar"}, {"key": "flow_builder.make_test_connection_failed", "value": "Fallo al probar la conexión. Por favor, revisa tu red e inténtalo de nuevo."}, {"key": "flow_builder.make_api_token_required_list", "value": "Se requiere token API para listar escenarios"}, {"key": "flow_builder.make_list_scenarios_failed", "value": "Fallo al obtener escenarios"}, {"key": "flow_builder.make_list_scenarios_network_error", "value": "Fallo al obtener escenarios. Por favor, revisa tu red e inténtalo de nuevo."}, {"key": "flow_builder.make_hide", "value": "Ocultar"}, {"key": "flow_builder.make_edit", "value": "<PERSON><PERSON>"}, {"key": "flow_builder.make_hide_configuration_panel", "value": "Ocultar panel de configuración"}, {"key": "flow_builder.make_show_configuration_panel", "value": "Mostrar panel de configuración"}, {"key": "flow_builder.make_configured", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "flow_builder.make_not_configured", "value": "No configurado"}, {"key": "flow_builder.make_connection_configured", "value": "La conexión Make.com está configurada correctamente"}, {"key": "flow_builder.make_configure_connection", "value": "Por favor, configura el token API de Make.com y los detalles del escenario"}, {"key": "flow_builder.make_scenario_label", "value": "Escenario:"}, {"key": "flow_builder.make_target_scenario", "value": "Escenario de destino: {{name}}"}, {"key": "flow_builder.make_webhook_connected", "value": "Webhook Conectado"}, {"key": "flow_builder.make_webhook_url_configured", "value": "URL de Webhook Configurada"}, {"key": "flow_builder.make_timeout_label", "value": "Tiempo de espera: {{timeout}}s"}, {"key": "flow_builder.make_request_timeout", "value": "Tiempo de espera de solicitud: {{timeout}} segundos"}, {"key": "flow_builder.make_max_wait_time", "value": "Tiempo máximo de espera para la respuesta de Make.com"}, {"key": "flow_builder.make_multimedia_support", "value": "📎 Soporte Multimedia"}, {"key": "flow_builder.make_multimedia_enabled", "value": "Soporte multimedia habilitado"}, {"key": "flow_builder.make_supports_types", "value": "Soporta: {{types}}"}, {"key": "flow_builder.make_max_size", "value": "<PERSON><PERSON><PERSON> máximo: {{size}}MB"}, {"key": "flow_builder.make_api_configuration", "value": "Configuración API Make.com"}, {"key": "flow_builder.make_configure_connection_help", "value": "Configura la conexión a tu cuenta de Make.com"}, {"key": "flow_builder.make_required_fields_marked", "value": "Los campos requeridos están marcados con *"}, {"key": "flow_builder.make_api_token_label", "value": "Token API *"}, {"key": "flow_builder.make_api_token_help_title", "value": "Tu token API de Make.com"}, {"key": "flow_builder.make_api_token_help_description", "value": "Obtén esto de la configuración de tu cuenta de Make.com"}, {"key": "flow_builder.make_api_token_placeholder", "value": "Introduce tu token API de Make.com"}, {"key": "flow_builder.make_copied", "value": "¡Copiado!"}, {"key": "flow_builder.make_copy_token_clipboard", "value": "Copiar token al portapapeles"}, {"key": "flow_builder.make_team_id_label", "value": "ID de Equipo"}, {"key": "flow_builder.make_team_id_help", "value": "Tu ID de equipo de Make.com para escenarios a nivel de equipo"}, {"key": "flow_builder.make_team_id_placeholder", "value": "Introduce ID de equipo (opcional)"}, {"key": "flow_builder.make_organization_id_label", "value": "ID de Organización"}, {"key": "flow_builder.make_organization_id_help", "value": "Tu ID de organización de Make.com para escenarios a nivel de organización"}, {"key": "flow_builder.make_organization_id_placeholder", "value": "Introduce ID de organización (opcional)"}, {"key": "flow_builder.make_testing", "value": "Probando..."}, {"key": "flow_builder.make_test_connection", "value": "Probar Conexión"}, {"key": "flow_builder.make_loading", "value": "Cargando..."}, {"key": "flow_builder.make_list_scenarios", "value": "<PERSON><PERSON>"}, {"key": "flow_builder.webhook_delete_node", "value": "Eliminar nodo"}, {"key": "flow_builder.webhook_integration", "value": "Integración Webhook"}, {"key": "flow_builder.http_delete_node", "value": "Eliminar nodo"}, {"key": "flow_builder.http_integration", "value": "Solicitud HTTP"}, {"key": "flow_builder.auth_none", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flow_builder.auth_bearer_token", "value": "<PERSON><PERSON>"}, {"key": "flow_builder.auth_basic_auth", "value": "Autenticación Básica"}, {"key": "flow_builder.auth_api_key", "value": "Clave API"}, {"key": "flow_builder.template_get_user_data", "value": "Obtener Datos de Usuario"}, {"key": "flow_builder.template_post_form_data", "value": "Publicar Datos de Formulario"}, {"key": "flow_builder.template_update_record", "value": "Actualizar <PERSON>"}, {"key": "flow_builder.media_images", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"key": "flow_builder.media_videos", "value": "Videos"}, {"key": "flow_builder.media_audio", "value": "Audio"}, {"key": "flow_builder.media_documents", "value": "Documentos"}, {"key": "flow_builder.media_images_desc", "value": "Imágenes JPEG, PNG, WebP"}, {"key": "flow_builder.media_videos_desc", "value": "Videos MP4, 3GPP"}, {"key": "flow_builder.media_audio_desc", "value": "Archivos de audio MP3, AAC, OGG"}, {"key": "flow_builder.media_documents_desc", "value": "Archivos PDF, DOC, DOCX"}, {"key": "flow_builder.wait_node_title", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flow_builder.wait_no_date_selected", "value": "No se ha seleccionado fecha"}, {"key": "flow_builder.wait_for_duration", "value": "Esperar {{value}} {{unit}}"}, {"key": "flow_builder.wait_schedule_not_set", "value": "Horario no establecido"}, {"key": "flow_builder.wait_scheduled_for", "value": "Programado para {{date}} a las {{time}} ({{timezone}})"}, {"key": "flow_builder.wait_duration_mode", "value": "Duración"}, {"key": "flow_builder.wait_schedule_mode", "value": "Programar"}, {"key": "flow_builder.wait_time_value", "value": "Valor de Tiempo"}, {"key": "flow_builder.wait_time_unit", "value": "Unidad de Tiempo"}, {"key": "flow_builder.wait_seconds", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flow_builder.wait_minutes", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flow_builder.wait_hours", "value": "<PERSON><PERSON>"}, {"key": "flow_builder.wait_days", "value": "Días"}, {"key": "flow_builder.wait_date", "value": "<PERSON><PERSON>"}, {"key": "flow_builder.wait_time", "value": "<PERSON><PERSON>"}, {"key": "flow_builder.wait_timezone", "value": "Zona Horaria"}, {"key": "flow_builder.quick_reply_default_message", "value": "Por favor, selecciona una opción para continuar:"}, {"key": "flow_builder.quick_reply_default_option1", "value": "Tengo una pregunta sobre mi pedido."}, {"key": "flow_builder.quick_reply_default_option2", "value": "Tengo una pregunta sobre un producto."}, {"key": "flow_builder.quick_reply_default_option3", "value": "Tengo otra pregunta."}, {"key": "flow_builder.quick_reply_invalid_response", "value": "No entendí tu selección. Por favor, elige una de las opciones disponibles:"}, {"key": "flow_builder.quick_reply_new_option", "value": "Nueva opción"}, {"key": "flow_builder.quick_reply_node_title", "value": "Respuesta Rápida"}, {"key": "flow_builder.quick_reply_message_label", "value": "Men<PERSON><PERSON>"}, {"key": "flow_builder.quick_reply_options_label", "value": "Opciones"}, {"key": "flow_builder.quick_reply_add_option", "value": "Agregar Opción"}, {"key": "flow_builder.quick_reply_invalid_response_label", "value": "Mensaje de Respuesta Inválida"}, {"key": "flow_builder.audio_upload_default_caption", "value": "¡Escucha este audio!"}, {"key": "flow_builder.audio_upload_duplicate_node", "value": "Duplicar nodo"}, {"key": "flow_builder.audio_upload_delete_node", "value": "Eliminar nodo"}, {"key": "flow_builder.audio_upload_node_title", "value": "Mensaje de Audio"}, {"key": "flow_builder.audio_upload_upload_complete", "value": "Subida completa"}, {"key": "flow_builder.audio_upload_upload_success", "value": "Audio subido correctamente"}, {"key": "flow_builder.audio_upload_upload_failed", "value": "Fallo al subir"}, {"key": "flow_builder.audio_upload_upload_error", "value": "Hubo un error al subir tu audio"}, {"key": "flow_builder.audio_upload_upload_label", "value": "Subir Audio:"}, {"key": "flow_builder.audio_upload_caption_label", "value": "Pie de foto:"}, {"key": "flow_builder.audio_upload_caption_placeholder", "value": "Introduce pie de foto"}, {"key": "flow_builder.audio_upload_browser_not_supported", "value": "Tu navegador no soporta el elemento de audio."}, {"key": "flow_builder.audio_upload_audio_label", "value": "Audio"}, {"key": "flow_builder.audio_upload_no_audio_selected", "value": "No se seleccionó audio"}, {"key": "flow_builder.image_upload_default_caption", "value": "¡Mira esta imagen!"}, {"key": "flow_builder.image_upload_duplicate_node", "value": "Duplicar nodo"}, {"key": "flow_builder.image_upload_delete_node", "value": "Eliminar nodo"}, {"key": "flow_builder.image_upload_node_title", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flow_builder.image_upload_upload_complete", "value": "Subida completa"}, {"key": "flow_builder.image_upload_upload_success", "value": "Imagen subida correctamente"}, {"key": "flow_builder.image_upload_upload_failed", "value": "Fallo al subir"}, {"key": "flow_builder.image_upload_upload_error", "value": "Hubo un error al subir tu imagen"}, {"key": "flow_builder.image_upload_upload_label", "value": "Subir Imagen:"}, {"key": "flow_builder.image_upload_caption_label", "value": "Pie de foto:"}, {"key": "flow_builder.image_upload_caption_placeholder", "value": "Introduce pie de foto"}, {"key": "flow_builder.image_upload_preview_alt", "value": "Vista previa"}, {"key": "flow_builder.image_upload_image_label", "value": "Imagen"}, {"key": "flow_builder.image_upload_no_image_selected", "value": "No se seleccionó imagen"}, {"key": "flow_builder.video_upload_default_caption", "value": "¡Mira este video!"}, {"key": "flow_builder.video_upload_duplicate_node", "value": "Duplicar nodo"}, {"key": "flow_builder.video_upload_delete_node", "value": "Eliminar nodo"}, {"key": "flow_builder.video_upload_node_title", "value": "Mensaje de <PERSON>"}, {"key": "flow_builder.video_upload_upload_complete", "value": "Subida completa"}, {"key": "flow_builder.video_upload_upload_success", "value": "Video subido correctamente"}, {"key": "flow_builder.video_upload_upload_failed", "value": "Fallo al subir"}, {"key": "flow_builder.video_upload_upload_error", "value": "Hubo un error al subir tu video"}, {"key": "flow_builder.video_upload_upload_label", "value": "Subir Video:"}, {"key": "flow_builder.video_upload_caption_label", "value": "Pie de foto:"}, {"key": "flow_builder.video_upload_caption_placeholder", "value": "Introduce pie de foto"}, {"key": "flow_builder.video_upload_browser_not_supported", "value": "Tu navegador no soporta el elemento de video."}, {"key": "flow_builder.video_upload_video_label", "value": "Video"}, {"key": "flow_builder.video_upload_no_video_selected", "value": "No se seleccionó video"}, {"key": "flow_builder.document_upload_default_caption", "value": "Aquí tienes un documento"}, {"key": "flow_builder.document_upload_duplicate_node", "value": "Duplicar nodo"}, {"key": "flow_builder.document_upload_delete_node", "value": "Eliminar nodo"}, {"key": "flow_builder.document_upload_node_title", "value": "Mensaje de Documento"}, {"key": "flow_builder.document_upload_upload_complete", "value": "Subida completa"}, {"key": "flow_builder.document_upload_upload_success", "value": "Documento subido correctamente"}, {"key": "flow_builder.document_upload_upload_failed", "value": "Fallo al subir"}, {"key": "flow_builder.document_upload_upload_error", "value": "Hubo un error al subir tu documento"}, {"key": "flow_builder.document_upload_upload_label", "value": "Subir Documento:"}, {"key": "flow_builder.document_upload_caption_label", "value": "Pie de foto:"}, {"key": "flow_builder.document_upload_caption_placeholder", "value": "Introduce pie de foto"}, {"key": "flow_builder.document_upload_document_label", "value": "Documento"}, {"key": "flow_builder.document_upload_no_document_selected", "value": "No se seleccionó documento"}, {"key": "flow_builder.ai_assistant_node_title", "value": "Asistente IA"}, {"key": "flow_builder.ai_assistant_duplicate_node", "value": "Duplicar nodo"}, {"key": "flow_builder.ai_assistant_delete_node", "value": "Eliminar nodo"}, {"key": "flow_builder.shopify_orders", "value": "Pedidos"}, {"key": "flow_builder.shopify_products", "value": "Productos"}, {"key": "flow_builder.shopify_customers", "value": "Clientes"}, {"key": "flow_builder.shopify_inventory", "value": "Inventario"}, {"key": "flow_builder.shopify_fulfillments", "value": "Cumplimientos"}, {"key": "flow_builder.shopify_webhooks", "value": "Webhooks"}, {"key": "flow_builder.shopify_product_variants", "value": "Variantes de Producto"}, {"key": "flow_builder.shopify_collections", "value": "Colecciones"}, {"key": "flow_builder.shopify_get_fetch_data", "value": "Obtener (Traer Da<PERSON>)"}, {"key": "flow_builder.shopify_create_new", "value": "<PERSON><PERSON>r <PERSON>"}, {"key": "flow_builder.shopify_update_existing", "value": "<PERSON>ual<PERSON><PERSON>"}, {"key": "flow_builder.shopify_delete", "value": "Eliminar"}, {"key": "flow_builder.shopify_get_recent_orders", "value": "Obtener Pedidos Recientes"}, {"key": "flow_builder.shopify_update_product_stock", "value": "Actualizar Stock de Producto"}, {"key": "flow_builder.shopify_create_customer", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flow_builder.shopify_create_order_fulfillment", "value": "<PERSON><PERSON><PERSON>o de Pedido"}, {"key": "flow_builder.shopify_node_title", "value": "Shopify"}, {"key": "flow_builder.shopify_duplicate_node", "value": "Duplicar nodo"}, {"key": "flow_builder.shopify_delete_node", "value": "Eliminar nodo"}, {"key": "flow_builder.shopify_connection_failed", "value": "Fallo de conexión"}, {"key": "flow_builder.shopify_hide", "value": "Ocultar"}, {"key": "flow_builder.shopify_edit", "value": "<PERSON><PERSON>"}, {"key": "flow_builder.google_sheets_append_row", "value": "Agregar <PERSON>"}, {"key": "flow_builder.google_sheets_append_row_description", "value": "Añadir una nueva fila de datos a la hoja"}, {"key": "flow_builder.google_sheets_append_row_tooltip", "value": "Añade una nueva fila de datos al final de tu Hoja de Cálculo de Google. Perfecto para capturar clientes potenciales, envíos de formularios y recopilación de datos de conversaciones de WhatsApp."}, {"key": "flow_builder.google_sheets_read_rows", "value": "<PERSON><PERSON>"}, {"key": "flow_builder.google_sheets_read_rows_description", "value": "Obtener datos con filtros opcionales"}, {"key": "flow_builder.google_sheets_read_rows_tooltip", "value": "Lee datos de tu Hoja de Cálculo de Google con filtrado opcional y selección de rango de filas. Úsalo para buscar datos existentes o recuperar información."}, {"key": "flow_builder.google_sheets_update_row", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "flow_builder.google_sheets_update_row_description", "value": "Modifica los datos de filas existentes"}, {"key": "flow_builder.google_sheets_update_row_tooltip", "value": "Actualiza filas existentes en tu Hoja de Cálculo de Google por número de fila o por valores de columna coincidentes. Ideal para actualizar el estado de pedidos, información de usuarios o cualquier registro existente."}, {"key": "flow_builder.google_sheets_get_sheet_info", "value": "Obtener Información de Hoja"}, {"key": "flow_builder.google_sheets_get_sheet_info_description", "value": "Recupera metadatos y encabezados de la hoja"}, {"key": "flow_builder.google_sheets_get_sheet_info_tooltip", "value": "Obtén información sobre tu Hoja de Cálculo de Google, incluidos encabezados de columna, nombres de hojas y metadatos. Útil para configuración dinámica y validación."}, {"key": "flow_builder.google_sheets_lead_capture_form", "value": "Formulario de Captura de Clientes Potenciales"}, {"key": "flow_builder.typebot_create_session", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flow_builder.typebot_update_session", "value": "Actualizar <PERSON>"}, {"key": "flow_builder.typebot_close_session", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flow_builder.typebot_get_status", "value": "Obtener Estado"}, {"key": "flow_builder.typebot_text_input", "value": "Entrada de Texto"}, {"key": "flow_builder.typebot_number_input", "value": "Entrada de Número"}, {"key": "flow_builder.typebot_email_input", "value": "Entrada de Correo Electrónico"}, {"key": "flow_builder.typebot_url_input", "value": "Entrada de URL"}, {"key": "flow_builder.typebot_date_input", "value": "Entrada de Fecha"}, {"key": "flow_builder.typebot_phone_input", "value": "Entrada de Teléfono"}, {"key": "flow_builder.typebot_multiple_choice", "value": "Opción Múltiple"}, {"key": "flow_builder.typebot_file_upload", "value": "Carga de Archivos"}, {"key": "flow_builder.typebot_start_new_chat", "value": "Iniciar <PERSON>"}, {"key": "flow_builder.typebot_send_user_message", "value": "Enviar Men<PERSON>"}, {"key": "flow_builder.typebot_get_bot_response", "value": "Obtener Respuesta del Bot"}, {"key": "flow_builder.typebot_close_conversation", "value": "Cerrar Conversación"}, {"key": "flow_builder.bot_disable_5_minutes", "value": "5 minutos"}, {"key": "flow_builder.bot_disable_15_minutes", "value": "15 minutos"}, {"key": "flow_builder.bot_disable_30_minutes", "value": "30 minutos"}, {"key": "flow_builder.bot_disable_1_hour", "value": "1 hora"}, {"key": "flow_builder.bot_disable_2_hours", "value": "2 horas"}, {"key": "flow_builder.bot_disable_4_hours", "value": "4 horas"}, {"key": "flow_builder.bot_disable_8_hours", "value": "8 horas"}, {"key": "flow_builder.bot_disable_24_hours", "value": "24 horas"}, {"key": "flow_builder.bot_disable_manual", "value": "Hasta que se habilite manualmente"}, {"key": "flow_builder.bot_disable_custom", "value": "Duración personalizada"}, {"key": "flow_builder.bot_disable_node_title", "value": "Transferencia a Agente"}, {"key": "flow_builder.bot_disable_duplicate_node", "value": "Duplicar nodo"}, {"key": "flow_builder.bot_disable_delete_node", "value": "Eliminar nodo"}, {"key": "flow_builder.bot_disable_disable_bot", "value": "Deshabilitar <PERSON>"}, {"key": "flow_builder.bot_disable_auto_assign", "value": "Asignar automáticamente al agente disponible"}, {"key": "flow_builder.bot_disable_default_handoff_message", "value": "Un cliente está solicitando ayuda humana."}, {"key": "flow_builder.bot_reset_bot_only", "value": "Volver a habilitar solo respuestas del bot"}, {"key": "flow_builder.bot_reset_bot_only_description", "value": "Solo eliminar la bandera de deshabilitación del bot, mantener todo el contexto e historial"}, {"key": "flow_builder.bot_reset_bot_and_context", "value": "Restablecer bot + borrar contexto de conversación"}, {"key": "flow_builder.bot_reset_bot_and_context_description", "value": "Volver a habilitar el bot y borrar las variables/contexto del flujo"}, {"key": "flow_builder.bot_reset_full_reset", "value": "Restablecimiento completo (bot + contexto + historial)"}, {"key": "flow_builder.bot_reset_full_reset_description", "value": "Restablecimiento completo, incluido el historial de conversaciones"}, {"key": "flow_builder.bot_reset_node_title", "value": "Restablecimiento de Bot"}, {"key": "flow_builder.bot_reset_duplicate_node", "value": "Duplicar nodo"}, {"key": "flow_builder.bot_reset_delete_node", "value": "Eliminar nodo"}, {"key": "flow_builder.bot_reset_default_confirmation", "value": "La asistencia del bot se ha vuelto a habilitar. ¿Cómo puedo ayudarte?"}, {"key": "flow_builder.ai_provider_openai", "value": "OpenAI"}, {"key": "flow_builder.ai_provider_anthropic", "value": "Anthropic"}, {"key": "flow_builder.ai_provider_gemini", "value": "Google Gemini"}, {"key": "flow_builder.ai_provider_deepseek", "value": "DeepSeek"}, {"key": "flow_builder.ai_provider_xai", "value": "xAI"}, {"key": "flow_builder.ai_model_gpt4o_latest", "value": "GPT-4o (Último)"}, {"key": "flow_builder.ai_model_gpt41_nano", "value": "GPT-4.1 Nano"}, {"key": "flow_builder.ai_model_gpt41_mini", "value": "GPT-4.1 Mini"}, {"key": "flow_builder.ai_model_gpt4_turbo", "value": "GPT-4 Turbo"}, {"key": "flow_builder.ai_model_gpt35_turbo", "value": "GPT-3.5 Turbo"}, {"key": "flow_builder.ai_model_claude37_sonnet", "value": "Claude 3.7 Sonnet (Último)"}, {"key": "flow_builder.ai_model_claude3_opus", "value": "Claude 3 Opus"}, {"key": "flow_builder.ai_model_claude3_sonnet", "value": "Claude 3 Sonnet"}, {"key": "flow_builder.ai_model_claude3_haiku", "value": "Claude 3 Haiku"}, {"key": "flow_builder.ai_assistant_node_title", "value": "Asistente IA"}, {"key": "flow_builder.ai_assistant_duplicate_node", "value": "Duplicar nodo"}, {"key": "flow_builder.ai_assistant_delete_node", "value": "Eliminar nodo"}, {"key": "flow_builder.documind_ask_question", "value": "<PERSON><PERSON>"}, {"key": "flow_builder.documind_ask_question_description", "value": "Haz una pregunta sobre los documentos de la carpeta seleccionada"}, {"key": "flow_builder.documind_analyze_documents", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flow_builder.documind_analyze_documents_description", "value": "Ana<PERSON>za todos los documentos de la carpeta y proporciona información"}, {"key": "flow_builder.documind_search_content", "value": "Buscar Contenido"}, {"key": "flow_builder.documind_search_content_description", "value": "Busca contenido específico en todos los documentos"}, {"key": "flow_builder.documind_node_title", "value": "IA Documind"}, {"key": "flow_builder.documind_duplicate_node", "value": "Duplicar nodo"}, {"key": "flow_builder.documind_delete_node", "value": "Eliminar nodo"}, {"key": "flow_builder.documind_conversation_history", "value": "Historial de Conversación"}, {"key": "flow_builder.documind_enable_history", "value": "Incluir historial de conversación"}, {"key": "flow_builder.documind_history_limit", "value": "Límite de Mensajes"}, {"key": "flow_builder.documind_history_help", "value": "Mensajes anteriores a incluir para el contexto"}, {"key": "flow_builder.chatpdf_ask_question", "value": "<PERSON><PERSON>"}, {"key": "flow_builder.chatpdf_ask_question_description", "value": "Haz una pregunta sobre el documento PDF seleccionado"}, {"key": "flow_builder.chatpdf_summarize", "value": "Resumir"}, {"key": "flow_builder.chatpdf_summarize_description", "value": "Obtén un resumen completo del documento PDF"}, {"key": "flow_builder.chatpdf_analyze_content", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flow_builder.chatpdf_analyze_content_description", "value": "Analiza y extrae información del contenido del PDF"}, {"key": "flow_builder.chatpdf_gpt4o_description", "value": "Último modelo GPT-4 (2 créditos por mensaje)"}, {"key": "flow_builder.chatpdf_gpt4_turbo_description", "value": "Modelo GPT-4 Turbo (4 créditos por mensaje)"}, {"key": "flow_builder.chatpdf_node_title", "value": "ChatPDF"}, {"key": "flow_builder.chatpdf_duplicate_node", "value": "Duplicar nodo"}, {"key": "flow_builder.chatpdf_delete_node", "value": "Eliminar nodo"}, {"key": "flow_builder.pipeline_contact_id", "value": "ID de Contacto"}, {"key": "flow_builder.pipeline_contact_id_description", "value": "Identificador único del contacto"}, {"key": "flow_builder.pipeline_contact_phone", "value": "Teléfono del Contacto"}, {"key": "flow_builder.pipeline_contact_phone_description", "value": "Número de teléfono del contacto"}, {"key": "flow_builder.pipeline_contact_name", "value": "Nombre del Contacto"}, {"key": "flow_builder.pipeline_contact_name_description", "value": "Nombre completo del contacto"}, {"key": "flow_builder.pipeline_node_title", "value": "Actualizar Etapa de Pipeline"}, {"key": "flow_builder.pipeline_duplicate_node", "value": "Duplicar nodo"}, {"key": "flow_builder.pipeline_delete_node", "value": "Eliminar nodo"}, {"key": "flow_builder.followup_default_text_template", "value": "¡Gracias por tu interés! ¿Cómo podemos ayudarte más?"}, {"key": "flow_builder.woocommerce_orders", "value": "Pedidos"}, {"key": "flow_builder.woocommerce_products", "value": "Productos"}, {"key": "flow_builder.woocommerce_customers", "value": "Clientes"}, {"key": "flow_builder.woocommerce_coupons", "value": "Cupones"}, {"key": "flow_builder.woocommerce_product_categories", "value": "Categorías de Productos"}, {"key": "flow_builder.woocommerce_product_variations", "value": "Variaciones de Producto"}, {"key": "flow_builder.woocommerce_reports", "value": "Informes"}, {"key": "flow_builder.woocommerce_payment_gateways", "value": "Pa<PERSON><PERSON>s de <PERSON>"}, {"key": "flow_builder.woocommerce_shipping_zones", "value": "Zonas de Envío"}, {"key": "flow_builder.woocommerce_tax_rates", "value": "Tasas de Impuestos"}, {"key": "flow_builder.woocommerce_get_recent_orders", "value": "Obtener Pedidos Recientes"}, {"key": "flow_builder.woocommerce_update_product_stock", "value": "Actualizar Stock de Producto"}, {"key": "flow_builder.woocommerce_create_customer", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flow_builder.woocommerce_create_discount_coupon", "value": "Crear Cupón de Descuento"}, {"key": "flow_builder.woocommerce_mark_order_completed", "value": "Marcar Pedido como Completado"}, {"key": "flow_builder.woocommerce_node_title", "value": "WooCommerce"}, {"key": "calendar.no_available_slots", "value": "No se encontraron franjas horarias disponibles para la fecha y duración seleccionadas"}, {"key": "calendar.checking", "value": "Comprobando..."}, {"key": "calendar.refresh_availability", "value": "Actualizar Disponibilidad"}, {"key": "calendar.category.work", "value": "Trabajo"}, {"key": "calendar.category.personal", "value": "Personal"}, {"key": "calendar.category.family", "value": "Familia"}, {"key": "calendar.category.health", "value": "<PERSON><PERSON>"}, {"key": "calendar.category.projects", "value": "Proyectos"}, {"key": "calendar.category.default", "value": "Predeterminado"}, {"key": "calendar.week_of", "value": "Semana del"}, {"key": "campaigns.unknown_error", "value": "Ocurrió un error desconocido"}, {"key": "settings.inbox.history_sync_enabled", "value": "Sincronización de Historial Habilitada"}, {"key": "settings.inbox.history_sync_disabled", "value": "Sincronización de Historial Deshabilitada"}, {"key": "settings.inbox.history_sync_enabled_desc", "value": "El historial de mensajes de WhatsApp se sincronizará en la próxima conexión"}, {"key": "settings.inbox.history_sync_disabled_desc", "value": "La sincronización del historial ha sido deshabilitada para esta conexión"}, {"key": "settings.inbox.update_history_sync_failed", "value": "Error al actualizar la configuración de sincronización del historial"}, {"key": "groups.create_conversation_failed", "value": "Fallo al crear conversación"}, {"key": "admin.settings.no_logo_file_selected", "value": "No se seleccionó ningún archivo de logotipo"}, {"key": "admin.settings.failed_upload_logo", "value": "Fallo al subir logotipo"}, {"key": "admin.settings.logo_uploaded", "value": "Logotipo subido"}, {"key": "admin.settings.logo_uploaded_desc", "value": "El logotipo se ha subido correctamente."}, {"key": "admin.settings.error_uploading_logo", "value": "Error al subir logotipo"}, {"key": "admin.settings.no_favicon_file_selected", "value": "No se seleccionó ningún archivo de favicon"}, {"key": "admin.settings.failed_upload_favicon", "value": "Fallo al subir favicon"}, {"key": "admin.settings.favicon_uploaded", "value": "Favicon subido"}, {"key": "admin.settings.favicon_uploaded_desc", "value": "El favicon se ha subido correctamente."}, {"key": "admin.settings.error_uploading_favicon", "value": "Error al subir favicon"}, {"key": "admin.settings.app_name_required", "value": "Se requiere el nombre de la aplicación"}, {"key": "admin.settings.failed_save_branding", "value": "Fallo al guardar la configuración de marca"}, {"key": "admin.settings.branding_saved", "value": "Configuración de marca guardada"}, {"key": "admin.settings.branding_saved_desc", "value": "La configuración de marca se ha guardado correctamente."}, {"key": "admin.settings.error_saving_branding", "value": "Error al guardar la configuración de marca"}, {"key": "admin.settings.description", "value": "Configura los ajustes del sistema, las pasarelas de pago y las preferencias de la aplicación"}, {"key": "admin.settings.logo", "value": "Logotipo"}, {"key": "admin.settings.logo_description", "value": "Sube el logotipo de tu empresa (tamaño recomendado: 200x50px)"}, {"key": "admin.settings.logo_preview_alt", "value": "Vista previa del Logotipo"}, {"key": "admin.settings.choose_logo", "value": "Elegir <PERSON>"}, {"key": "admin.settings.upload", "value": "Subir"}, {"key": "admin.settings.favicon", "value": "Favicon"}, {"key": "admin.settings.favicon_description", "value": "Sube tu favicon (tamaño recomendado: 32x32px)"}, {"key": "admin.settings.embed_code_copied", "value": "Código de inserción copiado"}, {"key": "admin.settings.embed_code_copied_desc", "value": "El código de inserción se ha copiado a tu portapapeles."}, {"key": "admin.settings.copy_failed", "value": "Fallo al copiar"}, {"key": "admin.settings.copy_failed_desc", "value": "Fallo al copiar el código de inserción al portapapeles. Por favor, cópialo manualmente."}, {"key": "admin.settings.failed_save_stripe", "value": "Fallo al guardar la configuración de Stripe"}, {"key": "admin.settings.stripe_saved", "value": "Configuración de Stripe guardada"}, {"key": "admin.settings.stripe_saved_desc", "value": "La configuración de Stripe se ha guardado correctamente."}, {"key": "admin.settings.error_saving_stripe", "value": "Error al guardar la configuración de Stripe"}, {"key": "admin.settings.stripe_connection_successful", "value": "Conexión con Stripe exitosa"}, {"key": "admin.settings.connected_to_stripe_account", "value": "Conectado a la cuenta de Stripe: {{email}}"}, {"key": "admin.settings.error_connecting_stripe", "value": "Error al conectarse a Stripe"}, {"key": "admin.settings.failed_test_stripe", "value": "Fallo al probar la conexión con Stripe"}, {"key": "admin.settings.test_email_required", "value": "Se requiere dirección de correo electrónico de prueba"}, {"key": "admin.settings.smtp_connection_successful", "value": "Conexión SMTP exitosa"}, {"key": "admin.settings.smtp_test_passed", "value": "La prueba de conexión SMTP ha sido superada"}, {"key": "admin.settings.error_testing_smtp", "value": "Error al probar la conexión SMTP"}, {"key": "admin.settings.failed_test_smtp", "value": "Fallo al probar la conexión SMTP"}, {"key": "admin.settings.default_plan_required", "value": "Se requiere el plan predeterminado cuando el registro está habilitado"}, {"key": "admin.settings.registration_saved", "value": "Configuración de registro guardada"}, {"key": "admin.settings.registration_saved_desc", "value": "La configuración de registro se ha guardado correctamente."}, {"key": "admin.settings.error_saving_registration", "value": "Error al guardar la configuración de registro"}, {"key": "admin.settings.failed_save_registration", "value": "Fallo al guardar la configuración de registro"}, {"key": "admin.settings.company_name_placeholder", "value": "Nombre de la Empresa"}, {"key": "admin.settings.test_connection", "value": "Probar Conexión"}, {"key": "admin.settings.configure", "value": "Configurar"}, {"key": "admin.settings.configure_google_oauth", "value": "Configura OAuth de Google para inicio de sesión social"}, {"key": "admin.settings.configure_facebook_oauth", "value": "Configura OAuth de Facebook para inicio de sesión social"}, {"key": "admin.settings.configure_apple_oauth", "value": "Configura OAuth de Apple para inicio de sesión social"}, {"key": "admin.settings.configure_stripe_gateway", "value": "Configura la pasarela de pago Stripe"}, {"key": "admin.settings.configure_paypal_gateway", "value": "Configura la pasarela de pago PayPal"}, {"key": "admin.settings.configure_smtp_settings", "value": "Configura los ajustes SMTP para enviar correos electrónicos del sistema, notificaciones y restablecimientos de contraseña"}, {"key": "admin.settings.configure_general_settings", "value": "Configura los ajustes generales de la aplicación"}, {"key": "admin.settings.enable_company_registration", "value": "Habilitar Registro de Empresas"}, {"key": "admin.settings.registration_description", "value": "<PERSON>uando está habilitado, las nuevas empresas pueden registrarse para obtener cuentas. Cuando está deshabilitado, la página de registro mostrará un mensaje indicando que el registro no está disponible actualmente."}, {"key": "admin.settings.approval_description", "value": "Cuando está habilitado, los nuevos registros de empresas requerirán la aprobación del super administrador antes de que puedan acceder a la plataforma."}, {"key": "admin.settings.frontend_website", "value": "Sitio Web Front-end"}, {"key": "admin.settings.frontend_website_description", "value": "Habilita o deshabilita la página de destino pública en /landing"}, {"key": "admin.settings.website_disabled", "value": "Sitio Web Deshabilitado"}, {"key": "admin.settings.website_disabled_description", "value": "La página de destino pública está actualmente deshabilitada. Los visitantes de /landing verán una página de \"no encontrado\"."}, {"key": "inbox.cannot_send_media", "value": "No se puede enviar multimedia, no conectado al servidor"}, {"key": "inbox.not_connected_server", "value": "No conectado al servidor"}, {"key": "inbox.conversation_not_found", "value": "Conversación no encontrada"}, {"key": "inbox.network_error", "value": "Error de red"}, {"key": "inbox.failed_send_media", "value": "Error al enviar mensaje multimedia"}, {"key": "inbox.server_error", "value": "Error del servidor ({{status}}): {{statusText}}"}, {"key": "inbox.send_message_failed", "value": "Fallo al enviar mensaje"}, {"key": "flow_builder.duplicate_node", "value": "Duplicar nodo"}, {"key": "flow_builder.delete_node", "value": "Eliminar nodo"}, {"key": "flow_builder.send_message", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flow_builder.default_message", "value": "¡Hola! ¿Cómo puedo ayudarte?"}, {"key": "flow_builder.var_contact_name", "value": "Nombre del Contacto"}, {"key": "flow_builder.var_contact_phone", "value": "Número de teléfono del Contacto"}, {"key": "flow_builder.var_message_content", "value": "Contenido del mensaje recibido"}, {"key": "flow_builder.var_date_today", "value": "<PERSON><PERSON>"}, {"key": "flow_builder.var_time_now", "value": "<PERSON><PERSON> actual"}, {"key": "flow_builder.var_availability", "value": "Datos de disponibilidad de Google Calendar del nodo anterior"}, {"key": "flow_builder.type_message_placeholder", "value": "Escribe tu mensaje aquí..."}, {"key": "flow_builder.insert_variable", "value": "Insertar Variable:"}, {"key": "flow_builder.variables_help", "value": "Las variables se reemplazarán con los valores reales al enviar el mensaje."}, {"key": "flow_builder.condition", "value": "Condición"}, {"key": "flow_builder.condition_type", "value": "Tipo de Condición:"}, {"key": "flow_builder.advanced", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flow_builder.enter_custom_condition", "value": "Introduce condición personalizada"}, {"key": "flow_builder.condition_examples", "value": "Ejemplos: <PERSON><PERSON><PERSON>('ayuda'), EsMedia(), CoincidenciaExacta('hola')"}, {"key": "flow_builder.enter_text_value", "value": "Introduce valor de texto"}, {"key": "flow_builder.case_sensitive", "value": "Sensible a mayúsculas/minúsculas"}, {"key": "flow_builder.has_media_desc", "value": "Verifica si el mensaje tiene algún archivo multimedia adjunto."}, {"key": "flow_builder.media_type_desc", "value": "Verifica si el mensaje contiene multimedia del tipo seleccionado."}, {"key": "flow_builder.before", "value": "<PERSON><PERSON>"}, {"key": "flow_builder.after", "value": "Después"}, {"key": "flow_builder.between", "value": "<PERSON><PERSON>"}, {"key": "flow_builder.time_format_between", "value": "Usa formato: HH:MM,HH:MM (24h)"}, {"key": "flow_builder.time_format_single", "value": "Usa formato: HH:MM (24h)"}, {"key": "flow_builder.name", "value": "Nombre"}, {"key": "flow_builder.phone", "value": "Teléfono"}, {"key": "flow_builder.email", "value": "Correo Electrónico"}, {"key": "flow_builder.tags", "value": "Etiquetas"}, {"key": "flow_builder.attribute_value", "value": "Valor del atributo"}, {"key": "flow_builder.yes", "value": "Sí"}, {"key": "flow_builder.no", "value": "No"}, {"key": "flow_builder.input", "value": "Entrada"}, {"key": "flow_builder.collect_user_response", "value": "Recopilar respuesta del usuario"}, {"key": "flow_builder.enter_input_prompt", "value": "Introduce el prompt de entrada"}, {"key": "flow_builder.action", "value": "Acción"}, {"key": "flow_builder.perform_api_call", "value": "Realizar llamada API"}, {"key": "flow_builder.enter_action", "value": "Introduce acción"}, {"key": "flow_builder.action_examples", "value": "Ejemplos: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(), <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>('VIP'), <PERSON><PERSON><PERSON><PERSON>o('clientespotenciales')"}, {"key": "flow_builder.search_nodes", "value": "Buscar nodos..."}, {"key": "flow_builder.add_node", "value": "Agregar <PERSON>"}, {"key": "flow_builder.no_nodes_found", "value": "No se encontraron nodos"}, {"key": "flow_builder.try_different_search", "value": "Intenta con un término de búsqueda diferente"}, {"key": "flow_builder.checking", "value": "Comprobando..."}, {"key": "flow_builder.connected", "value": "Conectado"}, {"key": "flow_builder.connect", "value": "Conectar"}, {"key": "flow_builder.connect_google_calendar_first", "value": "Conéctate a Google Calendar primero"}, {"key": "flow_builder.name_required", "value": "Se requiere nombre"}, {"key": "flow_builder.provide_flow_name", "value": "Por favor, proporciona un nombre para tu flujo"}, {"key": "flow_builder.error_loading_flow", "value": "Error al cargar el flujo"}, {"key": "flow_builder.could_not_parse_flow_data", "value": "No se pudieron analizar los datos del flujo"}, {"key": "flow_builder.flow_created", "value": "<PERSON><PERSON><PERSON> c<PERSON>o"}, {"key": "flow_builder.flow_created_successfully", "value": "Su flujo se ha creado correctamente."}, {"key": "flow_builder.error_creating_flow", "value": "Error al crear el flujo"}, {"key": "flow_builder.something_went_wrong", "value": "Algo salió mal"}, {"key": "flow_builder.flow_updated", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flow_builder.flow_updated_successfully", "value": "Su flujo se ha actualizado correctamente."}, {"key": "flow_builder.error_updating_flow", "value": "Error al actualizar el flujo"}, {"key": "flow_builder.node_deleted", "value": "Nodo eliminado"}, {"key": "flow_builder.node_connections_removed", "value": "El nodo y sus conexiones han sido eliminados."}, {"key": "flow_builder.node_duplicated", "value": "Nodo duplicado"}, {"key": "flow_builder.node_copy_created", "value": "Se ha creado una copia del nodo."}, {"key": "flow_builder.flow_name", "value": "Nombre del flujo"}, {"key": "flow_builder.node_selection", "value": "Selección de Nodo"}, {"key": "flow_builder.active", "value": "Activo"}, {"key": "flow_builder.draft", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flow_builder.creating_new_flow", "value": "Creando Nuevo Flujo"}, {"key": "flow_builder.current_flow_status", "value": "Estado actual del flujo"}, {"key": "flow_builder.send_image", "value": "Enviar Imagen"}, {"key": "flow_builder.image_label", "value": "Imagen:"}, {"key": "flow_builder.or_enter_image_url", "value": "O introduce URL de imagen:"}, {"key": "flow_builder.enter_image_url", "value": "Introduce URL o ruta de imagen"}, {"key": "flow_builder.caption_optional", "value": "Pie de foto (opcional):"}, {"key": "flow_builder.add_caption_image", "value": "Añade un pie de foto a tu imagen..."}, {"key": "flow_builder.insert_variable_caption", "value": "Insertar Variable en Pie de Foto:"}, {"key": "flow_builder.no_image_provided", "value": "No se proporcionó imagen"}, {"key": "flow_builder.caption_label", "value": "Pie de foto:"}, {"key": "flow_builder.send_video", "value": "Enviar Video"}, {"key": "flow_builder.video_url_label", "value": "URL de Video:"}, {"key": "flow_builder.enter_video_url", "value": "Introduce URL o ruta de video"}, {"key": "flow_builder.add_caption_video", "value": "Añade un pie de foto a tu video..."}, {"key": "flow_builder.no_url_provided", "value": "No se proporcionó URL"}, {"key": "flow_builder.send_audio", "value": "Enviar Audio"}, {"key": "flow_builder.audio_url_label", "value": "URL de Audio:"}, {"key": "flow_builder.enter_audio_url", "value": "Introduce URL o ruta de audio"}, {"key": "flow_builder.add_caption_audio", "value": "Añade un pie de foto a tu audio..."}, {"key": "flow_builder.send_document", "value": "Enviar Documento"}, {"key": "flow_builder.document_url_label", "value": "URL de Documento:"}, {"key": "flow_builder.enter_document_url", "value": "Introduce URL o ruta de documento"}, {"key": "flow_builder.file_name_optional", "value": "Nombre del Archivo (opcional):"}, {"key": "flow_builder.enter_file_name", "value": "Introduce el nombre del archivo (ej. informe.pdf)"}, {"key": "flow_builder.add_caption_document", "value": "Añade un pie de foto a tu documento..."}, {"key": "flow_builder.file_name_label", "value": "Nombre del Archivo:"}, {"key": "flow_builder.message_received", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flow_builder.when", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flow_builder.message_lowercase", "value": "mensaje"}, {"key": "flow_builder.that", "value": "que"}, {"key": "flow_builder.any_message", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"key": "flow_builder.contains_word", "value": "<PERSON><PERSON><PERSON>"}, {"key": "flow_builder.exact_match", "value": "Coincidencia Exacta"}, {"key": "flow_builder.regex_pattern", "value": "Patrón Regex"}, {"key": "flow_builder.has_media", "value": "Tiene Multimedia"}, {"key": "flow_builder.matches_pattern", "value": "coincide con el patrón"}, {"key": "flow_builder.changes_saved_automatically", "value": "Los cambios se guardan automáticamente al guardar el flujo."}, {"key": "flow_builder.default_video_caption", "value": "¡Mira este video!"}, {"key": "flow_builder.default_audio_caption", "value": "¡Escucha este audio!"}, {"key": "flow_builder.default_document_caption", "value": "¡Mira este documento!"}, {"key": "flow_builder.default_image_caption", "value": "¡Mira esta imagen!"}, {"key": "flow_builder.default_document_caption_full", "value": "Aquí está el documento que solicitaste."}, {"key": "analytics.title", "value": "Analíticas"}, {"key": "analytics.select_period", "value": "Selecciona el período de tiempo"}, {"key": "analytics.period.today", "value": "Hoy"}, {"key": "analytics.period.yesterday", "value": "Ayer"}, {"key": "analytics.period.7days", "value": "Últimos 7 días"}, {"key": "analytics.period.30days", "value": "Últimos 30 días"}, {"key": "analytics.period.90days", "value": "Últimos 90 días"}, {"key": "analytics.period.custom", "value": "<PERSON><PERSON>"}, {"key": "analytics.pick_date", "value": "Elige una fecha"}, {"key": "analytics.export", "value": "Exportar"}, {"key": "analytics.vs_last_period", "value": "vs <PERSON>ltimo <PERSON>"}, {"key": "analytics.cards.total_conversations", "value": "Conversaciones Totales"}, {"key": "analytics.cards.total_contacts", "value": "Contactos Totales"}, {"key": "analytics.cards.total_messages", "value": "Mensajes Totales"}, {"key": "analytics.cards.response_rate", "value": "Tasa de Respuesta"}, {"key": "analytics.charts.conversations_by_channel", "value": "Conversaciones por Canal"}, {"key": "analytics.charts.avg_response_time", "value": "Tiempo Promedio de Respuesta"}, {"key": "analytics.charts.channel_distribution", "value": "Distribución por Canal"}, {"key": "analytics.charts.bot_performance", "value": "Rendimiento del Bot"}, {"key": "analytics.response_time", "value": "Tiempo de Respuesta"}, {"key": "analytics.channels.whatsapp_official", "value": "WhatsApp Oficial"}, {"key": "analytics.channels.whatsapp_unofficial", "value": "WhatsApp No Oficial"}, {"key": "analytics.channels.messenger", "value": "<PERSON>"}, {"key": "analytics.channels.instagram", "value": "Instagram"}, {"key": "analytics.tabs.overview", "value": "Resumen"}, {"key": "analytics.tabs.top_intents", "value": "Intenciones Principales"}, {"key": "analytics.tabs.flow_performance", "value": "Rendimiento del Flujo"}, {"key": "analytics.bot.handling_title", "value": "Gestión por Bot"}, {"key": "analytics.bot.handling_description", "value": "El asistente de IA manejó exitosamente el 65% de todas las conversaciones sin intervención humana."}, {"key": "analytics.bot.escalation_title", "value": "Escalada a Humano"}, {"key": "analytics.bot.escalation_description", "value": "El 35% de las conversaciones requirieron escalada a un agente humano."}, {"key": "analytics.bot.escalation_reasons_title", "value": "Principales Razones de Escalada"}, {"key": "analytics.bot.reason_1", "value": "Preguntas complejas de productos"}, {"key": "analytics.bot.reason_2", "value": "Negociaciones de precios"}, {"key": "analytics.bot.reason_3", "value": "Problemas de soporte técnico"}, {"key": "analytics.intents.product_info", "value": "Información del Producto"}, {"key": "analytics.intents.pricing", "value": "Consultas de Precios"}, {"key": "analytics.intents.scheduling", "value": "Programación de Citas"}, {"key": "analytics.intents.support", "value": "Solicitudes de Soporte"}, {"key": "analytics.intents.other", "value": "<PERSON><PERSON><PERSON>"}, {"key": "analytics.flows.welcome", "value": "Flujo de Bienvenida"}, {"key": "analytics.flows.product_info", "value": "Información del Producto"}, {"key": "analytics.flows.support_ticket", "value": "<PERSON><PERSON><PERSON> <PERSON> So<PERSON>"}, {"key": "analytics.flows.completion_rate", "value": "{{rate}}% de finalización"}, {"key": "analytics.flows.conversations_count", "value": "{{count}} conversaciones"}, {"key": "analytics.export.success", "value": "Exportación exitosa"}, {"key": "analytics.export.success_description", "value": "Los datos de analíticas se han exportado correctamente."}, {"key": "analytics.export.error", "value": "Fallo en la exportación"}, {"key": "analytics.export.error_description", "value": "Fallo al exportar los datos de analíticas."}, {"key": "groups.unnamed_group", "value": "Grupo sin nombre"}, {"key": "groups.participants", "value": "participantes"}, {"key": "groups.group_info", "value": "Información del Grupo"}, {"key": "groups.close_group_info", "value": "Cerrar información del grupo"}, {"key": "groups.description", "value": "Descripción"}, {"key": "groups.group_id", "value": "ID del Grupo"}, {"key": "groups.created_date", "value": "<PERSON><PERSON><PERSON>"}, {"key": "groups.admin", "value": "Administrador"}, {"key": "groups.super_admin", "value": "Administrador del Grupo"}, {"key": "groups.show_more", "value": "Mostrar más"}, {"key": "groups.show_less", "value": "<PERSON><PERSON> menos"}, {"key": "groups.unknown_participant", "value": "Participante Desconocido"}, {"key": "groups.avatar.refresh_tooltip", "value": "Actualizar foto de perfil del grupo"}, {"key": "groups.participants.title", "value": "Participantes del Grupo"}, {"key": "groups.participants.view_all", "value": "Ver todos los participantes"}, {"key": "groups.participants.export_participants", "value": "Exportar Participantes"}, {"key": "groups.participants.export_csv", "value": "Exportar CSV"}, {"key": "groups.participants.search_placeholder", "value": "Buscar participantes..."}, {"key": "groups.participants.showing_count", "value": "Mostrando {{count}} de {{total}} participantes"}, {"key": "groups.participants.loading", "value": "Cargando participantes..."}, {"key": "groups.participants.error", "value": "Fallo al cargar participantes"}, {"key": "groups.participants.no_results", "value": "No se encontraron participantes que coincidan con tu búsqueda"}, {"key": "groups.participants.no_participants", "value": "No se encontraron participantes"}, {"key": "groups.participants.member", "value": "Miembro"}, {"key": "groups.participants.admin", "value": "Administrador"}, {"key": "groups.participants.super_admin", "value": "Super Administrador"}, {"key": "groups.participants.joined", "value": "Se unió"}, {"key": "groups.participants.export_success", "value": "Exportación exitosa"}, {"key": "groups.participants.export_success_desc", "value": "La lista de participantes se exportó a CSV"}, {"key": "groups.participants.export_error", "value": "Fallo al exportar la lista de participantes"}, {"key": "groups.participants.unknown_participant", "value": "Participante Desconocido"}, {"key": "groups.participants.sync_participants", "value": "Sincronizar Participantes"}, {"key": "groups.participants.sync_success", "value": "Sincronización exitosa"}, {"key": "groups.participants.sync_success_desc", "value": "Los participantes se han sincronizado desde WhatsApp. Los nombres aparecerán cuando los participantes envíen mensajes. Se muestran los números de teléfono para los participantes que aún no han estado activos."}, {"key": "groups.participants.sync_error", "value": "Fallo al sincronizar participantes"}, {"key": "groups.participants.name_info", "value": "Los nombres de los participantes aparecen cuando envían mensajes. Se muestran los números de teléfono de los participantes que aún no han estado activos."}, {"key": "common.unknown", "value": "Desconocido"}, {"key": "admin.settings.platform", "value": "Plataforma"}, {"key": "admin.settings.partnerapi", "value": "API de Socios"}, {"key": "admin.settings.title", "value": "Configuraciones"}, {"key": "admin.settings.branding", "value": "<PERSON><PERSON>"}, {"key": "admin.settings.branding_description", "value": "Personaliza la apariencia de tu aplicación"}, {"key": "admin.settings.payment_gateways", "value": "Pa<PERSON><PERSON>s de <PERSON>"}, {"key": "admin.settings.email", "value": "Configuración de Correo Electrónico"}, {"key": "admin.settings.general", "value": "General"}, {"key": "admin.settings.registration", "value": "Registro"}, {"key": "admin.settings.backup", "value": "Copia de Seguridad de Base de Datos"}, {"key": "admin.settings.app_name", "value": "Nombre de la Aplicación"}, {"key": "admin.settings.primary_color", "value": "Color Primario"}, {"key": "admin.settings.secondary_color", "value": "Color Secundario"}, {"key": "admin.settings.save_branding", "value": "Guardar Configuración de Marca"}, {"key": "admin.settings.platform.title", "value": "Configuración de Plataforma"}, {"key": "admin.settings.platform.description", "value": "Configura integraciones de API de socios y credenciales de Proveedor Técnico a nivel de plataforma"}, {"key": "admin.settings.platform.360dialog.title", "value": "API de Socio 360Dialog"}, {"key": "admin.settings.platform.360dialog.description", "value": "Configura las credenciales de Socio 360Dialog para la incorporación de empresas"}, {"key": "admin.settings.platform.meta.title", "value": "API de WhatsApp Business de Meta"}, {"key": "admin.settings.platform.meta.description", "value": "Configura las credenciales del Proveedor Técnico de Meta para la inscripción integrada"}, {"key": "admin.settings.platform.meta.benefits.embedded_signup", "value": "Integración de inscripción integrada de Proveedor Técnico"}, {"key": "admin.settings.platform.meta.benefits.streamlined_onboarding", "value": "Incorporación simplificada de cuentas de WhatsApp Business"}, {"key": "admin.settings.platform.meta.benefits.automatic_provisioning", "value": "Aprovisionamiento automático de números de teléfono"}, {"key": "admin.settings.platform.configure_button", "value": "Configurar"}, {"key": "admin.settings.platform.coming_soon", "value": "Próximamente"}, {"key": "admin.settings.platform.additional_partners.title", "value": "APIs de Socios Adicionales"}, {"key": "admin.settings.platform.additional_partners.description", "value": "Las integraciones de socios futuras aparecerán aquí"}, {"key": "meta.partner.config.title", "value": "Configuración del Socio de la API de WhatsApp Business de Meta"}, {"key": "meta.partner.config.loading", "value": "Cargando configuración..."}, {"key": "meta.partner.config.tech_provider_credentials", "value": "Credenciales del Proveedor Técnico"}, {"key": "meta.partner.config.tech_provider_description", "value": "Configura tus credenciales de Proveedor Técnico de Meta para la inscripción integrada"}, {"key": "meta.partner.config.app_id", "value": "ID de la Aplicación"}, {"key": "meta.partner.config.app_id_placeholder", "value": "Tu ID de Aplicación de Meta"}, {"key": "meta.partner.config.app_secret", "value": "Secreto de la Aplicación"}, {"key": "meta.partner.config.app_secret_placeholder", "value": "Tu Secreto de Aplicación de Meta"}, {"key": "meta.partner.config.business_manager_id", "value": "ID de Business Manager"}, {"key": "meta.partner.config.business_manager_id_placeholder", "value": "Tu ID de Business Manager"}, {"key": "meta.partner.config.webhook_verify_token", "value": "Token de Verificación de Webhook"}, {"key": "meta.partner.config.webhook_verify_token_placeholder", "value": "Token de verificación de webhook"}, {"key": "meta.partner.config.access_token", "value": "Token de Acceso de Usuario del Sistema"}, {"key": "meta.partner.config.access_token_placeholder", "value": "Token de acceso de usuario del sistema"}, {"key": "meta.partner.config.webhook_url", "value": "URL de Webhook"}, {"key": "meta.partner.config.webhook_url_placeholder", "value": "https://tudominio.com/api/webhooks/meta-whatsapp"}, {"key": "meta.partner.config.company_profile", "value": "Perfil de la Empresa"}, {"key": "meta.partner.config.company_profile_description", "value": "Esta información se mostrará a las empresas durante la incorporación"}, {"key": "meta.partner.config.company_name", "value": "Nombre de la Empresa"}, {"key": "meta.partner.config.company_name_placeholder", "value": "Nombre de tu empresa"}, {"key": "meta.partner.config.logo_url", "value": "URL del Logotipo"}, {"key": "meta.partner.config.logo_url_placeholder", "value": "https://ejemplo.com/logo.png"}, {"key": "meta.partner.config.test_configuration", "value": "Probar Configuración"}, {"key": "meta.partner.config.save_configuration", "value": "Guardar Configuración"}, {"key": "meta.partner.config.update_configuration", "value": "Actualizar Configuración"}, {"key": "meta.partner.config.validation.required_fields", "value": "Se requieren ID de Aplicación, Secreto de Aplicación y ID de Business Manager"}, {"key": "meta.partner.config.validation.success", "value": "¡Las credenciales de la API de Socio de Meta son válidas!"}, {"key": "meta.partner.config.validation.failed", "value": "Credenciales de la API de Socio de Meta inválidas"}, {"key": "meta.partner.config.validation.error", "value": "Fallo al validar la configuración"}, {"key": "meta.partner.config.save.success", "value": "Configuración de la API de Socio de Meta actualizada correctamente"}, {"key": "meta.partner.config.save.error", "value": "Fallo al guardar la configuración"}, {"key": "meta.partner.config.load.error", "value": "Fallo al cargar la configuración existente"}, {"key": "meta.whatsapp.onboarding.title", "value": "Meta WhatsApp Business API - Configuración Fácil"}, {"key": "meta.whatsapp.onboarding.description", "value": "Conecta tu cuenta de WhatsApp Business en solo unos clics usando nuestro flujo de incorporación integrado."}, {"key": "meta.whatsapp.onboarding.connection_name", "value": "Nombre de la Conexión"}, {"key": "meta.whatsapp.onboarding.connection_name_placeholder", "value": "ej., WhatsApp Business Principal"}, {"key": "meta.whatsapp.onboarding.connection_name_help", "value": "Dale un nombre memorable a esta conexión"}, {"key": "meta.whatsapp.onboarding.what_happens_next", "value": "Qué sucede a continuación:"}, {"key": "meta.whatsapp.onboarding.step_connect_business", "value": "Conecta tu cuenta de Meta Business"}, {"key": "meta.whatsapp.onboarding.step_select_account", "value": "Selecciona tu cuenta de WhatsApp Business"}, {"key": "meta.whatsapp.onboarding.step_choose_numbers", "value": "Elige los números de teléfono a integrar"}, {"key": "meta.whatsapp.onboarding.step_automatic_config", "value": "Configuración y configuración automáticas"}, {"key": "meta.whatsapp.onboarding.sdk_initializing", "value": "Inicializando SDK de Facebook..."}, {"key": "meta.whatsapp.onboarding.start_easy_setup", "value": "Iniciar Configuración Fácil"}, {"key": "meta.whatsapp.onboarding.processing", "value": "Procesando..."}, {"key": "meta.whatsapp.onboarding.cancel", "value": "<PERSON><PERSON><PERSON>"}, {"key": "meta.whatsapp.onboarding.checking_config", "value": "Verificando configuración..."}, {"key": "meta.whatsapp.onboarding.config_required.title", "value": "Configuración Requerida"}, {"key": "meta.whatsapp.onboarding.config_required.description", "value": "La integración de la API de WhatsApp Business de Meta Partner no está configurada. Por favor, contacta a tu administrador del sistema para configurar las credenciales de la API de Socio."}, {"key": "meta.whatsapp.onboarding.config_required.close", "value": "<PERSON><PERSON><PERSON>"}, {"key": "meta.whatsapp.onboarding.success", "value": "Cuenta de WhatsApp Business conectada con éxito. {{count}} número(s) de teléfono configurado(s)."}, {"key": "meta.whatsapp.onboarding.error.connection_name_required", "value": "Por favor, introduce un nombre de conexión"}, {"key": "meta.whatsapp.onboarding.error.sdk_not_initialized", "value": "SDK de Facebook no inicializado"}, {"key": "meta.whatsapp.onboarding.error.launch_signup", "value": "Fallo al lanzar el registro de WhatsApp"}, {"key": "meta.whatsapp.onboarding.error.signup_failed", "value": "Fallo al procesar el registro de la cuenta de WhatsApp Business"}, {"key": "meta.whatsapp.onboarding.error.process_signup", "value": "Fallo al procesar el registro"}, {"key": "meta.whatsapp.onboarding.error.sdk_init", "value": "Fallo al inicializar el SDK de Facebook"}, {"key": "channels.whatsapp.meta.easy_setup", "value": "Configuración Fácil"}, {"key": "channels.whatsapp.meta.title", "value": "WhatsApp Business API (Meta)"}, {"key": "channels.whatsapp.meta.description", "value": "API Oficial de WhatsApp Business de Meta"}, {"key": "channels.whatsapp.meta.embedded.title", "value": "WhatsApp Business Integrado"}, {"key": "channels.whatsapp.meta.embedded.description", "value": "Configuración rápida con inscripción integrada"}, {"key": "channels.whatsapp.meta.connection_info", "value": "API Oficial de WhatsApp Business (Meta)"}, {"key": "channels.whatsapp.meta.connection_description", "value": "Esta conexión utiliza la API Oficial de WhatsApp Business de Meta. Proporciona mensajería confiable con funciones avanzadas y cumplimiento."}, {"key": "common.success", "value": "Éxito"}, {"key": "common.error", "value": "Error"}, {"key": "common.validation_failed", "value": "Validación Fallida"}, {"key": "common.signup_error", "value": "<PERSON><PERSON><PERSON>"}, {"key": "common.sdk_error", "value": "<PERSON><PERSON>r de SDK"}, {"key": "common.configuration_error", "value": "Error de Configuración"}, {"key": "partner.config.success.created", "value": "Configuración de API de Socio creada correctamente"}, {"key": "partner.config.success.updated", "value": "Configuración de API de Socio actualizada correctamente"}, {"key": "partner.config.error.load_failed", "value": "Fallo al cargar la configuración existente"}, {"key": "partner.config.error.save_failed", "value": "Fallo al guardar la configuración"}, {"key": "partner.config.error.validation_failed", "value": "Fallo al validar la configuración"}, {"key": "api.access.title", "value": "Acceso API"}, {"key": "api.access.description", "value": "Gestiona claves API y acceso programático a tus canales"}, {"key": "api.access.create_key", "value": "Crear Clave API"}, {"key": "api.access.no_keys.title", "value": "No Hay Claves API"}, {"key": "api.access.no_keys.description", "value": "Crea tu primera clave API para empezar a enviar mensajes de forma programática"}, {"key": "api.access.tabs.keys", "value": "Claves API"}, {"key": "api.access.tabs.usage", "value": "Estadísticas de Uso"}, {"key": "api.access.tabs.docs", "value": "Documentación"}, {"key": "api.access.key.active", "value": "Activo"}, {"key": "api.access.key.inactive", "value": "Inactivo"}, {"key": "api.access.key.activate", "value": "Activar"}, {"key": "api.access.key.deactivate", "value": "Desactivar"}, {"key": "api.access.key.delete", "value": "Eliminar"}, {"key": "api.access.key.created", "value": "<PERSON><PERSON><PERSON>"}, {"key": "api.access.key.last_used", "value": "<PERSON><PERSON><PERSON>"}, {"key": "api.access.key.never_used", "value": "Nunca"}, {"key": "api.access.key.rate_limits", "value": "Límites de Tasa"}, {"key": "api.access.key.permissions", "value": "<PERSON><PERSON><PERSON>"}, {"key": "api.access.create.title", "value": "Crear Nueva Clave API"}, {"key": "api.access.create.description", "value": "Crea una nueva clave API para acceder a la API de mensajería de forma programática."}, {"key": "api.access.create.name_label", "value": "Nombre de la Clave API"}, {"key": "api.access.create.name_placeholder", "value": "ej., Bot de Producción, Automatización de Marketing"}, {"key": "api.access.create.creating", "value": "Creando..."}, {"key": "api.access.create.cancel", "value": "<PERSON><PERSON><PERSON>"}, {"key": "api.access.show_key.title", "value": "Clave API Creada"}, {"key": "api.access.show_key.description", "value": "Tu clave API ha sido creada. Cópiala ahora, ya que no se mostrará de nuevo."}, {"key": "api.access.show_key.warning", "value": "Guarda esta clave API de forma segura. No podrás volver a verla."}, {"key": "api.access.show_key.saved", "value": "He Guardado la Clave"}, {"key": "api.access.usage.total_requests", "value": "Solicitudes Totales"}, {"key": "api.access.usage.successful", "value": "Exitosas"}, {"key": "api.access.usage.failed", "value": "Fallidas"}, {"key": "api.access.usage.avg_duration", "value": "Duración Prom."}, {"key": "api.access.usage.data_transfer", "value": "Transferencia de Datos"}, {"key": "api.access.docs.title", "value": "Documentación API"}, {"key": "api.access.docs.description", "value": "Aprende cómo integrarte con nuestra API de mensajería"}, {"key": "api.access.docs.base_url", "value": "URL Base"}, {"key": "api.access.docs.endpoints", "value": "Endpoints Disponibles"}, {"key": "api.access.docs.authentication", "value": "Autenticación"}, {"key": "api.access.docs.auth_description", "value": "Incluye tu clave API en el encabezado Authorization:"}, {"key": "api.access.error.name_required", "value": "Por favor, introduce un nombre para la clave API"}, {"key": "api.access.error.create_failed", "value": "Fallo al crear clave API"}, {"key": "api.access.error.delete_failed", "value": "Fallo al eliminar clave API"}, {"key": "api.access.error.update_failed", "value": "Fallo al actualizar clave API"}, {"key": "api.access.error.load_failed", "value": "Fallo al cargar claves API"}, {"key": "api.access.success.created", "value": "Clave API creada correctamente"}, {"key": "api.access.success.deleted", "value": "Clave API eliminada correctamente"}, {"key": "api.access.success.activated", "value": "Clave API activada correctamente"}, {"key": "api.access.success.deactivated", "value": "Clave API desactivada correctamente"}, {"key": "api.access.success.copied", "value": "Clave API copiada al portapapeles"}]