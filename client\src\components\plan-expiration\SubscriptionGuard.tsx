import React, { useEffect, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '../../hooks/use-auth';
import { useSubscriptionStatus } from '../../hooks/useSubscriptionStatus.ts';
import { apiRequest } from '../../lib/queryClient';
import SubscriptionRenewalDialog from './SubscriptionRenewalDialog';

interface SubscriptionGuardProps {
  children: React.ReactNode;
}

export default function SubscriptionGuard({ children }: SubscriptionGuardProps) {
  const { user, company } = useAuth();
  const { data: subscriptionStatus } = useSubscriptionStatus();
  const [showModal, setShowModal] = useState(false);

  // Fetch all available plans and find the current one
  const { data: allPlans } = useQuery({
    queryKey: ['/api/plans'],
    queryFn: async () => {
      const res = await apiRequest("GET", "/api/plans");
      if (!res.ok) throw new Error("Failed to fetch plans");
      return res.json();
    }
  });

  // Find the current plan from all available plans
  const currentPlan = allPlans?.find((plan: any) => plan.id === company?.planId);

  useEffect(() => {

    if (!user || user.isSuperAdmin) {
      return;
    }


    if (subscriptionStatus && !subscriptionStatus.isActive) {
      const shouldShowModal = 
        subscriptionStatus.status === 'expired' || 
        subscriptionStatus.status === 'grace_period' ||
        subscriptionStatus.status === 'cancelled' ||
        subscriptionStatus.status === 'past_due';

      setShowModal(shouldShowModal);
    } else {
      setShowModal(false);
    }
  }, [subscriptionStatus, user]);


  useEffect(() => {
    const handleFetchError = (event: any) => {
      if (event.detail?.status === 402 && event.detail?.error === 'SUBSCRIPTION_EXPIRED') {
        setShowModal(true);
      }
    };

    window.addEventListener('subscription-expired', handleFetchError);
    return () => window.removeEventListener('subscription-expired', handleFetchError);
  }, []);


  useEffect(() => {
    const originalFetch = window.fetch;

    window.fetch = async (...args) => {
      const response = await originalFetch(...args);

      if (response.status === 402) {
        try {
          const errorData = await response.clone().json();
          if (errorData.error === 'SUBSCRIPTION_EXPIRED') {
            setShowModal(true);
            // Suppress console logging for expected subscription errors
            const originalError = console.error;
            console.error = () => {};
            setTimeout(() => {
              console.error = originalError;
            }, 100);
          }
        } catch (e) {
          // Ignore JSON parsing errors
        }
      }

      return response;
    };

    return () => {
      window.fetch = originalFetch;
    };
  }, []);

  const handleCloseModal = () => {
    setShowModal(false);
  };

  const getExpirationInfo = () => {
    if (!subscriptionStatus) return {};

    return {
      expirationDate: subscriptionStatus.daysUntilExpiry !== undefined ?
        new Date(Date.now() - subscriptionStatus.daysUntilExpiry * 24 * 60 * 60 * 1000).toISOString() :
        undefined,
      gracePeriodEnd: subscriptionStatus.gracePeriodDaysRemaining && subscriptionStatus.gracePeriodDaysRemaining > 0 ?
        new Date(Date.now() + subscriptionStatus.gracePeriodDaysRemaining * 24 * 60 * 60 * 1000).toISOString() :
        undefined,
      isInGracePeriod: subscriptionStatus.gracePeriodActive || subscriptionStatus.status === 'grace_period',
    };
  };

  return (
    <>
      {children}
      <SubscriptionRenewalDialog
        isOpen={showModal}
        onClose={handleCloseModal}
        companyName={company?.name}
        planName={currentPlan?.name || company?.plan || "Current Plan"}
        planPrice={currentPlan?.price || 29.99}
        currentPlanId={company?.planId || undefined}
        {...getExpirationInfo()}
      />
    </>
  );
}
