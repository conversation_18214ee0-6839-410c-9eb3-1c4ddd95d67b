/* Landing Page Specific Styles */

/* Smooth scrolling for anchor links */
html {
  scroll-behavior: smooth;
}

/* Modern landing page styles */
.landing-page {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

/* Hero section animations */
.hero-content {
  animation: fadeInUp 0.8s ease-out;
}

.hero-image {
  animation: fadeInRight 0.8s ease-out 0.2s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Feature cards hover effects */
.feature-card {
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border-color: #d1d5db;
}

/* Gradient backgrounds */
.hero-gradient {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.cta-gradient {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
}

/* Pricing card hover effects */
.pricing-card {
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
}

.pricing-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border-color: #d1d5db;
}

/* Popular plan highlight */
.pricing-card.popular {
  border: 2px solid #3b82f6;
  box-shadow: 0 0 30px rgba(59, 130, 246, 0.15);
}

/* Dashboard preview animation */
.dashboard-preview {
  transition: all 0.3s ease;
}

.dashboard-preview:hover {
  transform: rotate(0deg) scale(1.02);
}

/* Stats counter animation */
.stat-counter {
  animation: countUp 2s ease-out;
}

@keyframes countUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Button hover effects */
.btn-primary {
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
}

/* CTA Section button fixes */
.cta-sign-in-btn {
  background-color: transparent !important;
  border: 2px solid white !important;
  color: white !important;
}

.cta-sign-in-btn:hover {
  background-color: white !important;
  color: #3b82f6 !important;
}

.cta-sign-in-btn a {
  color: white !important;
  text-decoration: none;
}

.cta-sign-in-btn:hover a {
  color: #3b82f6 !important;
}

/* Navigation styles */
.nav-link {
  position: relative;
  transition: all 0.3s ease;
}

.nav-link:hover {
  color: #3b82f6;
}

.nav-link::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -4px;
  left: 50%;
  background-color: #3b82f6;
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.nav-link:hover::after {
  width: 100%;
}

/* Mobile menu animation */
.mobile-menu {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading spinner */
.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive text sizing */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
    line-height: 1.2;
  }

  .hero-subtitle {
    font-size: 1.125rem;
    line-height: 1.5;
  }
}

@media (max-width: 640px) {
  .hero-title {
    font-size: 2rem;
    line-height: 1.2;
  }

  .hero-subtitle {
    font-size: 1rem;
    line-height: 1.5;
  }
}

/* Responsive layouts */
@media (max-width: 1024px) {
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .pricing-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .features-grid {
    grid-template-columns: 1fr;
  }

  .pricing-grid {
    grid-template-columns: 1fr;
  }

  .hero-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 640px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .footer-grid {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .cta-buttons {
    flex-direction: column;
  }
}

/* Glassmorphism effects */
.glass-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-nav {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

/* Scroll animations */
.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.fade-in-left {
  animation: fadeInLeft 0.6s ease-out;
}

.fade-in-right {
  animation: fadeInRight 0.6s ease-out;
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Section spacing */
.section-padding {
  padding: 5rem 0;
}

@media (max-width: 768px) {
  .section-padding {
    padding: 3rem 0;
  }
}

/* Trust indicators */
.trust-badge {
  transition: all 0.3s ease;
}

.trust-badge:hover {
  transform: scale(1.05);
}
