import { useQuery } from '@tanstack/react-query';

interface SubscriptionStatus {
  isActive: boolean;
  status: string;
  daysUntilExpiry?: number;
  gracePeriodActive?: boolean;
  gracePeriodDaysRemaining?: number;
  nextBillingDate?: string;
  canRenew: boolean;
  canPause: boolean;
  canUpgrade: boolean;
  canDowngrade: boolean;
}

export function useSubscriptionStatus() {
  return useQuery<SubscriptionStatus>({
    queryKey: ['subscription-status'],
    queryFn: async () => {
      const response = await fetch('/api/enhanced-subscription/status', {
        credentials: 'include'
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch subscription status');
      }
      
      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
  });
}
