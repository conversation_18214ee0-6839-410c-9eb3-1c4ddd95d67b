import Header from '@/components/layout/Header';
import Sidebar from '@/components/layout/Sidebar';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useState, useEffect, useCallback } from 'react';
import { apiRequest } from '@/lib/queryClient';
import { useLocation } from 'wouter';
import { useTranslation } from '@/hooks/use-translation';
import Pagination from '@/components/contacts/Pagination';
import EditContactModal from '@/components/contacts/EditContactModal';
import { ContactExportModal } from '@/components/contacts/ContactExportModal';
import { CreateSegmentFromContactsModal } from '@/components/contacts/CreateSegmentFromContactsModal';
import { WhatsAppScrapingModal } from '@/components/contacts/WhatsAppScrapingModal';
import { useToast } from "@/hooks/use-toast";
import { formatDistanceToNow } from 'date-fns';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { FileUpload } from '@/components/ui/file-upload';
import { Loader2, Plus, Upload, Download, AlertCircle, CheckCircle, X, Trash2 } from 'lucide-react';


function normalizePhoneNumber(phone: string): string {
  if (!phone) return '';


  let normalized = phone.replace(/[^\d+]/g, '');


  if (normalized && !normalized.startsWith('+')) {

    normalized = normalized.replace(/^0+/, '');
    if (normalized.length > 10) {
      normalized = '+' + normalized;
    }
  }

  return normalized;
}

function isWhatsAppGroupChatId(phoneNumber: string | null | undefined): boolean {
  if (!phoneNumber) {
    return false;
  }


  const numericOnly = phoneNumber.replace(/[^0-9]/g, '');



  return numericOnly.length >= 15 && numericOnly.startsWith('120');
}

function isValidInternationalPhoneNumber(phone: string): boolean {
  const numericOnly = phone.replace(/[^0-9]/g, '');


  if (numericOnly.length < 7 || numericOnly.length > 14) {
    return false;
  }


  const validCountryCodePatterns = [

    /^1[2-9]\d{9}$/,

    /^44[1-9]\d{8,9}$/,

    /^49[1-9]\d{8,10}$/,

    /^33[1-9]\d{8}$/,

    /^39[0-9]\d{6,10}$/,

    /^34[6-9]\d{8}$/,

    /^31[1-9]\d{8}$/,

    /^32[1-9]\d{7,8}$/,

    /^41[1-9]\d{8}$/,

    /^43[1-9]\d{6,10}$/,

    /^61[2-9]\d{8}$/,

    /^81[1-9]\d{8,9}$/,

    /^82[1-9]\d{7,8}$/,

    /^86[1-9]\d{9,10}$/,

    /^91[6-9]\d{9}$/,

    /^55[1-9]\d{8,9}$/,

    /^52[1-9]\d{9}$/,

    /^54[1-9]\d{8,9}$/,

    /^57[1-9]\d{7,9}$/,

    /^27[1-9]\d{8}$/,

    /^234[7-9]\d{9}$/,

    /^254[7]\d{8}$/,

    /^255[6-9]\d{8}$/,

    /^20[1-9]\d{8,9}$/,

    /^7[3-9]\d{9}$/,

    /^90[5]\d{9}$/,

    /^966[5]\d{8}$/,

    /^971[5]\d{8}$/,

    /^92[3]\d{9}$/,

    /^880[1]\d{8,9}$/,

    /^62[8]\d{8,10}$/,

    /^60[1]\d{7,8}$/,

    /^66[6-9]\d{8}$/,

    /^63[9]\d{9}$/,

    /^84[3-9]\d{8}$/,

    /^65[6-9]\d{7}$/,
  ];

  return validCountryCodePatterns.some(pattern => pattern.test(numericOnly));
}

function validatePhoneNumber(phone: string): { isValid: boolean; error?: string } {
  if (!phone) {
    return { isValid: true }; // Phone is optional
  }


  if (phone.startsWith('LID-')) {
    return {
      isValid: false,
      error: 'LID format phone numbers are not allowed'
    };
  }


  const numericOnly = phone.replace(/[^0-9]/g, '');
  if (numericOnly.length > 14) {
    return {
      isValid: false,
      error: 'Phone number is too long (maximum 14 digits allowed)'
    };
  }

  if (numericOnly.length < 7) {
    return {
      isValid: false,
      error: 'Phone number is too short (minimum 7 digits required)'
    };
  }


  const normalized = normalizePhoneNumber(phone);


  if (isWhatsAppGroupChatId(normalized)) {
    return {
      isValid: false,
      error: 'WhatsApp group chat IDs are not allowed as contact phone numbers'
    };
  }





  return { isValid: true };
}

function checkForDuplicatePhone(phone: string, existingContacts: Contact[]): { isDuplicate: boolean; existingContact?: Contact } {
  if (!phone) {
    return { isDuplicate: false };
  }

  const normalizedPhone = normalizePhoneNumber(phone);

  const existingContact = existingContacts.find(contact => {
    if (!contact.phone) return false;
    return normalizePhoneNumber(contact.phone) === normalizedPhone;
  });

  return {
    isDuplicate: !!existingContact,
    existingContact
  };
}

interface Contact {
  id: number;
  name: string;
  email?: string | null;
  phone?: string | null;
  company?: string | null;
  avatarUrl?: string | null;
  tags?: string[] | null;
  isActive?: boolean | null;
  identifier?: string | null;
  identifierType?: string | null;
  source?: string | null;
  notes?: string | null;
  createdAt: string;
  updatedAt: string;
}

export default function Contacts() {
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearch, setDebouncedSearch] = useState('');
  const [channelFilter, setChannelFilter] = useState('');
  const [tagsFilter, setTagsFilter] = useState<string[]>([]);
  const [itemsPerPage] = useState(10);
  const [deleteContactId, setDeleteContactId] = useState<number | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);


  const [isAddContactDialogOpen, setIsAddContactDialogOpen] = useState(false);
  const [addContactForm, setAddContactForm] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
    identifierType: '',
    identifier: '',
    notes: '',
    tags: ''
  });
  const [isSubmittingContact, setIsSubmittingContact] = useState(false);


  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [importFile, setImportFile] = useState<File | null>(null);
  const [importProgress, setImportProgress] = useState(0);
  const [isImporting, setIsImporting] = useState(false);
  const [importResults, setImportResults] = useState<{
    successful: number;
    failed: number;
    errors: string[];
  } | null>(null);
  const [duplicateHandling, setDuplicateHandling] = useState<'skip' | 'update' | 'create'>('skip');
  const [csvPreview, setCsvPreview] = useState<any[]>([]);
  const [showPreview, setShowPreview] = useState(false);


  const [selectedContacts, setSelectedContacts] = useState<Set<number>>(new Set());
  const [isBulkDeleteDialogOpen, setIsBulkDeleteDialogOpen] = useState(false);
  const [isBulkDeleting, setIsBulkDeleting] = useState(false);


  const [isCreateSegmentModalOpen, setIsCreateSegmentModalOpen] = useState(false);


  const [isWhatsAppScrapingModalOpen, setIsWhatsAppScrapingModalOpen] = useState(false);

  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { t } = useTranslation();
  const [, setLocation] = useLocation();

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(searchTerm);
      setCurrentPage(1);
    }, 300);
    
    return () => clearTimeout(timer);
  }, [searchTerm]);
  
  const handleChannelFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setChannelFilter(e.target.value);
    setCurrentPage(1);
  };


  const { data: availableTags = [], refetch: refetchTags } = useQuery({
    queryKey: ['/api/contacts/tags'],
    queryFn: async () => {
      const response = await fetch('/api/contacts/tags');
      if (!response.ok) {
        throw new Error('Failed to fetch tags');
      }
      return response.json();
    },
    refetchOnWindowFocus: true,
    staleTime: 30000, // Consider data stale after 30 seconds
    gcTime: 5 * 60 * 1000 // Keep in cache for 5 minutes
  });

  const { data, isLoading } = useQuery({
    queryKey: ['/api/contacts', currentPage, debouncedSearch, channelFilter, tagsFilter, itemsPerPage],
    queryFn: async () => {
      const params = new URLSearchParams();
      params.append('page', currentPage.toString());
      params.append('limit', itemsPerPage.toString());

      if (debouncedSearch) {
        params.append('search', debouncedSearch);
      }

      if (channelFilter) {
        params.append('channel', channelFilter);
      }

      if (tagsFilter.length > 0) {
        params.append('tags', tagsFilter.join(','));
      }

      const response = await fetch(`/api/contacts?${params.toString()}`);
      if (!response.ok) {
        throw new Error('Failed to fetch contacts');
      }

      return response.json();
    },
    refetchOnWindowFocus: false
  });
  

  const rawContacts: Contact[] = Array.isArray(data?.contacts) ? data.contacts : [];


  const filteredContacts = rawContacts.filter(contact => {

    if (contact.phone && isWhatsAppGroupChatId(contact.phone)) {
      return false;
    }


    if (contact.identifier && isWhatsAppGroupChatId(contact.identifier)) {
      return false;
    }


    if (contact.phone) {
      const phoneValidation = validatePhoneNumber(contact.phone);
      if (!phoneValidation.isValid) {
        return false;
      }
    }


    if (contact.identifier) {
      const identifierValidation = validatePhoneNumber(contact.identifier);
      if (!identifierValidation.isValid) {
        return false;
      }
    }

    return true;
  });


  const deduplicatedContacts = filteredContacts.reduce((acc: Contact[], contact) => {
    if (!contact.phone) {

      acc.push(contact);
      return acc;
    }

    const normalizedPhone = normalizePhoneNumber(contact.phone);
    const existingIndex = acc.findIndex(existing =>
      existing.phone && normalizePhoneNumber(existing.phone) === normalizedPhone
    );

    if (existingIndex === -1) {

      acc.push(contact);
    } else {

      const existing = acc[existingIndex];
      if (new Date(contact.createdAt) > new Date(existing.createdAt)) {
        acc[existingIndex] = contact;
      }
    }

    return acc;
  }, []);

  const contacts: Contact[] = deduplicatedContacts;
  const totalContacts = data?.total || 0;
  const totalPages = Math.ceil(totalContacts / itemsPerPage);


  
  const deleteContactMutation = useMutation({
    mutationFn: async (contactId: number) => {
      const response = await apiRequest('DELETE', `/api/contacts/${contactId}`);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to delete contact');
      }

      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Contact deleted",
        description: "The contact has been successfully deleted.",
      });

      queryClient.invalidateQueries({ queryKey: ['/api/contacts'] });
      queryClient.invalidateQueries({ queryKey: ['/api/contacts/tags'] });
    },
    onError: (error: Error) => {
      toast({
        title: "Delete failed",
        description: error.message,
        variant: "destructive",
      });
    }
  });


  const addContactMutation = useMutation({
    mutationFn: async (contactData: any) => {
      const response = await apiRequest('POST', '/api/contacts', contactData);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to create contact');
      }

      return response.json();
    },
    onMutate: () => {
      setIsSubmittingContact(true);
    },
    onSuccess: () => {
      toast({
        title: t('contacts.add.success_title', 'Contact created'),
        description: t('contacts.add.success_description', 'The contact has been successfully created.'),
      });

      queryClient.invalidateQueries({ queryKey: ['/api/contacts'] });
      queryClient.invalidateQueries({ queryKey: ['/api/contacts/tags'] });
      setIsAddContactDialogOpen(false);
      resetAddContactForm();
    },
    onError: (error: Error) => {
      toast({
        title: t('contacts.add.error_title', 'Creation failed'),
        description: error.message,
        variant: "destructive",
      });
    },
    onSettled: () => {
      setIsSubmittingContact(false);
    }
  });


  const importContactsMutation = useMutation({
    mutationFn: async ({ file, duplicateHandling }: { file: File; duplicateHandling: string }) => {
      const formData = new FormData();
      formData.append('csvFile', file);
      formData.append('duplicateHandling', duplicateHandling);


      return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();

        xhr.upload.onprogress = (event) => {
          if (event.lengthComputable) {
            const percentComplete = Math.round((event.loaded / event.total) * 100);
            setImportProgress(percentComplete);
          }
        };

        xhr.onload = () => {
          if (xhr.status === 200) {
            try {
              const response = JSON.parse(xhr.responseText);
              resolve(response);
            } catch (e) {
              reject(new Error('Invalid response format'));
            }
          } else {
            try {
              const errorResponse = JSON.parse(xhr.responseText);
              reject(new Error(errorResponse.error || 'Import failed'));
            } catch (e) {
              reject(new Error(`Import failed with status ${xhr.status}`));
            }
          }
        };

        xhr.onerror = () => reject(new Error('Network error during import'));

        xhr.open('POST', '/api/contacts/import');
        xhr.send(formData);
      });
    },
    onMutate: () => {
      setIsImporting(true);
      setImportProgress(0);
      setImportResults(null);
    },
    onSuccess: (data: any) => {
      setImportResults(data);
      toast({
        title: t('contacts.import.success_title', 'Import completed'),
        description: t('contacts.import.success_description', 'Successfully imported {{count}} contacts', { count: data.successful }),
      });

      queryClient.invalidateQueries({ queryKey: ['/api/contacts'] });
      queryClient.invalidateQueries({ queryKey: ['/api/contacts/tags'] });
    },
    onError: (error: Error) => {
      toast({
        title: t('contacts.import.error_title', 'Import failed'),
        description: error.message,
        variant: "destructive",
      });
    },
    onSettled: () => {
      setIsImporting(false);
      setImportProgress(0);
    }
  });


  const bulkDeleteContactsMutation = useMutation({
    mutationFn: async (contactIds: number[]) => {
      const response = await apiRequest('DELETE', '/api/contacts/bulk', { contactIds });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete contacts');
      }

      return response.json();
    },
    onMutate: () => {
      setIsBulkDeleting(true);
    },
    onSuccess: (data: any) => {
      const { successful, failed, total } = data;

      if (successful.length > 0) {
        toast({
          title: t('contacts.bulk_delete.success_title', 'Contacts deleted'),
          description: t('contacts.bulk_delete.success_description', 'Successfully deleted {{count}} of {{total}} contacts', {
            count: successful.length,
            total
          }),
        });
      }

      if (failed.length > 0) {
        toast({
          title: t('contacts.bulk_delete.partial_failure_title', 'Some deletions failed'),
          description: t('contacts.bulk_delete.partial_failure_description', '{{count}} contacts could not be deleted', {
            count: failed.length
          }),
          variant: 'destructive',
        });
      }

      queryClient.invalidateQueries({ queryKey: ['/api/contacts'] });
      queryClient.invalidateQueries({ queryKey: ['/api/contacts/tags'] });
      setSelectedContacts(new Set());
      setIsBulkDeleteDialogOpen(false);
    },
    onError: (error: Error) => {
      toast({
        title: t('contacts.bulk_delete.error_title', 'Bulk delete failed'),
        description: error.message,
        variant: "destructive",
      });
    },
    onSettled: () => {
      setIsBulkDeleting(false);
    }
  });


  const resetAddContactForm = () => {
    setAddContactForm({
      name: '',
      email: '',
      phone: '',
      company: '',
      identifierType: '',
      identifier: '',
      notes: '',
      tags: ''
    });
  };

  const resetImportForm = () => {
    setImportFile(null);
    setImportProgress(0);
    setImportResults(null);
    setCsvPreview([]);
    setShowPreview(false);
    setDuplicateHandling('skip');
  };

  const handleAddContactSubmit = () => {
    if (!addContactForm.name.trim()) {
      toast({
        title: t('common.error', 'Error'),
        description: t('contacts.add.name_required', 'Contact name is required'),
        variant: 'destructive'
      });
      return;
    }


    if (addContactForm.phone) {
      const phoneValidation = validatePhoneNumber(addContactForm.phone);
      if (!phoneValidation.isValid) {
        toast({
          title: t('common.error', 'Error'),
          description: phoneValidation.error,
          variant: 'destructive'
        });
        return;
      }


      const duplicateCheck = checkForDuplicatePhone(addContactForm.phone, contacts || []);
      if (duplicateCheck.isDuplicate) {
        toast({
          title: t('common.error', 'Error'),
          description: t('contacts.add.duplicate_phone', 'A contact with this phone number already exists: {{name}}', {
            name: duplicateCheck.existingContact?.name
          }),
          variant: 'destructive'
        });
        return;
      }
    }

    const tagsArray = addContactForm.tags
      ? addContactForm.tags.split(',').map(tag => tag.trim()).filter(Boolean)
      : [];


    const normalizedPhone = addContactForm.phone ? normalizePhoneNumber(addContactForm.phone) : '';

    addContactMutation.mutate({
      ...addContactForm,
      phone: normalizedPhone,
      tags: tagsArray
    });
  };

  const handleFileSelected = (file: File) => {
    setImportFile(file);

    parseCsvPreview(file);
  };

  const parseCsvPreview = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const text = e.target?.result as string;
      const lines = text.split('\n').filter(line => line.trim());
      const headers = lines[0]?.split(',').map(h => h.trim());
      const preview = lines.slice(1, 6).map((line, index) => {
        const values = line.split(',').map(v => v.trim());
        const obj: any = {};
        headers?.forEach((header, index) => {
          obj[header] = values[index] || '';
        });


        const warnings: string[] = [];
        if (obj.phone) {
          const phoneValidation = validatePhoneNumber(obj.phone);
          if (!phoneValidation.isValid) {
            warnings.push(phoneValidation.error || 'Invalid phone number');
          }

          const duplicateCheck = checkForDuplicatePhone(obj.phone, contacts || []);
          if (duplicateCheck.isDuplicate) {
            warnings.push(`Duplicate phone number (existing contact: ${duplicateCheck.existingContact?.name})`);
          }
        }

        obj._warnings = warnings;
        obj._rowNumber = index + 2; // +2 because we skip header and arrays are 0-indexed
        return obj;
      });
      setCsvPreview(preview);
      setShowPreview(true);
    };
    reader.readAsText(file);
  };

  const downloadCsvTemplate = () => {
    const headers = ['name', 'email', 'phone', 'company', 'identifierType', 'identifier', 'notes', 'tags'];
    const exampleData = [
      'Abid,<EMAIL>,+923059002132,Pointer Software,whatsapp,+923059020132,Sales lead,"lead,customer"',
      'Niamat,<EMAIL>,+923000052443,Pointer Software,messenger,niamat.shakran,Marketing contact,"prospect,vip"'
    ];

    const csvContent = [headers.join(','), ...exampleData].join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'contacts_template.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const handleImportSubmit = () => {
    if (!importFile) {
      toast({
        title: t('common.error', 'Error'),
        description: t('contacts.import.no_file_selected', 'Please select a CSV file to import'),
        variant: 'destructive'
      });
      return;
    }

    importContactsMutation.mutate({
      file: importFile,
      duplicateHandling
    });
  };


  const handleSelectContact = (contactId: number, checked: boolean) => {
    const newSelected = new Set(selectedContacts);

    const numericId = typeof contactId === 'string' ? parseInt(contactId, 10) : contactId;

    if (isNaN(numericId)) {
      console.error('Invalid contact ID detected:', contactId);
      return;
    }

    if (checked) {
      newSelected.add(numericId);
    } else {
      newSelected.delete(numericId);
    }

    setSelectedContacts(newSelected);
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {

      const allContactIds = new Set(contacts.map(contact => {
        const id = typeof contact.id === 'string' ? parseInt(contact.id, 10) : contact.id;
        return id;
      }).filter(id => !isNaN(id)));

      setSelectedContacts(allContactIds);
    } else {
      setSelectedContacts(new Set());
    }
  };

  const handleClearSelection = () => {
    setSelectedContacts(new Set());
  };

  const handleBulkDelete = () => {
    if (selectedContacts.size === 0) return;
    setIsBulkDeleteDialogOpen(true);
  };

  const handleSegmentCreated = (segment: any) => {
    toast({
      title: t('common.success', 'Success'),
      description: t('segments.create.success_redirect', 'Segment "{{name}}" created successfully. You can now use it in campaigns.', {
        name: segment.name
      })
    });


    setSelectedContacts(new Set());



  };

  const confirmBulkDelete = () => {
    const contactIds = Array.from(selectedContacts);


    const validContactIds = contactIds
      .map(id => {
        const numId = typeof id === 'string' ? parseInt(id, 10) : Number(id);
        return isNaN(numId) ? null : numId;
      })
      .filter(id => id !== null) as number[];

    if (validContactIds.length === 0) {
      toast({
        title: t('common.error', 'Error'),
        description: 'No valid contact IDs selected for deletion',
        variant: 'destructive'
      });
      return;
    }

    bulkDeleteContactsMutation.mutate(validContactIds);
  };

  const isAllSelected = contacts.length > 0 && selectedContacts.size === contacts.length;
  const isIndeterminate = selectedContacts.size > 0 && selectedContacts.size < contacts.length;

  const handleEditContact = (contact: Contact) => {
    setSelectedContact(contact);
    setIsEditModalOpen(true);
  };
  
  const handleEditModalClose = () => {
    setIsEditModalOpen(false);
    setSelectedContact(null);
  };
  
  const handleDeleteContact = (id: number) => {
    setDeleteContactId(id);
    setIsDeleteDialogOpen(true);
  };
  
  const confirmDelete = () => {
    if (deleteContactId) {
      deleteContactMutation.mutate(deleteContactId);
    }
    setIsDeleteDialogOpen(false);
  };
  
  const formatLastContact = (date: string) => {
    if (!date) return 'Never';
    return formatDistanceToNow(new Date(date), { addSuffix: true });
  };
  
  const handleChannelClick = (contact: Contact) => {
    if (!contact.id || !contact.identifierType) return;

    localStorage.setItem('selectedContactId', contact.id.toString());
    localStorage.setItem('selectedChannelType', contact.identifierType);

    setLocation('/');

    toast({
      title: t('contacts.redirecting_to_inbox', 'Redirecting to inbox'),
      description: t('contacts.opening_conversation_with', 'Opening conversation with {{name}}', { name: contact.name }),
    });
  };
  
  const handleMessageClick = (contact: Contact) => {
    handleChannelClick(contact);
  };
  
  return (
    <div className="h-screen flex flex-col overflow-hidden font-sans text-gray-800">
      <Header />
      
      <div className="flex flex-1 overflow-hidden">
        <Sidebar />
        
        <div className="flex-1 overflow-y-auto p-6">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
            <h1 className="text-2xl">{t('nav.contacts', 'Contacts')}</h1>
            <div className="flex flex-col sm:flex-row gap-2">
              <Button
                onClick={() => setIsAddContactDialogOpen(true)}
                className="flex items-center gap-2"
                variant="default"
              >
                <Plus className="h-4 w-4" />
                {t('contacts.add_contact', 'Add Contact')}
              </Button>
              <Button
                onClick={() => setIsImportDialogOpen(true)}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Upload className="h-4 w-4" />
                {t('contacts.import.button', 'Import CSV')}
              </Button>
              <Button
                onClick={() => setIsExportModalOpen(true)}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                {t('contacts.export.button', 'Export Contacts')}
              </Button>


            {/* 03059002132 */}

              <Button
                onClick={() => setIsWhatsAppScrapingModalOpen(true)}
                variant="outline"
                className="flex items-center gap-2 bg-green-50 hover:bg-green-100 text-green-700 border-green-200"
              >
                <i className="ri-whatsapp-line text-lg"></i>
                {t('contacts.scraping.button', 'Find New Leads')}
              </Button>
            </div>
          </div>

          {/* Bulk Actions Toolbar */}
          {selectedContacts.size > 0 && (
            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3">
              <div className="flex items-center gap-3">
                <span className="text-sm font-medium text-blue-900">
                  {t('contacts.bulk_actions.selected_count', '{{count}} contacts selected', { count: selectedContacts.size })}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleClearSelection}
                  className="text-blue-700 border-blue-300 hover:bg-blue-100"
                >
                  {t('contacts.bulk_actions.clear_selection', 'Clear Selection')}
                </Button>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsCreateSegmentModalOpen(true)}
                  className="flex items-center gap-2"
                >
                  <i className="ri-group-line h-4 w-4" />
                  {t('contacts.bulk_actions.create_segment', 'Create Segment')}
                </Button>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={handleBulkDelete}
                  disabled={isBulkDeleting}
                  className="flex items-center gap-2"
                >
                  {isBulkDeleting ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      {t('contacts.bulk_actions.deleting', 'Deleting...')}
                    </>
                  ) : (
                    <>
                      <Trash2 className="h-4 w-4" />
                      {t('contacts.bulk_actions.delete_selected', 'Delete Selected')}
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}

          <div className="flex mb-6">
            <div className="relative flex-1 max-w-md">
              <input
                type="search"
                placeholder={t('contacts.search_placeholder', 'Search contacts...')}
                className="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <i className="ri-search-line absolute left-3 top-2.5 text-gray-400"></i>
            </div>
            <div className="ml-4">
              <select
                className="p-2 border border-gray-300 rounded-lg"
                value={channelFilter}
                onChange={handleChannelFilterChange}
              >
                <option value="">{t('contacts.all_channels', 'All Channels')}</option>
                <option value="whatsapp_official">{t('contacts.whatsapp_official', 'WhatsApp Official')}</option>
                <option value="whatsapp_unofficial">{t('contacts.whatsapp_unofficial', 'WhatsApp Unofficial')}</option>
                <option value="messenger">{t('contacts.facebook_messenger', 'Facebook Messenger')}</option>
                <option value="instagram">{t('contacts.instagram', 'Instagram')}</option>
              </select>
            </div>

            {/* Tags Filter */}
            <div className="ml-4 flex items-center gap-2">
              <select
                className="p-2 border border-gray-300 rounded-lg min-w-[120px]"
                value=""
                onChange={(e) => {
                  const selectedTag = e.target.value;
                  if (selectedTag && !tagsFilter.includes(selectedTag)) {
                    setTagsFilter(prev => [...prev, selectedTag]);
                    setCurrentPage(1);
                  }
                }}
                disabled={availableTags.length === 0}
              >
                <option value="">
                  {availableTags.length === 0
                    ? t('contacts.no_tags_available', 'No tags available')
                    : t('contacts.filter_by_tags', 'Filter by Tags')
                  }
                </option>
                {availableTags.map((tag: string) => (
                  <option key={tag} value={tag} disabled={tagsFilter.includes(tag)}>
                    {tag}
                  </option>
                ))}
              </select>
              <button
                onClick={() => refetchTags()}
                className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
                title={t('contacts.refresh_tags', 'Refresh tags')}
              >
                <i className="ri-refresh-line text-sm"></i>
              </button>
            </div>
          </div>

          {/* Selected Tags Display */}
          {tagsFilter.length > 0 && (
            <div className="mb-4 flex flex-wrap items-center gap-2">
              <span className="text-sm text-gray-600">{t('contacts.selected_tags', 'Selected tags:')}</span>
              {tagsFilter.map((tag) => (
                <span
                  key={tag}
                  className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
                >
                  {tag}
                  <button
                    onClick={() => {
                      setTagsFilter(prev => prev.filter(t => t !== tag));
                      setCurrentPage(1);
                    }}
                    className="ml-1 text-blue-600 hover:text-blue-800"
                  >
                    <i className="ri-close-line text-xs"></i>
                  </button>
                </span>
              ))}
              <button
                onClick={() => {
                  setTagsFilter([]);
                  setCurrentPage(1);
                }}
                className="text-sm text-gray-500 hover:text-gray-700 underline"
              >
                {t('contacts.clear_all_tags', 'Clear all')}
              </button>
            </div>
          )}

          {isLoading ? (
            <div className="animate-pulse space-y-4">
              <div className="h-8 bg-gray-200 rounded w-full"></div>
              <div className="h-8 bg-gray-200 rounded w-full"></div>
              <div className="h-8 bg-gray-200 rounded w-full"></div>
              <div className="h-8 bg-gray-200 rounded w-full"></div>
              <div className="h-8 bg-gray-200 rounded w-full"></div>
            </div>
          ) : contacts.length === 0 ? (
            <div className="text-center py-10">
              <div className="text-5xl text-gray-300 mb-3">
                <i className="ri-user-search-line"></i>
              </div>
              <h3 className="text-lg font-medium text-gray-700">{t('contacts.no_contacts_found', 'No contacts found')}</h3>
              <p className="text-gray-500">
                {searchTerm || channelFilter || tagsFilter.length > 0 ?
                  t('contacts.try_adjusting_filters', 'Try adjusting your search or filters') :
                  t('contacts.add_first_contact', 'Add your first contact to get started')}
              </p>
            </div>
          ) : (
            <>
              <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">
                        <Checkbox
                          checked={isAllSelected}
                          onCheckedChange={handleSelectAll}
                          aria-label={t('contacts.bulk_actions.select_all', 'Select all contacts')}
                          className="data-[state=indeterminate]:bg-primary data-[state=indeterminate]:text-primary-foreground"
                          {...(isIndeterminate && { 'data-state': 'indeterminate' })}
                        />
                      </TableHead>
                      <TableHead>{t('contacts.table.name', 'Name')}</TableHead>
                      <TableHead>{t('contacts.table.contact_info', 'Contact Info')}</TableHead>
                      <TableHead>{t('contacts.table.company', 'Company')}</TableHead>
                      <TableHead>{t('contacts.table.channel', 'Channel')}</TableHead>
                      <TableHead>{t('contacts.table.tags', 'Tags')}</TableHead>
                      <TableHead>{t('contacts.table.last_updated', 'Last Updated')}</TableHead>
                      <TableHead>{t('contacts.table.actions', 'Actions')}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {contacts.map((contact: any) => {

                      const contactId = typeof contact.id === 'string' ? parseInt(contact.id, 10) : contact.id;

                      return (
                        <TableRow
                          key={contact.id}
                          className={selectedContacts.has(contactId) ? 'bg-blue-50 border-blue-200' : ''}
                        >
                          <TableCell>
                            <Checkbox
                              checked={selectedContacts.has(contactId)}
                              onCheckedChange={(checked) => handleSelectContact(contactId, checked as boolean)}
                              aria-label={t('contacts.bulk_actions.select_contact', 'Select contact {{name}}', { name: contact.name })}
                            />
                          </TableCell>
                        <TableCell className="font-medium">
                          <div className="flex items-center">
                            <div className="relative mr-3">
                              <img
                                src={contact.avatarUrl || `https://ui-avatars.com/api/?name=${encodeURIComponent(contact.name)}&background=random`}
                                alt={contact.name}
                                className="w-8 h-8 rounded-full"
                              />
                              <span className={`absolute bottom-0 right-0 block h-2 w-2 rounded-full ${contact.isActive ? 'bg-green-500' : 'bg-gray-300'} border border-white`}></span>
                            </div>
                            {contact.name}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            {contact.email && <div className="text-sm">{contact.email}</div>}
                            {contact.phone && <div className="text-sm text-gray-500">{contact.phone}</div>}
                          </div>
                        </TableCell>
                        <TableCell>{contact.company || '-'}</TableCell>
                        <TableCell>
                          <span 
                            className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 cursor-pointer hover:bg-gray-200"
                            onClick={() => handleChannelClick(contact)}
                          >
                            {contact.identifierType === 'whatsapp' && <i className="ri-whatsapp-line text-green-600 mr-1"></i>}
                            {contact.identifierType === 'whatsapp_unofficial' && <i className="ri-whatsapp-line text-green-600 mr-1"></i>}
                            {contact.identifierType === 'messenger' && <i className="ri-messenger-line text-blue-600 mr-1"></i>}
                            {contact.identifierType === 'instagram' && <i className="ri-instagram-line text-pink-600 mr-1"></i>}
                            {contact.identifierType || 'Unknown'}
                          </span>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-wrap gap-1">
                            {contact.tags?.map((tag: string, idx: number) => (
                              <span key={idx} className="px-2 py-0.5 text-xs rounded-full bg-blue-100 text-blue-800">
                                {tag}
                              </span>
                            ))}
                          </div>
                        </TableCell>
                        <TableCell>{formatLastContact(contact.updatedAt)}</TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <button
                              className="p-1 rounded-md hover:bg-gray-100"
                              title={t('contacts.message', 'Message')}
                              onClick={() => handleMessageClick(contact)}
                            >
                              <i className="ri-message-3-line text-blue-500"></i>
                            </button>
                            <button
                              className="p-1 rounded-md hover:bg-gray-100"
                              title={t('common.edit', 'Edit')}
                              onClick={() => handleEditContact(contact)}
                            >
                              <i className="ri-edit-line text-gray-500"></i>
                            </button>
                            <button
                              className="p-1 rounded-md hover:bg-gray-100"
                              title={t('common.delete', 'Delete')}
                              onClick={() => handleDeleteContact(contact.id)}
                            >
                              <i className="ri-delete-bin-line text-red-500"></i>
                            </button>
                          </div>
                        </TableCell>
                      </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>
              
              <Pagination 
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
              />
            </>
          )}
        </div>
      </div>
      
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('contacts.delete_contact', 'Delete Contact')}</AlertDialogTitle>
            <AlertDialogDescription>
              {t('contacts.delete_warning', 'This will permanently delete this contact and all associated conversations, messages, and notes. This action cannot be undone.')}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('common.cancel', 'Cancel')}</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-red-500 hover:bg-red-600"
            >
              {t('common.delete', 'Delete')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Bulk Delete Confirmation Dialog */}
      <AlertDialog open={isBulkDeleteDialogOpen} onOpenChange={setIsBulkDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {t('contacts.bulk_delete.title', 'Delete {{count}} Contacts', { count: selectedContacts.size })}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {t('contacts.bulk_delete.warning', 'This will permanently delete these contacts and all associated conversations, messages, and notes. This action cannot be undone.')}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isBulkDeleting}>
              {t('common.cancel', 'Cancel')}
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmBulkDelete}
              disabled={isBulkDeleting}
              className="bg-red-500 hover:bg-red-600"
            >
              {isBulkDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {t('contacts.bulk_delete.deleting', 'Deleting...')}
                </>
              ) : (
                t('contacts.bulk_delete.confirm', 'Delete {{count}} Contacts', { count: selectedContacts.size })
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      
      <EditContactModal
        contact={selectedContact}
        isOpen={isEditModalOpen}
        onClose={handleEditModalClose}
      />

      {/* Add New Contact Dialog */}
      <Dialog open={isAddContactDialogOpen} onOpenChange={setIsAddContactDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>{t('contacts.add.title', 'Add New Contact')}</DialogTitle>
            <DialogDescription>
              {t('contacts.add.description', 'Create a new contact with the information below.')}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6 pt-4">
            {/* Contact Avatar Upload Section */}
            <div className="flex flex-col items-center space-y-3 p-4 border-2 border-dashed border-gray-200 rounded-lg hover:border-gray-300 transition-colors">
              <div className="w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center">
                <i className="ri-user-line text-2xl text-gray-400"></i>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-600">{t('contacts.add.avatar_upload', 'Upload contact photo')}</p>
                <p className="text-xs text-gray-400">{t('contacts.add.avatar_optional', 'Optional - JPG, PNG up to 5MB')}</p>
              </div>
              <Button variant="outline" size="sm" disabled={isSubmittingContact}>
                <Upload className="h-4 w-4 mr-2" />
                {t('contacts.add.choose_photo', 'Choose Photo')}
              </Button>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="add-name">{t('contacts.add.name_label', 'Name')} *</Label>
                <Input
                  id="add-name"
                  value={addContactForm.name}
                  onChange={(e) => setAddContactForm(prev => ({ ...prev, name: e.target.value }))}
                  placeholder={t('contacts.add.name_placeholder', 'Enter contact name')}
                  disabled={isSubmittingContact}
                  className="focus:ring-2 focus:ring-primary-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="add-email">{t('contacts.add.email_label', 'Email')}</Label>
                <div className="relative">
                  <Input
                    id="add-email"
                    type="email"
                    value={addContactForm.email}
                    onChange={(e) => setAddContactForm(prev => ({ ...prev, email: e.target.value }))}
                    placeholder={t('contacts.add.email_placeholder', 'Enter email address')}
                    disabled={isSubmittingContact}
                    className="focus:ring-2 focus:ring-primary-500"
                  />
                  {addContactForm.email && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      {addContactForm.email.includes('@') && addContactForm.email.includes('.') ? (
                        <i className="ri-check-line text-green-500"></i>
                      ) : (
                        <i className="ri-error-warning-line text-orange-500"></i>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="add-phone">{t('contacts.add.phone_label', 'Phone')}</Label>
                <div className="relative">
                  <Input
                    id="add-phone"
                    type="tel"
                    value={addContactForm.phone}
                    onChange={(e) => setAddContactForm(prev => ({ ...prev, phone: e.target.value }))}
                    placeholder={t('contacts.add.phone_placeholder', '+1234567890')}
                    disabled={isSubmittingContact}
                    className={`pl-10 focus:ring-2 focus:ring-primary-500 ${
                      addContactForm.phone && !validatePhoneNumber(addContactForm.phone).isValid
                        ? 'border-red-500 focus:border-red-500'
                        : addContactForm.phone && checkForDuplicatePhone(addContactForm.phone, contacts || []).isDuplicate
                        ? 'border-yellow-500 focus:border-yellow-500'
                        : ''
                    }`}
                  />
                  <i className="ri-phone-line absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                  {addContactForm.phone && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      {validatePhoneNumber(addContactForm.phone).isValid ? (
                        <i className="ri-check-line text-green-500"></i>
                      ) : (
                        <i className="ri-error-warning-line text-red-500"></i>
                      )}
                    </div>
                  )}
                </div>
                {addContactForm.phone && (
                  <div className="text-xs">
                    {(() => {
                      const phoneValidation = validatePhoneNumber(addContactForm.phone);
                      if (!phoneValidation.isValid) {
                        return (
                          <div className="flex items-center text-red-600">
                            <AlertCircle className="w-3 h-3 mr-1" />
                            {phoneValidation.error}
                          </div>
                        );
                      }

                      const duplicateCheck = checkForDuplicatePhone(addContactForm.phone, contacts || []);
                      if (duplicateCheck.isDuplicate) {
                        return (
                          <div className="flex items-center text-yellow-600">
                            <AlertCircle className="w-3 h-3 mr-1" />
                            Duplicate phone number (existing contact: {duplicateCheck.existingContact?.name})
                          </div>
                        );
                      }

                      return (
                        <div className="flex items-center text-green-600">
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Valid phone number
                        </div>
                      );
                    })()}
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="add-company">{t('contacts.add.company_label', 'Company')}</Label>
                <Input
                  id="add-company"
                  value={addContactForm.company}
                  onChange={(e) => setAddContactForm(prev => ({ ...prev, company: e.target.value }))}
                  placeholder={t('contacts.add.company_placeholder', 'Enter company name')}
                  disabled={isSubmittingContact}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="add-channel">{t('contacts.add.channel_label', 'Channel')}</Label>
                <Select
                  value={addContactForm.identifierType}
                  onValueChange={(value) => setAddContactForm(prev => ({ ...prev, identifierType: value }))}
                  disabled={isSubmittingContact}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={t('contacts.add.select_channel_placeholder', 'Select channel')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="whatsapp_official">{t('contacts.add.channel.whatsapp_official', 'WhatsApp Official')}</SelectItem>
                    <SelectItem value="whatsapp_unofficial">{t('contacts.add.channel.whatsapp_unofficial', 'WhatsApp Unofficial')}</SelectItem>
                    <SelectItem value="messenger">{t('contacts.add.channel.messenger', 'Facebook Messenger')}</SelectItem>
                    <SelectItem value="instagram">{t('contacts.add.channel.instagram', 'Instagram')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="add-identifier">{t('contacts.add.channel_identifier_label', 'Channel Identifier')}</Label>
                <Input
                  id="add-identifier"
                  value={addContactForm.identifier}
                  onChange={(e) => setAddContactForm(prev => ({ ...prev, identifier: e.target.value }))}
                  placeholder={t('contacts.add.channel_identifier_placeholder', 'Phone number or ID')}
                  disabled={isSubmittingContact}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="add-tags">{t('contacts.add.tags_label', 'Tags')}</Label>
              <div className="relative">
                <Input
                  id="add-tags"
                  value={addContactForm.tags}
                  onChange={(e) => setAddContactForm(prev => ({ ...prev, tags: e.target.value }))}
                  placeholder={t('contacts.add.tags_placeholder', 'Type tags separated by commas...')}
                  disabled={isSubmittingContact}
                  className="focus:ring-2 focus:ring-primary-500"
                />
                <i className="ri-price-tag-3-line absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
              </div>
              <div className="flex flex-wrap gap-1 mt-2">
                {['lead', 'customer', 'prospect', 'vip', 'partner'].map((tag) => (
                  <button
                    key={tag}
                    type="button"
                    onClick={() => {
                      const currentTags = addContactForm.tags ? addContactForm.tags.split(',').map(t => t.trim()) : [];
                      if (!currentTags.includes(tag)) {
                        const newTags = [...currentTags, tag].join(', ');
                        setAddContactForm(prev => ({ ...prev, tags: newTags }));
                      }
                    }}
                    className="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full transition-colors"
                    disabled={isSubmittingContact}
                  >
                    + {tag}
                  </button>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="add-notes">{t('contacts.add.notes_label', 'Notes')}</Label>
              <Textarea
                id="add-notes"
                value={addContactForm.notes}
                onChange={(e) => setAddContactForm(prev => ({ ...prev, notes: e.target.value }))}
                placeholder={t('contacts.add.notes_placeholder', 'Additional notes about this contact...')}
                rows={3}
                disabled={isSubmittingContact}
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsAddContactDialogOpen(false);
                resetAddContactForm();
              }}
              disabled={isSubmittingContact}
            >
              {t('common.cancel', 'Cancel')}
            </Button>
            <Button
              onClick={handleAddContactSubmit}
              disabled={isSubmittingContact}
            >
              {isSubmittingContact ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {t('contacts.add.creating', 'Creating...')}
                </>
              ) : (
                <>
                  <Plus className="mr-2 h-4 w-4" />
                  {t('contacts.add.create_button', 'Create Contact')}
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* CSV Import Dialog */}
      <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{t('contacts.import.title', 'Import Contacts from CSV')}</DialogTitle>
            <DialogDescription>
              {t('contacts.import.description', 'Upload a CSV file to import multiple contacts at once. Download the template to see the required format.')}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <Button
                variant="outline"
                onClick={downloadCsvTemplate}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                {t('contacts.import.download_template', 'Download Template')}
              </Button>
            </div>

            <div className="space-y-2">
              <Label>{t('contacts.import.file_label', 'CSV File')}</Label>
              <FileUpload
                onFileSelected={handleFileSelected}
                fileType=".csv"
                maxSize={10} // 10MB limit
                className="w-full"
                showProgress={isImporting}
                progress={importProgress}
              />
              <p className="text-xs text-muted-foreground">
                {t('contacts.import.file_help', 'Maximum file size: 10MB. Only CSV files are supported.')}
              </p>
            </div>

            {showPreview && csvPreview.length > 0 && (
              <div className="space-y-2">
                <Label>{t('contacts.import.preview_label', 'Preview (first 5 rows)')}</Label>
                <div className="border rounded-md overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead className="bg-gray-50">
                      <tr>
                        {Object.keys(csvPreview[0] || {}).filter(header => !header.startsWith('_')).map((header) => (
                          <th key={header} className="px-3 py-2 text-left font-medium">
                            {header}
                          </th>
                        ))}
                        <th className="px-3 py-2 text-left font-medium">Validation</th>
                      </tr>
                    </thead>
                    <tbody>
                      {csvPreview.map((row, index) => (
                        <tr key={index} className={`border-t ${row._warnings?.length > 0 ? 'bg-red-50' : ''}`}>
                          {Object.entries(row).filter(([key]) => !key.startsWith('_')).map(([, value], cellIndex) => (
                            <td key={cellIndex} className="px-3 py-2">
                              {value as string}
                            </td>
                          ))}
                          <td className="px-3 py-2">
                            {row._warnings?.length > 0 ? (
                              <div className="space-y-1">
                                {row._warnings.map((warning: string, wIndex: number) => (
                                  <div key={wIndex} className="flex items-center text-red-600 text-xs">
                                    <AlertCircle className="w-3 h-3 mr-1" />
                                    {warning}
                                  </div>
                                ))}
                              </div>
                            ) : (
                              <div className="flex items-center text-green-600 text-xs">
                                <CheckCircle className="w-3 h-3 mr-1" />
                                Valid
                              </div>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                {csvPreview.some(row => row._warnings?.length > 0) && (
                  <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                    <div className="flex items-center">
                      <AlertCircle className="w-4 h-4 text-yellow-600 mr-2" />
                      <span className="text-sm text-yellow-800">
                        Some rows have validation issues. These contacts may be skipped or cause errors during import.
                      </span>
                    </div>
                  </div>
                )}
              </div>
            )}

            <div className="space-y-2">
              <Label>{t('contacts.import.duplicate_handling_label', 'Duplicate Handling')}</Label>
              <Select
                value={duplicateHandling}
                onValueChange={(value: 'skip' | 'update' | 'create') => setDuplicateHandling(value)}
                disabled={isImporting}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="skip">{t('contacts.import.duplicate.skip', 'Skip duplicates')}</SelectItem>
                  <SelectItem value="update">{t('contacts.import.duplicate.update', 'Update existing')}</SelectItem>
                  <SelectItem value="create">{t('contacts.import.duplicate.create', 'Create new')}</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                {t('contacts.import.duplicate_help', 'How to handle contacts with duplicate email addresses')}
              </p>
            </div>

            {isImporting && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>{t('contacts.import.importing', 'Importing...')}</span>
                  <span>{importProgress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${importProgress}%` }}
                  ></div>
                </div>
              </div>
            )}

            {importResults && (
              <div className="space-y-2">
                <Label>{t('contacts.import.results_label', 'Import Results')}</Label>
                <div className="p-4 border rounded-md bg-gray-50">
                  <div className="flex items-center gap-2 text-green-600 mb-2">
                    <CheckCircle className="h-4 w-4" />
                    <span>{t('contacts.import.successful', 'Successfully imported: {{count}}', { count: importResults.successful })}</span>
                  </div>
                  {importResults.failed > 0 && (
                    <div className="flex items-center gap-2 text-red-600 mb-2">
                      <AlertCircle className="h-4 w-4" />
                      <span>{t('contacts.import.failed', 'Failed to import: {{count}}', { count: importResults.failed })}</span>
                    </div>
                  )}
                  {importResults.errors.length > 0 && (
                    <div className="mt-2">
                      <p className="text-sm font-medium text-gray-700 mb-1">{t('contacts.import.errors', 'Errors:')}</p>
                      <ul className="text-xs text-gray-600 space-y-1">
                        {importResults.errors.slice(0, 5).map((error, index) => (
                          <li key={index}>• {error}</li>
                        ))}
                        {importResults.errors.length > 5 && (
                          <li>• {t('contacts.import.more_errors', 'And {{count}} more errors...', { count: importResults.errors.length - 5 })}</li>
                        )}
                      </ul>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsImportDialogOpen(false);
                resetImportForm();
              }}
              disabled={isImporting}
            >
              {importResults ? t('common.close', 'Close') : t('common.cancel', 'Cancel')}
            </Button>
            {!importResults && (
              <Button
                onClick={handleImportSubmit}
                disabled={!importFile || isImporting}
              >
                {isImporting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {t('contacts.import.importing', 'Importing...')}
                  </>
                ) : (
                  <>
                    <Upload className="mr-2 h-4 w-4" />
                    {t('contacts.import.import_button', 'Import Contacts')}
                  </>
                )}
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Contact Export Modal */}
      <ContactExportModal
        isOpen={isExportModalOpen}
        onClose={() => setIsExportModalOpen(false)}
        currentFilters={{
          search: debouncedSearch,
          channel: channelFilter
        }}
      />

      {/* Create Segment Modal */}
      <CreateSegmentFromContactsModal
        isOpen={isCreateSegmentModalOpen}
        onClose={() => setIsCreateSegmentModalOpen(false)}
        selectedContactIds={Array.from(selectedContacts)}
        onSegmentCreated={handleSegmentCreated}
      />

      {/* WhatsApp Scraping Modal */}
      <WhatsAppScrapingModal
        isOpen={isWhatsAppScrapingModalOpen}
        onClose={() => setIsWhatsAppScrapingModalOpen(false)}
      />
    </div>
  );
}
