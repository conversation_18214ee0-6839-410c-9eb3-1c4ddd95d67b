import { useState, useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';
import { useTranslation } from '@/hooks/use-translation';

interface ErrorState {
  hasError: boolean;
  error: Error | null;
  isRetrying: boolean;
  retryCount: number;
}

interface UseMessengerErrorOptions {
  maxRetries?: number;
  showToast?: boolean;
  onError?: (error: Error) => void;
  onRetry?: () => void;
}

export const useMessengerError = (options: UseMessengerErrorOptions = {}) => {
  const { maxRetries = 3, showToast = true, onError, onRetry } = options;
  const { toast } = useToast();
  const { t } = useTranslation();

  const [errorState, setErrorState] = useState<ErrorState>({
    hasError: false,
    error: null,
    isRetrying: false,
    retryCount: 0
  });

  const handleError = useCallback((error: Error | string) => {
    const errorObj = typeof error === 'string' ? new Error(error) : error;
    
    setErrorState(prev => ({
      hasError: true,
      error: errorObj,
      isRetrying: false,
      retryCount: prev.retryCount
    }));

    if (showToast) {
      toast({
        title: t('messenger.error', 'Error'),
        description: getErrorMessage(errorObj),
        variant: 'destructive',
      });
    }

    onError?.(errorObj);
  }, [toast, t, showToast, onError]);

  const retry = useCallback(async (retryFn: () => Promise<void> | void) => {
    if (errorState.retryCount >= maxRetries) {
      if (showToast) {
        toast({
          title: t('messenger.max_retries', 'Maximum Retries Reached'),
          description: t('messenger.max_retries_desc', 'Please refresh the page or contact support.'),
          variant: 'destructive',
        });
      }
      return;
    }

    setErrorState(prev => ({
      ...prev,
      isRetrying: true,
      retryCount: prev.retryCount + 1
    }));

    try {
      await retryFn();
      

      setErrorState({
        hasError: false,
        error: null,
        isRetrying: false,
        retryCount: 0
      });

      if (showToast) {
        toast({
          title: t('messenger.retry_success', 'Retry Successful'),
          description: t('messenger.retry_success_desc', 'Operation completed successfully.'),
          variant: 'default',
        });
      }

      onRetry?.();
    } catch (error: any) {
      setErrorState(prev => ({
        hasError: true,
        error: error,
        isRetrying: false,
        retryCount: prev.retryCount
      }));

      if (showToast) {
        toast({
          title: t('messenger.retry_failed', 'Retry Failed'),
          description: getErrorMessage(error),
          variant: 'destructive',
        });
      }
    }
  }, [errorState.retryCount, maxRetries, toast, t, showToast, onRetry]);

  const clearError = useCallback(() => {
    setErrorState({
      hasError: false,
      error: null,
      isRetrying: false,
      retryCount: 0
    });
  }, []);

  const canRetry = errorState.retryCount < maxRetries && !errorState.isRetrying;

  return {
    ...errorState,
    handleError,
    retry,
    clearError,
    canRetry
  };
};

/**
 * Get user-friendly error message
 */
const getErrorMessage = (error: Error): string => {

  if (error.message.includes('fetch') || error.message.includes('NetworkError')) {
    return 'Network connection failed. Please check your internet connection.';
  }


  if (error.name === 'AbortError' || error.message.includes('timeout')) {
    return 'Request timed out. Please try again.';
  }


  if (error.message.includes('HTTP 5')) {
    return 'Server error occurred. Please try again later.';
  }


  if (error.message.includes('HTTP 429')) {
    return 'Too many requests. Please wait a moment and try again.';
  }


  if (error.message.includes('HTTP 401') || error.message.includes('Unauthorized')) {
    return 'Authentication failed. Please refresh the page and try again.';
  }


  if (error.message.includes('HTTP 403') || error.message.includes('Access denied')) {
    return 'Access denied. You may not have permission to perform this action.';
  }


  if (error.message.includes('HTTP 404')) {
    return 'Resource not found. It may have been deleted or moved.';
  }


  if (error.message.includes('HTTP 400')) {
    return error.message.replace(/^HTTP \d+:\s*/, '') || 'Invalid request. Please check your input.';
  }


  return error.message || 'An unexpected error occurred. Please try again.';
};

export default useMessengerError;
