import Header from '@/components/layout/Header';
import Sidebar from '@/components/layout/Sidebar';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, Pie<PERSON>hart, Pie, Cell } from 'recharts';
import { useState } from 'react';
import { Loader2, Download, Calendar as CalendarIcon, TrendingUp, TrendingDown, Users, MessageSquare, CheckCircle, AlertCircle } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useTranslation } from '@/hooks/use-translation';
import { format, subDays, startOfDay } from 'date-fns';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';


interface AnalyticsOverview {
  conversationsCount: number;
  contactsCount: number;
  messagesCount: number;
  responseRate: number;
  avgResponseTime?: number;
  activeUsers?: number;
  conversationsGrowth?: number;
  contactsGrowth?: number;
  messagesGrowth?: number;
  responseRateGrowth?: number;
}

interface ConversationsByDay {
  name: string;
  whatsapp_official: number;
  whatsapp_unofficial: number;
  messenger: number;
  instagram: number;
  total: number;
}

interface ChannelDistribution {
  name: string;
  value: number;
  percentage: number;
}







interface AnalyticsData {
  overview: AnalyticsOverview;
  conversationsByDay: ConversationsByDay[];
  channelDistribution: ChannelDistribution[];
  messagesByChannel: any[];
  conversionFunnel?: any[];
  userActivity?: any[];
}

export default function Analytics() {
  const [timePeriod, setTimePeriod] = useState('7days');
  const [dateRange, setDateRange] = useState<{ from: Date; to: Date }>({
    from: subDays(new Date(), 7),
    to: new Date()
  });
  const [isCustomDateRange, setIsCustomDateRange] = useState(false);

  const { t } = useTranslation();
  const { toast } = useToast();


  const { data: analyticsData, isLoading: loading, error } = useQuery<AnalyticsData>({
    queryKey: ['/api/analytics/overview', timePeriod, dateRange],
    queryFn: async () => {
      const params = new URLSearchParams();
      params.append('period', timePeriod);

      if (isCustomDateRange && dateRange.from && dateRange.to) {
        params.append('from', dateRange.from.toISOString());
        params.append('to', dateRange.to.toISOString());
      }

      const response = await apiRequest('GET', `/api/analytics/overview?${params.toString()}`);

      if (!response.ok) {
        throw new Error('Failed to fetch analytics data');
      }

      return response.json();
    },
    refetchOnWindowFocus: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });


  const handleTimePeriodChange = (period: string) => {
    setTimePeriod(period);
    if (period !== 'custom') {
      setIsCustomDateRange(false);

      const now = new Date();
      let from = new Date();

      switch (period) {
        case 'today':
          from = startOfDay(now);
          break;
        case 'yesterday':
          from = startOfDay(subDays(now, 1));
          break;
        case '7days':
          from = subDays(now, 7);
          break;
        case '30days':
          from = subDays(now, 30);
          break;
        case '90days':
          from = subDays(now, 90);
          break;
        default:
          from = subDays(now, 7);
      }

      setDateRange({ from, to: now });
    } else {
      setIsCustomDateRange(true);
    }
  };

 

  const conversationData = analyticsData?.conversationsByDay || [
    { name: 'Mon', whatsapp_official: 0, whatsapp_unofficial: 0, messenger: 0, instagram: 0 },
    { name: 'Tue', whatsapp_official: 0, whatsapp_unofficial: 0, messenger: 0, instagram: 0 },
    { name: 'Wed', whatsapp_official: 0, whatsapp_unofficial: 0, messenger: 0, instagram: 0 },
    { name: 'Thu', whatsapp_official: 0, whatsapp_unofficial: 0, messenger: 0, instagram: 0 },
    { name: 'Fri', whatsapp_official: 0, whatsapp_unofficial: 0, messenger: 0, instagram: 0 },
    { name: 'Sat', whatsapp_official: 0, whatsapp_unofficial: 0, messenger: 0, instagram: 0 },
    { name: 'Sun', whatsapp_official: 0, whatsapp_unofficial: 0, messenger: 0, instagram: 0 },
  ];
  
  const formattedConversationData = conversationData.map((item: any) => {
    if (item.name && item.name.includes('-')) {
      const date = new Date(item.name);
      return {
        ...item,
        name: date.toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' })
      };
    }
    return item;
  });
  
  const channelDistributionData = analyticsData?.channelDistribution || [
    { name: 'WhatsApp Official', value: 0 },
    { name: 'WhatsApp Unofficial', value: 0 },
    { name: 'Messenger', value: 0 },
    { name: 'Instagram', value: 0 },
  ];

  const COLORS = ['#25D366', '#F59E0B', '#1877F2', '#E4405F'];
  
  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: any) => {
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * Math.PI / 180);
    const y = cy + radius * Math.sin(-midAngle * Math.PI / 180);
  
    return (
      <text 
        x={x} 
        y={y} 
        fill="white" 
        textAnchor="middle" 
        dominantBaseline="central"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };
  
  return (
    <div className="h-screen flex flex-col overflow-hidden font-sans text-gray-800">
      <Header />
      
      <div className="flex flex-1 overflow-hidden">
        <Sidebar />
        
        <div className="flex-1 overflow-y-auto p-3 sm:p-6">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 gap-4">
            <h1 className="text-2xl">
              {t('analytics.title', 'Analytics')}
            </h1>
            <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3">
              <Select value={timePeriod} onValueChange={handleTimePeriodChange}>
                <SelectTrigger className="w-full sm:w-[180px] h-10">
                  <SelectValue placeholder={t('analytics.select_period', 'Select time period')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="today">{t('analytics.period.today', 'Today')}</SelectItem>
                  <SelectItem value="yesterday">{t('analytics.period.yesterday', 'Yesterday')}</SelectItem>
                  <SelectItem value="7days">{t('analytics.period.7days', 'Last 7 days')}</SelectItem>
                  <SelectItem value="30days">{t('analytics.period.30days', 'Last 30 days')}</SelectItem>
                  <SelectItem value="90days">{t('analytics.period.90days', 'Last 90 days')}</SelectItem>
                  <SelectItem value="custom">{t('analytics.period.custom', 'Custom Range')}</SelectItem>
                </SelectContent>
              </Select>

              {isCustomDateRange && (
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full sm:w-[280px] justify-start text-left font-normal h-10",
                        !dateRange && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateRange?.from ? (
                        dateRange.to ? (
                          <>
                            {format(dateRange.from, "LLL dd, y")} -{" "}
                            {format(dateRange.to, "LLL dd, y")}
                          </>
                        ) : (
                          format(dateRange.from, "LLL dd, y")
                        )
                      ) : (
                        <span>{t('analytics.pick_date', 'Pick a date')}</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      initialFocus
                      mode="range"
                      defaultMonth={dateRange?.from}
                      selected={dateRange}
                      onSelect={(range) => {
                        if (range?.from && range?.to) {
                          setDateRange({ from: range.from, to: range.to });
                        }
                      }}
                      numberOfMonths={2}
                    />
                  </PopoverContent>
                </Popover>
              )}
            </div>
          </div>
          
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">Loading analytics data...</span>
            </div>
          ) : error ? (
            <div className="flex flex-col justify-center items-center h-64 text-red-500 space-y-2">
              <AlertCircle className="h-8 w-8" />
              <p className="text-center">{error instanceof Error ? error.message : 'Failed to load analytics data'}</p>
              <Button
                variant="outline"
                onClick={() => window.location.reload()}
                className="mt-4"
              >
                {t('common.retry', 'Retry')}
              </Button>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6 mb-6">
                <Card className="hover:shadow-md transition-shadow">
                  <CardContent className="pt-6">
                    <div className="flex justify-between items-start">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm text-gray-500 truncate">
                          {t('analytics.cards.total_conversations', 'Total Conversations')}
                        </p>
                        <p className="text-2xl sm:text-3xl font-bold mt-1 text-gray-900">
                          {analyticsData?.overview?.conversationsCount?.toLocaleString() || 0}
                        </p>
                      </div>
                      <div className="p-2 bg-green-100 text-green-700 rounded-md flex-shrink-0">
                        <MessageSquare className="h-5 w-5" />
                      </div>
                    </div>
                    <div className="flex items-center mt-3 text-sm">
                      <span className="text-green-500 flex items-center">
                        <TrendingUp className="h-4 w-4 mr-1" />
                        {analyticsData?.overview?.conversationsGrowth || 0}%
                      </span>
                      <span className="text-gray-500 ml-2">
                        {t('analytics.vs_last_period', 'vs last period')}
                      </span>
                    </div>
                  </CardContent>
                </Card>
                
                <Card className="hover:shadow-md transition-shadow">
                  <CardContent className="pt-6">
                    <div className="flex justify-between items-start">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm text-gray-500 truncate">
                          {t('analytics.cards.total_contacts', 'Total Contacts')}
                        </p>
                        <p className="text-2xl sm:text-3xl font-bold mt-1 text-gray-900">
                          {analyticsData?.overview?.contactsCount?.toLocaleString() || 0}
                        </p>
                      </div>
                      <div className="p-2 bg-blue-100 text-blue-700 rounded-md flex-shrink-0">
                        <Users className="h-5 w-5" />
                      </div>
                    </div>
                    <div className="flex items-center mt-3 text-sm">
                      <span className={cn(
                        "flex items-center",
                        (analyticsData?.overview?.contactsGrowth || 0) >= 0 ? "text-green-500" : "text-red-500"
                      )}>
                        {(analyticsData?.overview?.contactsGrowth || 0) >= 0 ? (
                          <TrendingUp className="h-4 w-4 mr-1" />
                        ) : (
                          <TrendingDown className="h-4 w-4 mr-1" />
                        )}
                        {Math.abs(analyticsData?.overview?.contactsGrowth || 0)}%
                      </span>
                      <span className="text-gray-500 ml-2">
                        {t('analytics.vs_last_period', 'vs last period')}
                      </span>
                    </div>
                  </CardContent>
                </Card>
                
                <Card className="hover:shadow-md transition-shadow">
                  <CardContent className="pt-6">
                    <div className="flex justify-between items-start">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm text-gray-500 truncate">
                          {t('analytics.cards.total_messages', 'Total Messages')}
                        </p>
                        <p className="text-2xl sm:text-3xl font-bold mt-1 text-gray-900">
                          {analyticsData?.overview?.messagesCount?.toLocaleString() || 0}
                        </p>
                      </div>
                      <div className="p-2 bg-purple-100 text-purple-700 rounded-md flex-shrink-0">
                        <MessageSquare className="h-5 w-5" />
                      </div>
                    </div>
                    <div className="flex items-center mt-3 text-sm">
                      <span className={cn(
                        "flex items-center",
                        (analyticsData?.overview?.messagesGrowth || 0) >= 0 ? "text-green-500" : "text-red-500"
                      )}>
                        {(analyticsData?.overview?.messagesGrowth || 0) >= 0 ? (
                          <TrendingUp className="h-4 w-4 mr-1" />
                        ) : (
                          <TrendingDown className="h-4 w-4 mr-1" />
                        )}
                        {Math.abs(analyticsData?.overview?.messagesGrowth || 0)}%
                      </span>
                      <span className="text-gray-500 ml-2">
                        {t('analytics.vs_last_period', 'vs last period')}
                      </span>
                    </div>
                  </CardContent>
                </Card>

                <Card className="hover:shadow-md transition-shadow">
                  <CardContent className="pt-6">
                    <div className="flex justify-between items-start">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm text-gray-500 truncate">
                          {t('analytics.cards.response_rate', 'Response Rate')}
                        </p>
                        <p className="text-2xl sm:text-3xl font-bold mt-1 text-gray-900">
                          {analyticsData?.overview?.responseRate || 0}%
                        </p>
                      </div>
                      <div className="p-2 bg-emerald-100 text-emerald-700 rounded-md flex-shrink-0">
                        <CheckCircle className="h-5 w-5" />
                      </div>
                    </div>
                    <div className="flex items-center mt-3 text-sm">
                      <span className={cn(
                        "flex items-center",
                        (analyticsData?.overview?.responseRateGrowth || 0) >= 0 ? "text-green-500" : "text-red-500"
                      )}>
                        {(analyticsData?.overview?.responseRateGrowth || 0) >= 0 ? (
                          <TrendingUp className="h-4 w-4 mr-1" />
                        ) : (
                          <TrendingDown className="h-4 w-4 mr-1" />
                        )}
                        {Math.abs(analyticsData?.overview?.responseRateGrowth || 0)}%
                      </span>
                      <span className="text-gray-500 ml-2">
                        {t('analytics.vs_last_period', 'vs last period')}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </div>
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 sm:gap-6 mb-6">
                <Card className="hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg sm:text-xl">
                      {t('analytics.charts.conversations_by_channel', 'Conversations by Channel')}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="w-full overflow-x-auto">
                      <ResponsiveContainer width="100%" height={300} minWidth={300}>
                        <LineChart data={formattedConversationData} margin={{ top: 20, right: 30, left: 0, bottom: 0 }}>
                          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                          <XAxis
                            dataKey="name"
                            tick={{ fontSize: 12 }}
                            tickLine={{ stroke: '#e0e0e0' }}
                          />
                          <YAxis
                            tick={{ fontSize: 12 }}
                            tickLine={{ stroke: '#e0e0e0' }}
                          />
                          <Tooltip
                            contentStyle={{
                              backgroundColor: '#fff',
                              border: '1px solid #e0e0e0',
                              borderRadius: '8px',
                              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                            }}
                          />
                          <Legend />
                          <Line
                            type="monotone"
                            dataKey="whatsapp_official"
                            name={t('analytics.channels.whatsapp_official', 'WhatsApp Official')}
                            stroke="#25D366"
                            strokeWidth={2}
                            dot={{ fill: '#25D366', strokeWidth: 2, r: 4 }}
                            activeDot={{ r: 6, stroke: '#25D366', strokeWidth: 2 }}
                          />
                          <Line
                            type="monotone"
                            dataKey="whatsapp_unofficial"
                            name={t('analytics.channels.whatsapp_unofficial', 'WhatsApp Unofficial')}
                            stroke="#F59E0B"
                            strokeWidth={2}
                            dot={{ fill: '#F59E0B', strokeWidth: 2, r: 4 }}
                            activeDot={{ r: 6, stroke: '#F59E0B', strokeWidth: 2 }}
                          />
                          <Line
                            type="monotone"
                            dataKey="messenger"
                            name={t('analytics.channels.messenger', 'Messenger')}
                            stroke="#1877F2"
                            strokeWidth={2}
                            dot={{ fill: '#1877F2', strokeWidth: 2, r: 4 }}
                            activeDot={{ r: 6, stroke: '#1877F2', strokeWidth: 2 }}
                          />
                          <Line
                            type="monotone"
                            dataKey="instagram"
                            name={t('analytics.channels.instagram', 'Instagram')}
                            stroke="#E4405F"
                            strokeWidth={2}
                            dot={{ fill: '#E4405F', strokeWidth: 2, r: 4 }}
                            activeDot={{ r: 6, stroke: '#E4405F', strokeWidth: 2 }}
                          />
                        </LineChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>
                
                <Card className="hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg sm:text-xl">
                      {t('analytics.charts.channel_distribution', 'Channel Distribution')}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="w-full overflow-x-auto">
                      <ResponsiveContainer width="100%" height={300} minWidth={300}>
                        <PieChart>
                          <Tooltip
                            contentStyle={{
                              backgroundColor: '#fff',
                              border: '1px solid #e0e0e0',
                              borderRadius: '8px',
                              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                            }}
                          />
                          <Legend />
                          <Pie
                            data={channelDistributionData}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={renderCustomizedLabel}
                            outerRadius={100}
                            fill="#8884d8"
                            dataKey="value"
                          >
                            {channelDistributionData.map((_: { name: string, value: number }, index: number) => (
                              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                            ))}
                          </Pie>
                        </PieChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
