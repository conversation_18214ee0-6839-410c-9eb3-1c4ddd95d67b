import { useEffect } from 'react';
import { useAuth } from '@/hooks/use-auth';
import { useLocation } from 'wouter';
import { useQuery } from '@tanstack/react-query';
import { Loader2 } from 'lucide-react';

export default function RootRedirect() {
  const { user, isLoading: authLoading } = useAuth();
  const [_, setLocation] = useLocation();


  const { data: websiteSettings, isLoading: websiteLoading } = useQuery({
    queryKey: ['/api/public/website-enabled'],
    queryFn: async () => {
      try {
        const res = await fetch('/api/public/website-enabled');
        if (!res.ok) {
          return { enabled: true };
        }
        return res.json();
      } catch {
        return { enabled: true };
      }
    },
    retry: false,
    staleTime: 5 * 60 * 1000,
  });

  useEffect(() => {
    if (authLoading || websiteLoading) return;


    if (user) {
      if (user.isSuperAdmin) {
        setLocation('/admin/dashboard');
      } else {
        setLocation('/inbox');
      }
      return;
    }


    if (websiteSettings?.enabled) {
      setLocation('/landing');
      return;
    }


    setLocation('/auth');
  }, [user, authLoading, websiteSettings, websiteLoading, setLocation]);


  return (
    <div className="min-h-screen bg-gray-900 flex items-center justify-center">
      <div className="text-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
        <p className="text-white">Loading...</p>
      </div>
    </div>
  );
}
