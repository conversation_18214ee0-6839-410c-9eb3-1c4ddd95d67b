#!/bin/bash

# find-replace.sh - Case-sensitive find and replace in ALL files
# Author: PowerChat Plus Development Team
# Version: 1.0
# Note: Processes all files by default, regardless of extension

set -euo pipefail

# Color codes for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly CYAN='\033[0;36m'
readonly PURPLE='\033[0;35m'
readonly NC='\033[0m' # No Color

# Default configuration
DEFAULT_EXTENSIONS="*"  # Process all files by default
DRY_RUN=false
CREATE_BACKUP=false
EXTENSIONS="$DEFAULT_EXTENSIONS"
VERBOSE=false

# Directories to skip (common build/dependency directories)
SKIP_DIRS=("node_modules")

# Statistics
FILES_PROCESSED=0
FILES_MODIFIED=0
TOTAL_REPLACEMENTS=0

# Print functions
print_header() {
    echo -e "\n${PURPLE}🔍 Find and Replace Tool${NC}\n"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

show_usage() {
    cat << EOF
Find and Replace Tool - Case-sensitive text replacement in files

Usage: $0 <directory> <search_text> <replacement_text> [options]

Arguments:
  directory         Target directory to search (required)
  search_text       Text to find (case-sensitive, required)
  replacement_text  Text to replace with (required)

Options:
  -n, --dry-run           Show what would be changed without making changes
  -b, --backup            Create backup files (.bak) before making changes
  -e, --extensions LIST   Comma-separated list of file extensions to target
                         Default: * (all files)
  -v, --verbose          Show detailed processing information
  -h, --help             Show this help message

Automatically skipped directories:
  ${SKIP_DIRS[*]}

Examples:
  $0 /path/to/project "oldFunction" "newFunction"
  $0 ./src "TODO:" "DONE:" --dry-run
  $0 /var/www "localhost" "example.com" --backup
  $0 ./docs "v1.0" "v2.0" --extensions "md,txt"
  $0 ./config "old_value" "new_value" --extensions "*"

Safety Features:
  - No backup files created by default (use --backup to enable)
  - Automatically skips binary files and common build directories
  - Dry-run mode available for safe testing
  - Processes all files by default (use --extensions to limit)

EOF
}

# Validate if a file is a text file (not binary)
is_text_file() {
    local file="$1"
    
    # Check if file exists and is readable
    if [[ ! -f "$file" || ! -r "$file" ]]; then
        return 1
    fi
    
    # Use file command to check if it's text
    if file "$file" | grep -q "text\|empty"; then
        return 0
    fi
    
    # Additional check using head command
    if head -c 1024 "$file" | LC_ALL=C grep -q '[[:cntrl:]]' 2>/dev/null; then
        # Contains control characters, likely binary
        return 1
    fi
    
    return 0
}

# Check if file extension is in the target list
is_target_extension() {
    local file="$1"

    # If extensions is "*" or empty, process all files
    if [[ "$EXTENSIONS" == "*" || -z "$EXTENSIONS" ]]; then
        return 0
    fi

    local filename=$(basename "$file")
    local extension="${filename##*.}"

    # If no extension and we're filtering by extension, include files without extensions
    if [[ "$filename" == "$extension" ]]; then
        # Check if "no-ext" or similar is in the extensions list
        IFS=',' read -ra EXT_ARRAY <<< "$EXTENSIONS"
        for ext in "${EXT_ARRAY[@]}"; do
            if [[ "${ext,,}" == "no-ext" || "${ext,,}" == "noext" || "${ext,,}" == "" ]]; then
                return 0
            fi
        done
        return 1
    fi

    # Convert extensions to array and check
    IFS=',' read -ra EXT_ARRAY <<< "$EXTENSIONS"
    for ext in "${EXT_ARRAY[@]}"; do
        if [[ "${extension,,}" == "${ext,,}" ]]; then
            return 0
        fi
    done

    return 1
}

# Create backup of a file
create_backup() {
    local file="$1"
    local backup_file="${file}.bak"
    
    if [[ "$CREATE_BACKUP" == true ]]; then
        if cp "$file" "$backup_file" 2>/dev/null; then
            if [[ "$VERBOSE" == true ]]; then
                print_info "Created backup: $backup_file"
            fi
            return 0
        else
            print_error "Failed to create backup for: $file"
            return 1
        fi
    fi
    
    return 0
}

# Process a single file
process_file() {
    local file="$1"
    local search_text="$2"
    local replacement_text="$3"
    
    FILES_PROCESSED=$((FILES_PROCESSED + 1))
    
    if [[ "$VERBOSE" == true ]]; then
        print_step "Processing: $file"
    fi
    
    # Check if file is a target extension
    if ! is_target_extension "$file"; then
        if [[ "$VERBOSE" == true ]]; then
            print_info "Skipping (extension): $file"
        fi
        return 0
    fi
    
    # Check if file is text
    if ! is_text_file "$file"; then
        if [[ "$VERBOSE" == true ]]; then
            print_warning "Skipping (binary): $file"
        fi
        return 0
    fi
    
    # Count occurrences in the file
    local count
    count=$(grep -o -F "$search_text" "$file" 2>/dev/null | wc -l 2>/dev/null || echo "0")
    # Ensure count is a valid number and remove any whitespace
    count=$(echo "$count" | tr -d '[:space:]' | grep -E '^[0-9]+$' || echo "0")

    if [[ "$count" -eq 0 ]]; then
        if [[ "$VERBOSE" == true ]]; then
            print_info "No matches found in: $file"
        fi
        return 0
    fi
    
    print_info "Found $count occurrence(s) in: $file"
    
    if [[ "$DRY_RUN" == true ]]; then
        print_warning "[DRY RUN] Would replace $count occurrence(s) in: $file"
        TOTAL_REPLACEMENTS=$((TOTAL_REPLACEMENTS + ${count:-0}))
        FILES_MODIFIED=$((FILES_MODIFIED + 1))
        return 0
    fi
    
    # Create backup before modifying (only if enabled)
    if [[ "$CREATE_BACKUP" == true ]]; then
        if ! create_backup "$file"; then
            return 1
        fi
    fi
    
    # Perform the replacement using a more robust approach
    # Create temporary files for search and replacement text to avoid shell escaping issues
    local search_file=$(mktemp)
    local replace_file=$(mktemp)
    printf '%s' "$search_text" > "$search_file"
    printf '%s' "$replacement_text" > "$replace_file"

    # Use awk for safer text replacement
    if awk -v search="$search_text" -v replace="$replacement_text" '
        {
            gsub(search, replace)
            print
        }
    ' "$file" > "${file}.tmp" 2>/dev/null; then
        # Clean up temp files
        rm -f "$search_file" "$replace_file"
        if mv "${file}.tmp" "$file"; then
            print_success "Replaced $count occurrence(s) in: $file"
            TOTAL_REPLACEMENTS=$((TOTAL_REPLACEMENTS + ${count:-0}))
            FILES_MODIFIED=$((FILES_MODIFIED + 1))
        else
            print_error "Failed to update file: $file"
            rm -f "${file}.tmp"
            return 1
        fi
    else
        # Clean up temp files on error
        rm -f "$search_file" "$replace_file"
        print_error "Failed to process file: $file"
        rm -f "${file}.tmp"
        return 1
    fi
    
    return 0
}

# Main processing function
process_directory() {
    local directory="$1"
    local search_text="$2"
    local replacement_text="$3"

    print_step "Searching for files in: $directory"

    if [[ "$VERBOSE" == true ]]; then
        print_info "Skipping directories: ${SKIP_DIRS[*]}"
    fi

    # Build find command with exclusions
    local find_cmd="find \"$directory\" -type f"

    # Add exclusions for skip directories
    for skip_dir in "${SKIP_DIRS[@]}"; do
        find_cmd="$find_cmd -not -path \"*/$skip_dir/*\""
    done

    find_cmd="$find_cmd -print0"

    # Find all files recursively (excluding skip directories)
    local file_count=0
    while IFS= read -r -d '' file; do
        file_count=$((file_count + 1))
    done < <(eval "$find_cmd" 2>/dev/null)

    if [[ "$file_count" -eq 0 ]]; then
        print_warning "No files found in directory: $directory"
        return 0
    fi

    print_info "Found $file_count files to examine"
    echo

    # Process each file
    local current_file=0
    while IFS= read -r -d '' file; do
        current_file=$((current_file + 1))

        if [[ "$VERBOSE" == false ]]; then
            printf "\rProgress: %d/%d files processed" "$current_file" "$file_count"
        fi

        process_file "$file" "$search_text" "$replacement_text"
    done < <(eval "$find_cmd" 2>/dev/null)

    if [[ "$VERBOSE" == false ]]; then
        echo # New line after progress
    fi
}

# Print summary
print_summary() {
    echo
    echo -e "${PURPLE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    echo -e "${PURPLE}Summary${NC}"
    echo -e "${PURPLE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    echo -e "Files examined: $FILES_PROCESSED"
    echo -e "${GREEN}Files modified: $FILES_MODIFIED${NC}"
    echo -e "${GREEN}Total replacements: $TOTAL_REPLACEMENTS${NC}"

    if [[ "$DRY_RUN" == true ]]; then
        echo -e "${YELLOW}Mode: DRY RUN (no changes made)${NC}"
    else
        echo -e "Mode: LIVE (changes applied)"
        if [[ "$CREATE_BACKUP" == true && "$FILES_MODIFIED" -gt 0 ]]; then
            echo -e "${BLUE}Backup files created with .bak extension${NC}"
        fi
    fi
    echo
}

# Validate inputs
validate_inputs() {
    local directory="$1"
    local search_text="$2"
    local replacement_text="$3"

    # Check if directory exists
    if [[ ! -d "$directory" ]]; then
        print_error "Directory does not exist: $directory"
        return 1
    fi

    # Check if directory is readable
    if [[ ! -r "$directory" ]]; then
        print_error "Directory is not readable: $directory"
        return 1
    fi

    # Check if search text is not empty
    if [[ -z "$search_text" ]]; then
        print_error "Search text cannot be empty"
        return 1
    fi

    # Replacement text can be empty (for deletion)

    return 0
}

# Parse command line arguments
parse_arguments() {
    local directory=""
    local search_text=""
    local replacement_text=""
    local positional_args=()

    while [[ $# -gt 0 ]]; do
        case $1 in
            -n|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -b|--backup)
                CREATE_BACKUP=true
                shift
                ;;
            -e|--extensions)
                if [[ -n "${2:-}" ]]; then
                    EXTENSIONS="$2"
                    shift 2
                else
                    print_error "Option --extensions requires an argument"
                    return 1
                fi
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            -*)
                print_error "Unknown option: $1"
                return 1
                ;;
            *)
                positional_args+=("$1")
                shift
                ;;
        esac
    done

    # Check if we have the required positional arguments
    if [[ ${#positional_args[@]} -lt 3 ]]; then
        print_error "Missing required arguments"
        echo
        show_usage
        return 1
    fi

    directory="${positional_args[0]}"
    search_text="${positional_args[1]}"
    replacement_text="${positional_args[2]}"

    # Validate inputs
    if ! validate_inputs "$directory" "$search_text" "$replacement_text"; then
        return 1
    fi

    # Export for use in main function
    export TARGET_DIRECTORY="$directory"
    export SEARCH_TEXT="$search_text"
    export REPLACEMENT_TEXT="$replacement_text"

    return 0
}

# Main function
main() {
    # Parse command line arguments
    if ! parse_arguments "$@"; then
        exit 1
    fi

    print_header

    # Display configuration
    print_info "Configuration:"
    echo "  Directory: $TARGET_DIRECTORY"
    echo "  Search: '$SEARCH_TEXT'"
    echo "  Replace: '$REPLACEMENT_TEXT'"
    echo "  Extensions: $EXTENSIONS"
    echo "  Dry run: $DRY_RUN"
    echo "  Create backups: $CREATE_BACKUP"
    echo "  Verbose: $VERBOSE"
    echo

    # Confirm operation if not in dry-run mode
    if [[ "$DRY_RUN" == false ]]; then
        print_warning "This will modify files in the target directory!"
        read -p "Continue? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_info "Operation cancelled by user"
            exit 0
        fi
        echo
    fi

    # Start processing
    local start_time=$(date +%s)

    if ! process_directory "$TARGET_DIRECTORY" "$SEARCH_TEXT" "$REPLACEMENT_TEXT"; then
        print_error "Processing failed"
        exit 1
    fi

    local end_time=$(date +%s)
    local duration=$((end_time - start_time))

    # Print summary
    print_summary
    print_info "Processing completed in ${duration}s"

    # Exit with appropriate code
    if [[ "$FILES_MODIFIED" -gt 0 ]]; then
        exit 0
    else
        print_warning "No files were modified"
        exit 0
    fi
}

# Trap to handle interruption
trap 'echo -e "\n${RED}Operation interrupted by user${NC}"; exit 130' INT TERM

# Check if script is being sourced or executed
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    # Script is being executed directly
    if [[ $# -eq 0 ]]; then
        show_usage
        exit 1
    fi

    main "$@"
fi
