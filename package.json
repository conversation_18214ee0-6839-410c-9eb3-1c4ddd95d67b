{"name": "rest-express", "version": "2.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\" --names \"backend,frontend\" --prefix-colors \"blue,green\" --kill-others-on-fail", "dev:integrated": "cross-env NODE_ENV=development tsx watch --clear-screen=false server/dev-server.ts", "dev:backend": "cross-env NODE_ENV=development tsx watch --clear-screen=false server/dev-server.ts", "dev:frontend": "vite", "dev:clean": "npm run clean && npm run dev", "clean": "rimraf dist node_modules/.vite", "dev:old": "npm run build:dev && cross-env NODE_ENV=development NODE_TLS_REJECT_UNAUTHORIZED=0 tsx --require dotenv/config server/index.ts", "dev:no-build": "cross-env NODE_ENV=development NODE_TLS_REJECT_UNAUTHORIZED=0 tsx --require dotenv/config server/index.ts", "build": "node scripts/build-production.js", "build:production": "cross-env NODE_ENV=production vite build && node scripts/esbuild.config.js production", "build:development": "cross-env NODE_ENV=development vite build && node scripts/esbuild.config.js development", "build:base": "npm run build:production", "build:dev": "npm run build:development", "build:verify": "node scripts/verify-console-removal.js", "start": "cross-env NODE_ENV=production node --require dotenv/config dist/index.js", "check": "tsc", "db:push": "drizzle-kit push", "db:init": "node --experimental-modules scripts/init-multi-tenant.js", "db:create-schema": "node scripts/create-schema.cjs", "db:create-admin": "node scripts/generate-admin-password.js", "db:create-plans": "node --experimental-modules scripts/create-plans-table.js", "db:migrate": "node scripts/migrate.js run", "db:migrate:status": "node scripts/migrate.js status", "db:migrate:validate": "node scripts/migrate.js validate", "db:migrate:create": "node scripts/migrate.js create", "test:fixes": "node test-all-fixes.js", "test:migration": "node test-migration-system.js", "verify:fixes": "node verify-fixes.js"}, "dependencies": {"@anthropic-ai/sdk": "^0.37.0", "@dagrejs/dagre": "^1.1.5", "@ffmpeg-installer/ffmpeg": "^1.1.0", "@ffprobe-installer/ffprobe": "^2.1.2", "@google/generative-ai": "^0.24.0", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^3.10.0", "@jridgewell/trace-mapping": "^0.3.25", "@neondatabase/serverless": "^0.10.4", "@paypal/checkout-server-sdk": "^1.0.3", "@paypal/paypal-js": "^8.2.0", "@paypal/paypal-server-sdk": "^1.0.0", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@sendgrid/mail": "^8.1.5", "@tailwindcss/vite": "^4.1.3", "@tanstack/react-query": "^5.60.5", "@types/dompurify": "^3.0.5", "@types/fluent-ffmpeg": "^2.1.27", "@types/fs-extra": "^11.0.4", "@types/multer": "^1.4.12", "@types/node-cron": "^3.0.11", "@types/qrcode": "^1.5.5", "@types/tar": "^6.1.13", "baileys": "^6.7.18", "bcrypt": "^5.1.1", "big-integer": "^1.6.52", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "connect-pg-simple": "^10.0.0", "csv-writer": "^1.6.0", "date-and-time": "^4.0.3", "date-fns": "^3.6.0", "dompurify": "^3.2.6", "dotenv": "^16.5.0", "drizzle-orm": "^0.39.1", "drizzle-zod": "^0.7.0", "embla-carousel-react": "^8.6.0", "emoji-picker-react": "^4.12.2", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "fluent-ffmpeg": "^2.1.3", "form-data": "^4.0.4", "framer-motion": "^11.13.1", "fs-extra": "^11.3.0", "google-auth-library": "^9.15.1", "googleapis": "^148.0.0", "grapesjs": "^0.22.12", "helmet": "^8.1.0", "imapflow": "^1.0.189", "input-otp": "^1.4.2", "link-preview-js": "^3.1.0", "lucide-react": "^0.453.0", "mailparser": "^3.7.4", "mammoth": "^1.10.0", "memorystore": "^1.6.7", "mercadopago": "^2.7.0", "multer": "^1.4.5-lts.2", "nanoid": "^5.1.5", "next-themes": "^0.4.6", "node-cron": "^4.0.7", "node-fetch": "^3.3.2", "nodemailer": "^6.10.1", "openai": "^4.96.0", "passport": "^0.7.0", "passport-local": "^1.0.0", "pdf-parse": "^1.1.1", "pdfjs-dist": "^5.4.54", "pg": "^8.14.1", "pino": "^9.6.0", "qrcode": "^1.5.4", "qrcode-terminal": "^0.12.0", "qrcode.react": "^4.2.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "react-hook-form": "^7.55.0", "react-icons": "^5.4.0", "react-resizable-panels": "^2.1.7", "reactflow": "^11.11.4", "recharts": "^2.15.2", "remixicon": "^4.6.0", "sharp": "^0.32.6", "stripe": "^18.1.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tar": "^7.4.3", "telegram": "^2.22.2", "tw-animate-css": "^1.2.5", "use-debounce": "^10.0.5", "vaul": "^1.1.2", "wouter": "^3.3.5", "ws": "^8.18.0", "xlsx": "^0.18.5", "zod": "^3.24.2", "zod-validation-error": "^3.4.0", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.27.0", "@tailwindcss/typography": "^0.5.15", "@types/bcrypt": "^5.0.2", "@types/connect-pg-simple": "^7.0.3", "@types/dagre": "^0.7.53", "@types/express": "4.17.21", "@types/express-session": "^1.18.0", "@types/form-data": "^2.2.1", "@types/mailparser": "^3.4.6", "@types/node": "20.16.11", "@types/nodemailer": "^6.4.17", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/paypal__checkout-server-sdk": "^1.0.8", "@types/pdf-parse": "^1.1.5", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/ws": "^8.5.13", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "drizzle-kit": "^0.30.6", "esbuild": "^0.25.0", "eslint": "^8.57.1", "eslint-plugin-react": "^7.37.5", "globals": "^16.1.0", "javascript-obfuscator": "^4.1.1", "postcss": "^8.4.47", "rimraf": "^5.0.5", "tailwindcss": "^3.4.17", "terser": "^5.42.0", "tsx": "^4.19.1", "typescript": "5.6.3", "typescript-eslint": "^8.32.1", "vite": "^5.4.14", "webpack-obfuscator": "^3.5.1"}, "optionalDependencies": {"bufferutil": "^4.0.8"}}