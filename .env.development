# Development Environment Configuration
NODE_ENV=development

# Server Configuration
PORT=9000
FRONTEND_PORT=3000

# Database Configuration
DATABASE_URL="postgresql://postgres:root@localhost:5432/powerchat"
PGSSLMODE=disable
DB_SSL_MODE=disable

# Session Configuration
SESSION_SECRET=23748523049523798523ijwhw98423io2344u52984uweojq128y423481034hrqw2
FORCE_INSECURE_COOKIE=true

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_USERNAME=<EMAIL>
ADMIN_FULL_NAME=Super Admin

# Security Configuration (Development)
ENCRYPTION_KEY=your-32-byte-hex-encryption-key-change-this-in-production
ENABLE_RUNTIME_PROTECTION=false
ENABLE_CLIENT_PROTECTION=false
ENABLE_CONSOLE_PROTECTION=false
DISABLE_CSP=true

# Development Settings
NODE_TLS_REJECT_UNAUTHORIZED=0

# Logging
LOG_LEVEL=INFO

# API Keys (if needed)
XAI_API_KEY=AIzaSyBpiIaWPOyXM1RWZfihM30dkBXq9FMZuS0
