import { Express, Request, Response, NextFunction } from "express";
import { storage } from "./storage";
import { scrypt, randomBytes, timingSafeEqual } from "crypto";
import { promisify } from "util";
import { z } from "zod";
import multer from "multer";
import path from "path";
import fs from "fs";
import <PERSON><PERSON> from "stripe";
import paypal from "@paypal/checkout-server-sdk";
import { pool } from "./db";
import { ensureSuperAdmin } from "./middleware";
import nodemailer from "nodemailer";
import { createCipheriv, createDecipheriv, randomBytes as cryptoRandomBytes } from "crypto";
import axios from "axios";
import { registerAffiliateRoutes } from "./routes/admin/affiliate-routes";
import adminAiCredentialsRoutes from "./routes/admin-ai-credentials";
import { parseDialog360Error, createErrorResponse } from "./services/channels/360dialog-errors";

interface SMTPConfig {
  enabled: boolean;
  host?: string;
  port?: number;
  security?: 'none' | 'ssl' | 'starttls';
  username?: string;
  password?: string;
  fromName?: string;
  fromEmail?: string;
  testEmail?: string;
}

interface ValidationResult {
  valid: boolean;
  errors: Array<{ field: string; message: string }>;
}

interface EmailResult {
  success: boolean;
  messageId?: string;
  error?: string;
}

interface AuthenticatedUser {
  id: number;
  username: string;
  email: string;
  fullName: string;
  role: string;
  companyId: number | null;
  isSuperAdmin: boolean;
}


const scryptAsync = promisify(scrypt);

async function hashPassword(password: string) {
  const salt = randomBytes(16).toString("hex");
  const buf = (await scryptAsync(password, salt, 64)) as Buffer;
  return `${buf.toString("hex")}.${salt}`;
}

async function comparePasswords(supplied: string, stored: string) {
  const [hashed, salt] = stored.split(".");
  const hashedBuf = Buffer.from(hashed, "hex");
  const suppliedBuf = (await scryptAsync(supplied, salt, 64)) as Buffer;
  return timingSafeEqual(hashedBuf, suppliedBuf);
}

const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'default-key-change-in-production-32-chars';
const ALGORITHM = 'aes-256-cbc';

function getEncryptionKey(): Buffer {
  const key = ENCRYPTION_KEY;

  let finalKey: string;
  if (key.length === 32) {
    finalKey = key;
  } else if (key.length > 32) {
    finalKey = key.substring(0, 32);
  } else {
    finalKey = key.padEnd(32, '0');
  }

  return Buffer.from(finalKey, 'utf8');
}

function encryptPassword(password: string): string {
  const iv = cryptoRandomBytes(16);
  const cipher = createCipheriv(ALGORITHM, getEncryptionKey(), iv);
  let encrypted = cipher.update(password, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return iv.toString('hex') + ':' + encrypted;
}

function decryptPassword(encryptedPassword: string): string {
  const [ivHex, encrypted] = encryptedPassword.split(':');
  const iv = Buffer.from(ivHex, 'hex');
  const decipher = createDecipheriv(ALGORITHM, getEncryptionKey(), iv);
  let decrypted = decipher.update(encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  return decrypted;
}

const smtpConfigSchema = z.object({
  enabled: z.boolean().default(false),
  host: z.string().optional(),
  port: z.number().int().min(1).max(65535).optional(),
  security: z.enum(['none', 'ssl', 'starttls']).optional(),
  username: z.string().optional(),
  password: z.string().optional(),
  fromName: z.string().optional(),
  fromEmail: z.string().optional(),
  testEmail: z.string().optional()
});

function validateEnabledSmtp(config: SMTPConfig): ValidationResult {
  if (!config.enabled) {
    return { valid: true, errors: [] };
  }

  const errors: Array<{ field: string; message: string }> = [];

  if (!config.host || config.host.trim() === '') {
    errors.push({ field: 'host', message: 'SMTP host is required when SMTP is enabled' });
  }

  if (!config.port || config.port < 1 || config.port > 65535) {
    errors.push({ field: 'port', message: 'Valid port number is required when SMTP is enabled' });
  }

  if (!config.security || !['none', 'ssl', 'starttls'].includes(config.security)) {
    errors.push({ field: 'security', message: 'Valid security type is required when SMTP is enabled' });
  }

  if (!config.username || config.username.trim() === '') {
    errors.push({ field: 'username', message: 'Username is required when SMTP is enabled' });
  }

  if (!config.password || (config.password.trim() === '' && config.password !== '••••••••')) {
    errors.push({ field: 'password', message: 'Password is required when SMTP is enabled' });
  }

  if (!config.fromName || config.fromName.trim() === '') {
    errors.push({ field: 'fromName', message: 'From name is required when SMTP is enabled' });
  }

  if (!config.fromEmail || config.fromEmail.trim() === '') {
    errors.push({ field: 'fromEmail', message: 'From email is required when SMTP is enabled' });
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(config.fromEmail)) {
    errors.push({ field: 'fromEmail', message: 'Valid email address is required for from email' });
  }

  return { valid: errors.length === 0, errors };
}

async function createSMTPTransporter(config: SMTPConfig): Promise<nodemailer.Transporter> {
  const transportConfig: any = {
    host: config.host,
    port: config.port,
    auth: {
      user: config.username,
      pass: config.password
    }
  };

  if (config.security === 'ssl') {
    transportConfig.secure = true;
  } else if (config.security === 'starttls') {
    transportConfig.secure = false;
    transportConfig.requireTLS = true;
    transportConfig.tls = {
      rejectUnauthorized: false
    };
  } else {
    transportConfig.secure = false;
    transportConfig.ignoreTLS = true;
  }


  return nodemailer.createTransport(transportConfig);
}

async function testSMTPConnection(config: SMTPConfig, testEmail: string): Promise<EmailResult> {
  try {
    const transporter = await createSMTPTransporter(config);

    await transporter.verify();

    const mailOptions = {
      from: `"${config.fromName}" <${config.fromEmail}>`,
      to: testEmail,
      subject: 'SMTP Configuration Test',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333235;">SMTP Configuration Test</h2>
          <p>This is a test email to verify your SMTP configuration is working correctly.</p>
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3>Configuration Details:</h3>
            <ul>
              <li><strong>SMTP Host:</strong> ${config.host}</li>
              <li><strong>Port:</strong> ${config.port}</li>
              <li><strong>Security:</strong> ${config.security?.toUpperCase() || 'NONE'}</li>
              <li><strong>From:</strong> ${config.fromName} &lt;${config.fromEmail}&gt;</li>
            </ul>
          </div>
          <p>If you received this email, your SMTP configuration is working correctly!</p>
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
          <p style="color: #6b7280; font-size: 14px;">
            This email was sent from Email Configuration Test.<br>
            Sent at: ${new Date().toLocaleString()}
          </p>
        </div>
      `
    };

    const result = await transporter.sendMail(mailOptions);
    return { success: true, messageId: result.messageId };
  } catch (error) {
    console.error('SMTP test connection error:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

async function sendTestEmail(config: SMTPConfig, testEmail: string): Promise<EmailResult> {
  try {
    const transporter = await createSMTPTransporter(config);

    const mailOptions = {
      from: `"${config.fromName}" <${config.fromEmail}>`,
      to: testEmail,
      subject: 'SMTP Configuration Test',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333235;">SMTP Configuration Test</h2>
          <p>This is a test email to verify your SMTP configuration is working correctly.</p>
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3>Configuration Details:</h3>
            <ul>
              <li><strong>SMTP Host:</strong> ${config.host}</li>
              <li><strong>Port:</strong> ${config.port}</li>
              <li><strong>Security:</strong> ${config.security?.toUpperCase() || 'NONE'}</li>
              <li><strong>From:</strong> ${config.fromName} &lt;${config.fromEmail}&gt;</li>
            </ul>
          </div>
          <p>If you received this email, your SMTP configuration is working correctly!</p>
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
          <p style="color: #6b7280; font-size: 14px;">
            This email was sent from  Email Configuration Test.<br>
            Sent at: ${new Date().toLocaleString()}
          </p>
        </div>
      `
    };

    const result = await transporter.sendMail(mailOptions);
    return { success: true, messageId: result.messageId };
  } catch (error) {
    console.error('SMTP test email error:', error);
    throw new Error(`SMTP test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

async function sendEmail(to: string, subject: string, html: string, options: Record<string, any> = {}): Promise<EmailResult> {
  try {
    const smtpSetting = await storage.getAppSetting('smtp_config');
    if (!smtpSetting || !(smtpSetting.value as SMTPConfig).enabled) {
      throw new Error('SMTP is not configured or disabled');
    }

    const config: SMTPConfig = smtpSetting.value as SMTPConfig;
    if (config.password) {
      config.password = decryptPassword(config.password);
    }

    const transporter = await createSMTPTransporter(config);

    const mailOptions = {
      from: `"${config.fromName}" <${config.fromEmail}>`,
      to,
      subject,
      html,
      ...options
    };

    const result = await transporter.sendMail(mailOptions);
    return { success: true, messageId: result.messageId };
  } catch (error) {
    console.error('Email sending error:', error);
    throw error;
  }
}

const ensureAuthenticated = (req: Request, res: Response, next: NextFunction) => {
  if (req.isAuthenticated()) {
    return next();
  }
  res.status(401).json({ message: 'Unauthorized' });
};

const uploadsDir = path.join(process.cwd(), 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

const storage_config = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = path.join(uploadsDir, 'branding');
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + ext);
  }
});

const upload = multer({
  storage: storage_config,
  limits: {
    fileSize: 5 * 1024 * 1024,
    files: 1,
    fields: 5,
    fieldNameSize: 100,
    fieldSize: 1024 * 1024
  },
  fileFilter: (req, file, cb) => {

    const allowedMimeTypes = [
      'image/jpeg', 'image/jpg', 'image/png', 'image/gif',
      'image/svg+xml', 'image/x-icon', 'image/vnd.microsoft.icon'
    ];

    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.svg', '.ico'];

    const ext = path.extname(file.originalname).toLowerCase();
    if (!allowedExtensions.includes(ext)) {
      return cb(new Error(`File type not allowed. Allowed extensions: ${allowedExtensions.join(', ')}`));
    }

    if (!allowedMimeTypes.includes(file.mimetype)) {
      return cb(new Error(`File type not allowed. Allowed types: ${allowedMimeTypes.join(', ')}`));
    }

    const filename = file.originalname.toLowerCase();

    const dangerousPatterns = [
      /\.exe$/i, /\.bat$/i, /\.cmd$/i, /\.com$/i, /\.pif$/i, /\.scr$/i,
      /\.vbs$/i, /\.js$/i, /\.jar$/i, /\.php$/i, /\.asp$/i, /\.jsp$/i,
      /\.sh$/i, /\.ps1$/i, /\.html$/i, /\.htm$/i
    ];

    if (dangerousPatterns.some(pattern => pattern.test(filename))) {
      return cb(new Error('File type not allowed for security reasons'));
    }

    if (file.size && file.size > 5 * 1024 * 1024) {
      return cb(new Error('File size exceeds 5MB limit'));
    }

    cb(null, true);
  }
});

function registerAdminRoutes(app: Express) {
  app.get("/api/admin/users", ensureSuperAdmin, async (req: Request, res: Response) => {
    try {
      const allUsers = await storage.getAllUsers();

      const safeUsers = allUsers.map(user => {
        const { password, ...safeUser } = user;
        return safeUser;
      });

      res.json(safeUsers);
    } catch (error) {
      console.error("Error fetching users:", error);
      res.status(500).json({ error: "Failed to fetch users" });
    }
  });

  app.get("/api/admin/users/:id", ensureSuperAdmin, async (req: Request, res: Response) => {
    try {
      const userId = parseInt(req.params.id);
      const user = await storage.getUser(userId);

      if (!user) {
        return res.status(404).json({ error: "User not found" });
      }

      const { password, ...safeUser } = user;

      res.json(safeUser);
    } catch (error) {
      console.error("Error fetching user:", error);
      res.status(500).json({ error: "Failed to fetch user" });
    }
  });

  app.post("/api/admin/users", ensureSuperAdmin, async (req: Request, res: Response) => {
    try {
      const { username, email, fullName, password, role, companyId, isSuperAdmin } = req.body;

      if (!username || !email || !fullName || !password) {
        return res.status(400).json({ error: "Username, email, full name, and password are required" });
      }

      const existingUser = await storage.getUserByUsernameCaseInsensitive(username);
      if (existingUser) {
        return res.status(400).json({ error: "Username already exists" });
      }

      if (companyId && !isSuperAdmin) {
        const company = await storage.getCompany(companyId);
        if (!company) {
          return res.status(400).json({ error: "Company not found" });
        }
      }

      const hashedPassword = await hashPassword(password);

      const newUser = await storage.createUser({
        username,
        email,
        fullName,
        password: hashedPassword,
        role: role || "agent",
        companyId: isSuperAdmin ? null : companyId,
        isSuperAdmin: !!isSuperAdmin
      });

      const { password: _, ...safeUser } = newUser;

      res.status(201).json(safeUser);
    } catch (error) {
      console.error("Error creating user:", error);
      res.status(500).json({ error: "Failed to create user" });
    }
  });

  app.put("/api/admin/users/:id", ensureSuperAdmin, async (req: Request, res: Response) => {
    try {
      const userId = parseInt(req.params.id);
      const { email, fullName, role, companyId, isSuperAdmin, active } = req.body;

      const existingUser = await storage.getUser(userId);
      if (!existingUser) {
        return res.status(404).json({ error: "User not found" });
      }

      if (companyId && !isSuperAdmin) {
        const company = await storage.getCompany(companyId);
        if (!company) {
          return res.status(400).json({ error: "Company not found" });
        }
      }

      const updatedUser = await storage.updateUser(userId, {
        email,
        fullName,
        role,
        companyId: isSuperAdmin ? null : companyId,
        isSuperAdmin: !!isSuperAdmin,
        active: active !== undefined ? !!active : undefined
      });

      const { password, ...safeUser } = updatedUser;

      res.json(safeUser);
    } catch (error) {
      console.error("Error updating user:", error);
      res.status(500).json({ error: "Failed to update user" });
    }
  });

  app.post("/api/admin/users/:id/change-password", ensureSuperAdmin, async (req: Request, res: Response) => {
    try {
      const userId = parseInt(req.params.id);
      const { newPassword } = req.body;

      if (!newPassword) {
        return res.status(400).json({ error: "New password is required" });
      }

      const existingUser = await storage.getUser(userId);
      if (!existingUser) {
        return res.status(404).json({ error: "User not found" });
      }

      const hashedPassword = await hashPassword(newPassword);

      const success = await storage.updateUserPassword(userId, hashedPassword, true);

      if (!success) {
        return res.status(500).json({ error: "Failed to update password" });
      }

      res.json({ message: "Password updated successfully" });
    } catch (error) {
      console.error("Error changing user password:", error);
      res.status(500).json({ error: "Failed to change password" });
    }
  });

  app.post("/api/admin/users/:id/reset-password", ensureSuperAdmin, async (req: Request, res: Response) => {
    try {
      const userId = parseInt(req.params.id);

      const existingUser = await storage.getUser(userId);
      if (!existingUser) {
        return res.status(404).json({ error: "User not found" });
      }

      const temporaryPassword = randomBytes(4).toString("hex");

      const hashedPassword = await hashPassword(temporaryPassword);

      const success = await storage.updateUserPassword(userId, hashedPassword, true);

      if (!success) {
        return res.status(500).json({ error: "Failed to reset password" });
      }

      res.json({
        message: "Password reset successfully",
        temporaryPassword
      });
    } catch (error) {
      console.error("Error resetting user password:", error);
      res.status(500).json({ error: "Failed to reset password" });
    }
  });

  app.delete("/api/admin/users/:id", ensureSuperAdmin, async (req: Request, res: Response) => {
    try {
      const userId = parseInt(req.params.id);

      const existingUser = await storage.getUser(userId);
      if (!existingUser) {
        return res.status(404).json({ error: "User not found" });
      }

      if (userId === (req.user as any)?.id) {
        return res.status(400).json({ error: "Cannot delete your own account" });
      }

      const success = await storage.deleteUser(userId);

      if (!success) {
        return res.status(500).json({ error: "Failed to delete user" });
      }

      res.json({ message: "User deleted successfully" });
    } catch (error) {
      console.error("Error deleting user:", error);
      res.status(500).json({ error: "Failed to delete user" });
    }
  });


  const ensureSettingsAccess = (req: Request, res: Response, next: NextFunction) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const user = req.user;
    const session = req.session as any;

    if (user?.isSuperAdmin) {
      return next();
    }

    if (session?.impersonation?.originalUserId) {
      (req as any).isImpersonating = true;
      (req as any).originalUserId = session.impersonation.originalUserId;
      return next();
    }

    res.status(403).json({ message: 'Settings access requires super admin privileges' });
  };

  app.get("/api/admin/settings", ensureSettingsAccess, async (_req, res) => {
    try {
      const settings = await storage.getAllAppSettings();
      res.json(settings);
    } catch (error) {
      console.error("Error fetching settings:", error);
      res.status(500).json({ error: "Failed to fetch settings" });
    }
  });

  app.get("/api/admin/settings/:key", ensureSettingsAccess, async (req: Request, res: Response) => {
    try {
      const key = req.params.key;
      const setting = await storage.getAppSetting(key);

      if (!setting) {
        return res.status(404).json({ error: "Setting not found" });
      }

      res.json(setting);
    } catch (error) {
      console.error("Error fetching setting:", error);
      res.status(500).json({ error: "Failed to fetch setting" });
    }
  });


  app.post("/api/admin/settings/branding/logo", ensureSettingsAccess, upload.single('logo'), async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ error: "No file uploaded" });
      }

      const logoUrl = `/uploads/branding/${req.file.filename}`;

      await storage.saveAppSetting('branding_logo', logoUrl);

      try {
        if ((global as any).broadcastToAllClients) {
          (global as any).broadcastToAllClients({
            type: 'settingsUpdated',
            key: 'branding_logo',
            value: logoUrl
          });
        }
      } catch (error) {
        console.error('Error broadcasting logo update:', error);
      }

      res.json({
        message: "Logo uploaded successfully",
        logoUrl
      });
    } catch (error) {
      console.error("Error uploading logo:", error);
      res.status(500).json({ error: "Failed to upload logo" });
    }
  });

  app.post("/api/admin/settings/branding/favicon", ensureSettingsAccess, upload.single('favicon'), async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ error: "No file uploaded" });
      }

      const faviconUrl = `/uploads/branding/${req.file.filename}`;

      await storage.saveAppSetting('branding_favicon', faviconUrl);

      try {
        if ((global as any).broadcastToAllClients) {
          (global as any).broadcastToAllClients({
            type: 'settingsUpdated',
            key: 'branding_favicon',
            value: faviconUrl
          });
        }
      } catch (error) {
        console.error('Error broadcasting favicon update:', error);
      }

      res.json({
        message: "Favicon uploaded successfully",
        faviconUrl
      });
    } catch (error) {
      console.error("Error uploading favicon:", error);
      res.status(500).json({ error: "Failed to upload favicon" });
    }
  });


  app.post("/api/admin/companies/:id/logo", ensureSuperAdmin, upload.single('logo'), async (req, res) => {
    try {
      const companyId = parseInt(req.params.id);
      if (isNaN(companyId)) {
        return res.status(400).json({ error: "Invalid company ID" });
      }

      if (!req.file) {
        return res.status(400).json({ error: "No file uploaded" });
      }


      const existingCompany = await storage.getCompany(companyId);
      if (!existingCompany) {
        return res.status(404).json({ error: "Company not found" });
      }

      const logoUrl = `/uploads/branding/${req.file.filename}`;


      const updatedCompany = await storage.updateCompany(companyId, {
        logo: logoUrl
      });

      res.json({
        message: "Company logo uploaded successfully",
        logoUrl,
        company: updatedCompany
      });
    } catch (error) {
      console.error("Error uploading company logo:", error);
      res.status(500).json({ error: "Failed to upload company logo" });
    }
  });

  app.post("/api/admin/settings/branding", ensureSettingsAccess, async (req, res) => {
    try {
      const { appName, primaryColor, secondaryColor } = req.body;

      if (!appName) {
        return res.status(400).json({ error: "Application name is required" });
      }

      const brandingSettings = {
        appName: appName,
        primaryColor: primaryColor || '#333235',
        secondaryColor: secondaryColor || '#4F46E5'
      };

      await storage.saveAppSetting('branding', brandingSettings);

      try {
        if ((global as any).broadcastToAllClients) {

          (global as any).broadcastToAllClients({
            type: 'settingsUpdated',
            key: 'branding',
            value: brandingSettings
          });
        } else {
          console.error('🎨 broadcastToAllClients not available');
        }
      } catch (error) {
        console.error('Error broadcasting settings update:', error);
      }

      res.json({
        message: "Branding settings saved successfully",
        settings: brandingSettings
      });
    } catch (error) {
      console.error("Error saving branding settings:", error);
      if (error instanceof Error) {
        res.status(500).json({ error: error.message || "Failed to save branding settings" });
      } else {
        res.status(500).json({ error: "Failed to save branding settings" });
      }
    }
  });

  app.post("/api/admin/settings/registration", ensureSuperAdmin, async (req, res) => {
    try {
      const { enabled, requireApproval, defaultPlan } = req.body;

      if (defaultPlan && defaultPlan !== 'free') {
        const planId = parseInt(defaultPlan);
        if (!isNaN(planId)) {
          const plan = await storage.getPlan(planId);
          if (!plan) {
            return res.status(400).json({ error: `Plan with ID ${planId} not found` });
          }
          if (!plan.isActive) {
            return res.status(400).json({ error: `Plan with ID ${planId} is not active` });
          }
        }
      }

      const registrationSettings = {
        enabled: Boolean(enabled),
        requireApproval: Boolean(requireApproval),
        defaultPlan: defaultPlan || '1'
      };

      await storage.saveAppSetting('registration_settings', registrationSettings);

      res.json({
        message: "Registration settings saved successfully",
        settings: registrationSettings
      });
    } catch (error) {
      console.error("❌ Error saving registration settings:", error);
      res.status(500).json({ error: "Failed to save registration settings" });
    }
  });

  app.post("/api/admin/settings/general", ensureSuperAdmin, async (req, res) => {
    try {
      const { defaultCurrency, dateFormat, timeFormat, subdomainAuthentication, frontendWebsiteEnabled } = req.body;

      const generalSettings = {
        defaultCurrency: defaultCurrency || 'USD',
        dateFormat: dateFormat || 'MM/DD/YYYY',
        timeFormat: timeFormat || '12h',
        subdomainAuthentication: Boolean(subdomainAuthentication),
        frontendWebsiteEnabled: frontendWebsiteEnabled !== undefined ? Boolean(frontendWebsiteEnabled) : false
      };

      await storage.saveAppSetting('general_settings', generalSettings);


      await storage.saveAppSetting('subdomain_authentication', Boolean(subdomainAuthentication));

      res.json({
        message: "General settings saved successfully",
        settings: generalSettings
      });
    } catch (error) {
      console.error("❌ Error saving general settings:", error);
      res.status(500).json({ error: "Failed to save general settings" });
    }
  });


  app.get("/api/admin/settings/custom-scripts", ensureSettingsAccess, async (_req, res) => {
    try {
      const customScriptsSetting = await storage.getAppSetting('custom_scripts');

      if (!customScriptsSetting) {
        const defaultConfig = {
          enabled: false,
          scripts: '',
          lastModified: new Date().toISOString()
        };
        return res.json(defaultConfig);
      }

      res.json(customScriptsSetting.value);
    } catch (error) {
      console.error("Error fetching custom scripts settings:", error);
      res.status(500).json({ error: "Failed to fetch custom scripts settings" });
    }
  });

  app.post("/api/admin/settings/custom-scripts", ensureSuperAdmin, async (req, res) => {
    try {
      const { enabled, scripts } = req.body;


      if (typeof enabled !== 'boolean') {
        return res.status(400).json({ error: "Enabled must be a boolean value" });
      }

      if (typeof scripts !== 'string') {
        return res.status(400).json({ error: "Scripts must be a string" });
      }






      const allowedDomains = [
        'cdn.jsdelivr.net',
        'cdnjs.cloudflare.com',
        'unpkg.com',
        'code.jquery.com',
        'stackpath.bootstrapcdn.com',
        'maxcdn.bootstrapcdn.com',
        'ajax.googleapis.com',
        'fonts.googleapis.com',
        'fonts.gstatic.com',
        'www.googletagmanager.com',
        'www.google-analytics.com',
        'connect.facebook.net',
        'platform.twitter.com',
        'www.youtube.com',
        'player.vimeo.com',
        'cdn.weglot.com',
        'widget.intercom.io',
        'js.stripe.com',
        'checkout.stripe.com',
        'js.paypal.com',
        'www.paypal.com',
        'sdk.mercadopago.com',
        'secure.mlstatic.com',
        'embed.tawk.to',
        'tawk.to'
      ];


      const srcMatches = scripts.match(/src\s*=\s*["']([^"']+)["']/gi);
      if (srcMatches) {
        for (const match of srcMatches) {
          const urlMatch = match.match(/src\s*=\s*["']([^"']+)["']/i);
          if (urlMatch) {
            const url = urlMatch[1];
            try {
              const urlObj = new URL(url);
              const domain = urlObj.hostname;

              if (!allowedDomains.some(allowedDomain =>
                domain === allowedDomain || domain.endsWith('.' + allowedDomain)
              )) {
                return res.status(400).json({
                  error: `Domain '${domain}' is not in the allowed list for security reasons`,
                  details: `Please contact your administrator to whitelist this domain if it's trusted`
                });
              }
            } catch (e) {


            }
          }
        }
      }

      const customScriptsSettings = {
        enabled: Boolean(enabled),
        scripts: scripts || '',
        lastModified: new Date().toISOString()
      };

      await storage.saveAppSetting('custom_scripts', customScriptsSettings);


      try {
        if ((global as any).broadcastToAllClients) {
          (global as any).broadcastToAllClients({
            type: 'customScriptsUpdated',
            data: customScriptsSettings
          });
        }
      } catch (error) {
        console.error('Error broadcasting custom scripts update:', error);
      }

      res.json({
        message: "Custom scripts settings saved successfully",
        settings: customScriptsSettings
      });
    } catch (error) {
      console.error("Error saving custom scripts settings:", error);
      res.status(500).json({ error: "Failed to save custom scripts settings" });
    }
  });


  app.get("/api/admin/settings/smtp", ensureSettingsAccess, async (_req, res) => {
    try {
      const smtpSetting = await storage.getAppSetting('smtp_config');

      if (!smtpSetting) {
        const defaultConfig = {
          enabled: false,
          host: '',
          port: 465,
          security: 'starttls',
          username: '',
          password: '',
          fromName: '',
          fromEmail: '',
          testEmail: ''
        };
        return res.json(defaultConfig);
      }

      const config = { ...(smtpSetting.value as any) };
      if (config.password) {
        config.password = '••••••••';
      }

      res.json(config);
    } catch (error) {
      console.error("Error fetching SMTP settings:", error);
      res.status(500).json({ error: "Failed to fetch SMTP settings" });
    }
  });

  app.post("/api/admin/settings/smtp", ensureSettingsAccess, async (req, res) => {
    try {


      const validation = smtpConfigSchema.safeParse(req.body);
      if (!validation.success) {
        return res.status(400).json({
          error: "Invalid SMTP configuration format",
          details: validation.error.errors
        });
      }

      const config = validation.data;

      if (config.password === '••••••••') {
        const existingSetting = await storage.getAppSetting('smtp_config');
        if (existingSetting && (existingSetting.value as any).password) {
          config.password = (existingSetting.value as any).password;
        } else if (config.enabled) {
          return res.status(400).json({ error: "Password is required when SMTP is enabled" });
        }
      } else if (config.password && config.password.trim() !== '') {
        config.password = encryptPassword(config.password);
      }

      if (config.enabled) {
        const enabledValidation = validateEnabledSmtp(config);
        if (!enabledValidation.valid) {
          return res.status(400).json({
            error: "Invalid SMTP configuration",
            details: enabledValidation.errors
          });
        }
      }

      await storage.saveAppSetting('smtp_config', config);

      const responseConfig = { ...config };
      responseConfig.password = '••••••••';

      res.json({
        message: "SMTP settings saved successfully",
        config: responseConfig
      });
    } catch (error) {
      console.error("Error saving SMTP settings:", error);
      res.status(500).json({ error: "Failed to save SMTP settings" });
    }
  });

  app.post("/api/admin/settings/smtp/test", ensureSettingsAccess, async (req, res) => {
    try {

      const { testEmail } = req.body;

      if (!testEmail) {
        return res.status(400).json({ error: "Test email address is required" });
      }

      const smtpSetting = await storage.getAppSetting('smtp_config');
      if (!smtpSetting || !(smtpSetting.value as any).enabled) {
        return res.status(400).json({ error: "SMTP is not configured or disabled" });
      }

      const config = { ...(smtpSetting.value as any) };

      if (config.password && config.password !== '••••••••') {
        config.password = decryptPassword(config.password);
      } else {
        return res.status(400).json({ error: "SMTP password is not configured" });
      }

      const testResult = await testSMTPConnection(config, testEmail);

      if (testResult.success) {
        res.json({
          message: "Test email sent successfully",
          testEmail: testEmail
        });
      } else {
        res.status(400).json({
          error: "SMTP test failed",
          details: testResult.error
        });
      }
    } catch (error) {
      console.error("Error testing SMTP connection:", error);
      res.status(500).json({ error: "Failed to test SMTP connection" });
    }
  });

  app.post("/api/admin/settings/:key", ensureSuperAdmin, async (req, res) => {
    try {
      const key = req.params.key;
      const { value } = req.body;

      if (value === undefined) {
        return res.status(400).json({ error: "Value is required" });
      }

      const setting = await storage.saveAppSetting(key, value);

      try {
        if ((global as any).broadcastToAllClients) {
          (global as any).broadcastToAllClients({
            type: 'settingsUpdated',
            key,
            value
          });
        }
      } catch (error) {
        console.error('Error broadcasting settings update:', error);
      }

      res.json(setting);
    } catch (error) {
      console.error("Error saving setting:", error);
      res.status(500).json({ error: "Failed to save setting" });
    }
  });

  app.delete("/api/admin/settings/:key", ensureSuperAdmin, async (req, res) => {
    try {
      const key = req.params.key;
      const success = await storage.deleteAppSetting(key);

      if (!success) {
        return res.status(500).json({ error: "Failed to delete setting" });
      }

      res.json({ message: "Setting deleted successfully" });
    } catch (error) {
      console.error("Error deleting setting:", error);
      res.status(500).json({ error: "Failed to delete setting" });
    }
  });


  app.post("/api/admin/settings/payment/stripe", ensureSuperAdmin, async (req, res) => {
    try {
      const { publishableKey, secretKey, webhookSecret, testMode } = req.body;

      if (!publishableKey || !secretKey) {
        return res.status(400).json({ error: "Publishable key and secret key are required" });
      }

      const stripeSettings = {
        publishableKey,
        secretKey,
        webhookSecret: webhookSecret || '',
        testMode: !!testMode,
        enabled: true
      };

      await storage.saveAppSetting('payment_stripe', stripeSettings);

      res.json({
        message: "Stripe settings saved successfully",
        settings: {
          ...stripeSettings,
          secretKey: '••••••••'
        }
      });
    } catch (error) {
      console.error("Error saving Stripe settings:", error);
      res.status(500).json({ error: "Failed to save Stripe settings" });
    }
  });

  app.post("/api/admin/settings/payment/stripe/test", ensureSuperAdmin, async (_req, res) => {
    try {
      const stripeSettingObj = await storage.getAppSetting('payment_stripe');

      if (!stripeSettingObj || !stripeSettingObj.value) {
        return res.status(400).json({ error: "Stripe is not configured" });
      }

      const stripeSettings = stripeSettingObj.value as any;

      const stripe = new Stripe(stripeSettings.secretKey, {
        apiVersion: '2025-04-30.basil'
      });

      const account = await stripe.accounts.retrieve();

      res.json({
        message: "Stripe connection successful",
        account: {
          id: account.id,
          email: account.email,
          country: account.country,
          detailsSubmitted: account.details_submitted
        }
      });
    } catch (error) {
      console.error("Error testing Stripe connection:", error);
      res.status(500).json({
        error: "Failed to connect to Stripe",
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  app.post("/api/admin/settings/payment/mercadopago", ensureSuperAdmin, async (req, res) => {
    try {
      const { clientId, clientSecret, accessToken, testMode } = req.body;

      if (!clientId || !clientSecret || !accessToken) {
        return res.status(400).json({ error: "Client ID, Client Secret, and Access Token are required" });
      }

      const mercadoPagoSettings = {
        clientId,
        clientSecret,
        accessToken,
        testMode: !!testMode,
        enabled: true
      };

      await storage.saveAppSetting('payment_mercadopago', mercadoPagoSettings);

      res.json({
        message: "Mercado Pago settings saved successfully",
        settings: {
          ...mercadoPagoSettings,
          clientSecret: '••••••••',
          accessToken: '••••••••'
        }
      });
    } catch (error) {
      console.error("Error saving Mercado Pago settings:", error);
      res.status(500).json({ error: "Failed to save Mercado Pago settings" });
    }
  });

  app.post("/api/admin/settings/payment/mercadopago/test", ensureSuperAdmin, async (_req, res) => {
    try {
      const mercadoPagoSettingObj = await storage.getAppSetting('payment_mercadopago');

      if (!mercadoPagoSettingObj || !mercadoPagoSettingObj.value) {
        return res.status(400).json({ error: "Mercado Pago is not configured" });
      }

      const mercadoPagoSettings = mercadoPagoSettingObj.value as any;

      if (!mercadoPagoSettings.clientId || !mercadoPagoSettings.clientSecret || !mercadoPagoSettings.accessToken) {
        return res.status(400).json({ error: "Mercado Pago settings are incomplete" });
      }

      if (!mercadoPagoSettings.accessToken) {
        return res.status(400).json({ error: "Mercado Pago access token is required" });
      }

      let paymentMethods;

      try {
        const response = await fetch('https://api.mercadopago.com/v1/payment_methods', {
          headers: {
            'Authorization': `Bearer ${mercadoPagoSettings.accessToken}`
          }
        });


        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          console.error('Mercado Pago API test error response:', errorData);
          throw new Error(`Failed to connect to Mercado Pago API: ${response.status} ${response.statusText}`);
        }

        paymentMethods = await response.json();
      } catch (error) {
        console.error('Error in Mercado Pago API test call:', error);
        throw error;
      }

      if (!paymentMethods || paymentMethods.length === 0) {
        throw new Error('No payment methods returned from Mercado Pago API');
      }

      let userData;

      try {

        const userResponse = await fetch('https://api.mercadopago.com/users/me', {
          headers: {
            'Authorization': `Bearer ${mercadoPagoSettings.accessToken}`
          }
        });


        if (!userResponse.ok) {
          const errorData = await userResponse.json().catch(() => ({}));
          console.error('Mercado Pago user API error response:', errorData);
          throw new Error(`Failed to fetch user information from Mercado Pago API: ${userResponse.status} ${userResponse.statusText}`);
        }

        userData = await userResponse.json();
      } catch (error) {
        console.error('Error fetching Mercado Pago user information:', error);
        throw error;
      }

      if (userData.id.toString() !== mercadoPagoSettings.clientId) {
        
      }

      res.json({
        message: "Mercado Pago connection successful",
        account: {
          id: userData.id,
          email: userData.email,
          nickname: userData.nickname,
          country: userData.country_id
        }
      });
    } catch (error) {
      console.error("Error testing Mercado Pago connection:", error);
      res.status(500).json({
        error: "Failed to connect to Mercado Pago",
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  app.post("/api/admin/settings/payment/moyasar", ensureSuperAdmin, async (req, res) => {
    try {
      const { publishableKey, secretKey, testMode } = req.body;

      if (!publishableKey || !secretKey) {
        return res.status(400).json({ error: "Publishable key and secret key are required" });
      }

      const moyasarSettings = {
        publishableKey,
        secretKey,
        testMode: !!testMode,
        enabled: true
      };

      await storage.saveAppSetting('payment_moyasar', moyasarSettings);

      res.json({
        message: "Moyasar settings saved successfully",
        settings: {
          ...moyasarSettings,
          secretKey: '••••••••'
        }
      });
    } catch (error) {
      console.error("Error saving Moyasar settings:", error);
      res.status(500).json({ error: "Failed to save Moyasar settings" });
    }
  });

  app.post("/api/admin/settings/payment/moyasar/test", ensureSuperAdmin, async (_req, res) => {
    try {
      const moyasarSettingObj = await storage.getAppSetting('payment_moyasar');

      if (!moyasarSettingObj || !moyasarSettingObj.value) {
        return res.status(400).json({ error: "Moyasar is not configured" });
      }

      const moyasarSettings = moyasarSettingObj.value as any;

      if (!moyasarSettings.publishableKey || !moyasarSettings.secretKey) {
        return res.status(400).json({ error: "Moyasar settings are incomplete" });
      }


      try {

        const response = await fetch('https://api.moyasar.com/v1/payments', {
          method: 'GET',
          headers: {
            'Authorization': `Basic ${Buffer.from(moyasarSettings.secretKey + ':').toString('base64')}`
          }
        });

        if (response.status === 401) {
          throw new Error('Invalid API credentials. Please check your secret key.');
        } else if (response.status === 403) {
          throw new Error('API key does not have sufficient permissions.');
        } else if (response.status === 405) {


          if (!moyasarSettings.secretKey.startsWith('sk_')) {
            throw new Error('Invalid secret key format. Secret key should start with "sk_".');
          }


          res.json({
            success: true,
            message: "Moyasar API key format is valid",
            testMode: moyasarSettings.testMode,
            note: "Full API test not available - key format validated"
          });
          return;
        } else if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          console.error('Moyasar API test error response:', errorData);
          throw new Error(`API test failed: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        res.json({
          success: true,
          message: "Moyasar API connection test successful",
          testMode: moyasarSettings.testMode,
          apiResponse: {
            status: response.status,
            hasData: !!data
          }
        });
      } catch (error) {
        console.error('Error in Moyasar API test call:', error);
        throw error;
      }
    } catch (error) {
      console.error("Error testing Moyasar connection:", error);
      res.status(500).json({
        error: "Failed to test Moyasar connection",
        details: error instanceof Error ? error.message : String(error)
      });
    }
  });

  app.post("/api/admin/settings/payment/paypal", ensureSuperAdmin, async (req, res) => {
    try {
      const { clientId, clientSecret, testMode } = req.body;

      if (!clientId || !clientSecret) {
        return res.status(400).json({ error: "Client ID and Client Secret are required" });
      }

      const paypalSettings = {
        clientId,
        clientSecret,
        testMode: !!testMode,
        enabled: true
      };

      await storage.saveAppSetting('payment_paypal', paypalSettings);

      res.json({
        message: "PayPal settings saved successfully",
        settings: {
          ...paypalSettings,
          clientSecret: '••••••••'
        }
      });
    } catch (error) {
      console.error("Error saving PayPal settings:", error);
      res.status(500).json({ error: "Failed to save PayPal settings" });
    }
  });

  app.post("/api/admin/settings/payment/paypal/test", ensureSuperAdmin, async (_req, res) => {
    try {
      const paypalSettingObj = await storage.getAppSetting('payment_paypal');

      if (!paypalSettingObj || !paypalSettingObj.value) {
        return res.status(400).json({ error: "PayPal is not configured" });
      }

      const paypalSettings = paypalSettingObj.value as any;

      let environment;
      if (paypalSettings.testMode) {
        environment = new paypal.core.SandboxEnvironment(
          paypalSettings.clientId,
          paypalSettings.clientSecret
        );
      } else {
        environment = new paypal.core.LiveEnvironment(
          paypalSettings.clientId,
          paypalSettings.clientSecret
        );
      }

      const client = new paypal.core.PayPalHttpClient(environment);

      const request = new paypal.orders.OrdersCreateRequest();
      request.requestBody({
        intent: 'CAPTURE',
        purchase_units: [{
          amount: {
            currency_code: 'USD',
            value: '0.01'
          }
        }]
      });

      const response = await client.execute(request);

      if (response.statusCode !== 201) {
        throw new Error('Failed to connect to PayPal API');
      }


      res.json({
        message: "PayPal connection successful",
        account: {
          environment: paypalSettings.testMode ? 'sandbox' : 'live',
          status: 'connected'
        }
      });
    } catch (error) {
      console.error("Error testing PayPal connection:", error);
      res.status(500).json({
        error: "Failed to connect to PayPal",
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  app.post("/api/admin/settings/payment/bank-transfer", ensureSuperAdmin, async (req, res) => {
    try {
      const {
        accountName,
        accountNumber,
        bankName,
        routingNumber,
        swiftCode,
        instructions,
        enabled
      } = req.body;

      if (!accountName || !accountNumber || !bankName) {
        return res.status(400).json({ error: "Account name, account number, and bank name are required" });
      }

      const bankSettings = {
        accountName,
        accountNumber,
        bankName,
        routingNumber: routingNumber || '',
        swiftCode: swiftCode || '',
        instructions: instructions || '',
        enabled: enabled !== false
      };

      await storage.saveAppSetting('payment_bank_transfer', bankSettings);

      res.json({
        message: "Bank transfer settings saved successfully",
        settings: bankSettings
      });
    } catch (error) {
      console.error("Error saving bank transfer settings:", error);
      res.status(500).json({ error: "Failed to save bank transfer settings" });
    }
  });
















  app.get("/api/admin/settings/integrations/google-calendar", ensureSuperAdmin, async (req, res) => {
    try {
      const googleCalendarSetting = await storage.getAppSetting('google_calendar_oauth');


      const protocol = req.get('x-forwarded-proto') || req.protocol || 'http';
      const host = req.get('host') || 'localhost:9000';
      const origin = req.get('origin') || `${protocol}://${host}`;
      const baseUrl = process.env.BASE_URL || origin;

      if (!googleCalendarSetting) {
        const defaultConfig = {
          enabled: false,
          client_id: '',
          client_secret: '',
          redirect_uri: `${baseUrl}/api/google/calendar/callback`
        };
        return res.json(defaultConfig);
      }

      const config = { ...(googleCalendarSetting.value as any) };
      if (config.client_secret) {
        config.client_secret = '••••••••';
      }

      res.json(config);
    } catch (error) {
      console.error("Error fetching Google Calendar OAuth settings:", error);
      res.status(500).json({ error: "Failed to fetch Google Calendar OAuth settings" });
    }
  });


  app.post("/api/admin/settings/integrations/google-calendar", ensureSuperAdmin, async (req, res) => {
    try {
      const { enabled, client_id, client_secret, redirect_uri } = req.body;

      if (enabled && (!client_id || !client_secret)) {
        return res.status(400).json({ error: "Client ID and Client Secret are required when enabling Google Calendar integration" });
      }


      const protocol = req.get('x-forwarded-proto') || req.protocol || 'http';
      const host = req.get('host') || 'localhost:9000';
      const origin = req.get('origin') || `${protocol}://${host}`;
      const baseUrl = process.env.BASE_URL || origin;

      const googleCalendarSettings = {
        enabled: !!enabled,
        client_id: client_id || '',
        client_secret: client_secret || '',
        redirect_uri: redirect_uri || `${baseUrl}/api/google/calendar/callback`
      };

      await storage.saveAppSetting('google_calendar_oauth', googleCalendarSettings);

      res.json({
        message: "Google Calendar OAuth settings saved successfully",
        settings: {
          ...googleCalendarSettings,
          client_secret: googleCalendarSettings.client_secret ? '••••••••' : ''
        }
      });
    } catch (error) {
      console.error("Error saving Google Calendar OAuth settings:", error);
      res.status(500).json({ error: "Failed to save Google Calendar OAuth settings" });
    }
  });



  app.get("/api/admin/settings/integrations/zoho-calendar", ensureSuperAdmin, async (req, res) => {
    try {
      const zohoCalendarSetting = await storage.getAppSetting('zoho_calendar_oauth');


      const protocol = req.get('x-forwarded-proto') || req.protocol || 'http';
      const host = req.get('host') || 'localhost:9000';
      const origin = req.get('origin') || `${protocol}://${host}`;
      const baseUrl = process.env.BASE_URL || origin;

      if (!zohoCalendarSetting) {
        const defaultConfig = {
          enabled: false,
          client_id: '',
          client_secret: '',
          redirect_uri: `${baseUrl}/api/zoho/calendar/callback`
        };
        return res.json(defaultConfig);
      }

      const config = { ...(zohoCalendarSetting.value as any) };
      if (config.client_secret) {
        config.client_secret = '••••••••';
      }

      res.json(config);
    } catch (error) {
      console.error("Error fetching Zoho Calendar OAuth settings:", error);
      res.status(500).json({ error: "Failed to fetch Zoho Calendar OAuth settings" });
    }
  });

  app.post("/api/admin/settings/integrations/zoho-calendar", ensureSuperAdmin, async (req, res) => {
    try {
      const { enabled, client_id, client_secret, redirect_uri } = req.body;

      if (enabled && (!client_id || !client_secret)) {
        return res.status(400).json({ error: "Client ID and Client Secret are required when enabling Zoho Calendar integration" });
      }


      const protocol = req.get('x-forwarded-proto') || req.protocol || 'http';
      const host = req.get('host') || 'localhost:9000';
      const origin = req.get('origin') || `${protocol}://${host}`;
      const baseUrl = process.env.BASE_URL || origin;

      const zohoCalendarSettings = {
        enabled: !!enabled,
        client_id: client_id || '',
        client_secret: client_secret || '',
        redirect_uri: redirect_uri || `${baseUrl}/api/zoho/calendar/callback`
      };

      await storage.saveAppSetting('zoho_calendar_oauth', zohoCalendarSettings);


      console.log('Zoho Calendar OAuth settings saved:', {
        enabled: zohoCalendarSettings.enabled,
        client_id: zohoCalendarSettings.client_id,
        redirect_uri: zohoCalendarSettings.redirect_uri,
        has_client_secret: !!zohoCalendarSettings.client_secret
      });

      res.json({
        message: "Zoho Calendar OAuth settings saved successfully",
        settings: {
          ...zohoCalendarSettings,
          client_secret: zohoCalendarSettings.client_secret ? '••••••••' : ''
        },
        debug_info: {
          global_data_center_support: true,
          accounts_endpoint: 'https://accounts.zoho.com',
          calendar_api_endpoint: 'https://calendar.zoho.com/api/v1',
          required_scopes: ['ZohoCalendar.event.ALL', 'ZohoCalendar.calendar.READ']
        }
      });
    } catch (error) {
      console.error("Error saving Zoho Calendar OAuth settings:", error);
      res.status(500).json({ error: "Failed to save Zoho Calendar OAuth settings" });
    }
  });


  app.get("/api/admin/settings/integrations/zoho-calendar/test", ensureSuperAdmin, async (req, res) => {
    try {
      const zohoCalendarSetting = await storage.getAppSetting('zoho_calendar_oauth');

      if (!zohoCalendarSetting || !zohoCalendarSetting.value) {
        return res.json({
          success: false,
          error: "Zoho Calendar OAuth not configured",
          recommendations: [
            "Configure Zoho Calendar OAuth settings first",
            "Ensure you have a valid Zoho Developer Console application"
          ]
        });
      }

      const config = zohoCalendarSetting.value as any;

      const validationResults = {
        success: true,
        configuration: {
          enabled: config.enabled,
          has_client_id: !!config.client_id,
          has_client_secret: !!config.client_secret,
          redirect_uri: config.redirect_uri,
          client_id_format: config.client_id ? (config.client_id.startsWith('1000.') ? 'Valid' : 'Invalid format') : 'Missing'
        },
        endpoints: {
          auth_url: 'https://accounts.zoho.com/oauth/v2/auth',
          token_url: 'https://accounts.zoho.com/oauth/v2/token',
          calendar_api: 'https://calendar.zoho.com/api/v1'
        },
        required_scopes: ['ZohoCalendar.event.ALL', 'ZohoCalendar.calendar.READ'],
        recommendations: [] as string[]
      };


      if (!config.enabled) {
        validationResults.recommendations.push("Enable Zoho Calendar integration");
      }
      if (!config.client_id) {
        validationResults.recommendations.push("Add Client ID from Zoho Developer Console");
      }
      if (!config.client_secret) {
        validationResults.recommendations.push("Add Client Secret from Zoho Developer Console");
      }
      if (config.client_id && !config.client_id.startsWith('1000.')) {
        validationResults.recommendations.push("Client ID should start with '1000.' - verify it's from Zoho Developer Console");
      }

      res.json(validationResults);
    } catch (error) {
      console.error("Error testing Zoho Calendar configuration:", error);
      res.status(500).json({
        success: false,
        error: "Failed to test Zoho Calendar configuration"
      });
    }
  });



  app.get("/api/admin/settings/integrations/calendly", ensureSuperAdmin, async (req, res) => {
    try {
      const calendlySetting = await storage.getAppSetting('calendly_oauth');


      const protocol = req.get('x-forwarded-proto') || req.protocol || 'http';
      const host = req.get('host') || 'localhost:9000';
      const origin = req.get('origin') || `${protocol}://${host}`;
      const baseUrl = process.env.BASE_URL || origin;

      if (!calendlySetting) {
        const defaultConfig = {
          enabled: false,
          client_id: '',
          client_secret: '',
          webhook_signing_key: '',
          redirect_uri: `${baseUrl}/api/calendly/callback`
        };
        return res.json(defaultConfig);
      }

      const config = { ...(calendlySetting.value as any) };
      if (config.client_secret) {
        config.client_secret = '••••••••';
      }
      if (config.webhook_signing_key) {
        config.webhook_signing_key = '••••••••';
      }

      res.json(config);
    } catch (error) {
      console.error("Error fetching Calendly OAuth settings:", error);
      res.status(500).json({ error: "Failed to fetch Calendly OAuth settings" });
    }
  });

  app.post("/api/admin/settings/integrations/calendly", ensureSuperAdmin, async (req, res) => {
    try {
      const { enabled, client_id, client_secret, webhook_signing_key, redirect_uri } = req.body;

      if (enabled && (!client_id || !client_secret)) {
        return res.status(400).json({ error: "Client ID and Client Secret are required when enabling Calendly integration" });
      }


      const protocol = req.get('x-forwarded-proto') || req.protocol || 'http';
      const host = req.get('host') || 'localhost:9000';
      const origin = req.get('origin') || `${protocol}://${host}`;
      const baseUrl = process.env.BASE_URL || origin;

      const calendlySettings = {
        enabled: !!enabled,
        client_id: client_id || '',
        client_secret: client_secret || '',
        webhook_signing_key: webhook_signing_key || '',
        redirect_uri: redirect_uri || `${baseUrl}/api/calendly/callback`
      };

      await storage.saveAppSetting('calendly_oauth', calendlySettings);

      res.json({
        message: "Calendly OAuth settings saved successfully",
        settings: {
          ...calendlySettings,
          client_secret: calendlySettings.client_secret ? '••••••••' : '',
          webhook_signing_key: calendlySettings.webhook_signing_key ? '••••••••' : ''
        }
      });
    } catch (error) {
      console.error("Error saving Calendly OAuth settings:", error);
      res.status(500).json({ error: "Failed to save Calendly OAuth settings" });
    }
  });

  app.get("/api/admin/settings/integrations/google-sheets", ensureSuperAdmin, async (req, res) => {
    try {
      const googleSheetsSetting = await storage.getAppSetting('google_sheets_oauth');


      const protocol = req.get('x-forwarded-proto') || req.protocol || 'http';
      const host = req.get('host') || 'localhost:9000';
      const origin = req.get('origin') || `${protocol}://${host}`;
      const baseUrl = process.env.BASE_URL || origin;

      if (!googleSheetsSetting) {
        const defaultConfig = {
          enabled: false,
          client_id: '',
          client_secret: '',
          redirect_uri: `${baseUrl}/api/google/sheets/callback`
        };
        return res.json(defaultConfig);
      }

      const config = { ...(googleSheetsSetting.value as any) };
      if (config.client_secret) {
        config.client_secret = '••••••••';
      }

      res.json(config);
    } catch (error) {
      console.error("Error fetching Google Sheets OAuth settings:", error);
      res.status(500).json({ error: "Failed to fetch Google Sheets OAuth settings" });
    }
  });


  app.post("/api/admin/settings/integrations/google-sheets", ensureSuperAdmin, async (req, res) => {
    try {
      const { enabled, client_id, client_secret, redirect_uri } = req.body;

      if (enabled && (!client_id || !client_secret)) {
        return res.status(400).json({ error: "Client ID and Client Secret are required when enabling Google Sheets integration" });
      }


      const protocol = req.get('x-forwarded-proto') || req.protocol || 'http';
      const host = req.get('host') || 'localhost:9000';
      const origin = req.get('origin') || `${protocol}://${host}`;
      const baseUrl = process.env.BASE_URL || origin;

      const googleSheetsSettings = {
        enabled: !!enabled,
        client_id: client_id || '',
        client_secret: client_secret || '',
        redirect_uri: redirect_uri || `${baseUrl}/api/google/sheets/callback`
      };

      await storage.saveAppSetting('google_sheets_oauth', googleSheetsSettings);

      res.json({
        message: "Google Sheets OAuth settings saved successfully",
        settings: {
          ...googleSheetsSettings,
          client_secret: googleSheetsSettings.client_secret ? '••••••••' : ''
        }
      });
    } catch (error) {
      console.error("Error saving Google Sheets OAuth settings:", error);
      res.status(500).json({ error: "Failed to save Google Sheets OAuth settings" });
    }
  });

  app.post("/api/webhooks/stripe", async (req: Request, res: Response) => {
    try {
      const stripeSettingObj = await storage.getAppSetting('payment_stripe');

      if (!stripeSettingObj || !stripeSettingObj.value) {
        return res.status(400).json({ error: "Stripe is not configured" });
      }

      const stripeSettings = stripeSettingObj.value as any;

      const stripe = new Stripe(stripeSettings.secretKey, {
        apiVersion: '2025-04-30.basil'
      });

      const signature = req.headers['stripe-signature'] as string;

      if (!signature || !stripeSettings.webhookSecret) {
        return res.status(400).json({ error: "Missing signature or webhook secret" });
      }

      let event;
      try {
        event = stripe.webhooks.constructEvent(
          req.body,
          signature,
          stripeSettings.webhookSecret
        );
      } catch (err) {
        console.error('Webhook signature verification failed:', err);
        return res.status(400).send(`Webhook Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
      }

      switch (event.type) {
        case 'payment_intent.succeeded':
          const paymentIntent = event.data.object;
          if (paymentIntent.metadata && paymentIntent.metadata.transactionId) {
            const transactionId = parseInt(paymentIntent.metadata.transactionId);


            await storage.updatePaymentTransaction(transactionId, {
              status: 'completed',
              paymentIntentId: paymentIntent.id,
              receiptUrl: (paymentIntent as any).charges?.data[0]?.receipt_url
            });


            const transaction = await storage.getPaymentTransaction(transactionId);
            if (transaction && paymentIntent.metadata.companyId && paymentIntent.metadata.planId) {
              const companyId = parseInt(paymentIntent.metadata.companyId);
              const planId = parseInt(paymentIntent.metadata.planId);


              const plan = await storage.getPlan(planId);
              const updatedCompany = await storage.updateCompany(companyId, {
                planId: planId,
                plan: plan?.name.toLowerCase() || 'unknown',
                subscriptionStatus: 'active',
                subscriptionEndDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
              });




              try {
                if ((global as any).broadcastToCompany && updatedCompany) {
                  (global as any).broadcastToCompany({
                    type: 'plan_updated',
                    data: {
                      companyId,
                      newPlan: updatedCompany.plan,
                      planId: updatedCompany.planId,
                      timestamp: new Date().toISOString(),
                      changeType: 'payment_upgrade'
                    }
                  }, companyId);
                }
              } catch (broadcastError) {
                console.error('Error broadcasting plan update after payment:', broadcastError);
              }
            }
          }
          break;
        case 'payment_intent.payment_failed':
          const failedPaymentIntent = event.data.object;
          if (failedPaymentIntent.metadata && failedPaymentIntent.metadata.transactionId) {
            await storage.updatePaymentTransaction(
              parseInt(failedPaymentIntent.metadata.transactionId),
              {
                status: 'failed',
                paymentIntentId: failedPaymentIntent.id
              }
            );
          }
          break;
        default:
      }

      res.json({ received: true });
    } catch (error) {
      console.error('Error handling Stripe webhook:', error);
      res.status(500).json({ error: 'Failed to process webhook' });
    }
  });

  app.post("/api/webhooks/mercadopago", async (req: Request, res: Response) => {
    try {
      const mercadoPagoSettingObj = await storage.getAppSetting('payment_mercadopago');

      if (!mercadoPagoSettingObj || !mercadoPagoSettingObj.value) {
        return res.status(400).json({ error: "Mercado Pago is not configured" });
      }

      const mercadoPagoSettings = mercadoPagoSettingObj.value as any;

      if (!mercadoPagoSettings.clientId || !mercadoPagoSettings.clientSecret || !mercadoPagoSettings.accessToken) {
        return res.status(400).json({ error: "Mercado Pago settings are incomplete" });
      }

      if (!mercadoPagoSettings.accessToken) {
        return res.status(400).json({ error: "Mercado Pago access token is required" });
      }

      const { type, data } = req.body;

      if (!type || !data) {
        return res.status(400).json({ error: "Invalid webhook payload" });
      }

      if (type === 'payment') {
        const paymentId = data.id;

        const paymentResponse = await fetch(`https://api.mercadopago.com/v1/payments/${paymentId}`, {
          headers: {
            'Authorization': `Bearer ${mercadoPagoSettings.accessToken}`
          }
        });

        if (!paymentResponse.ok) {
          throw new Error(`Failed to get payment details: ${paymentResponse.status} ${paymentResponse.statusText}`);
        }

        const paymentData = await paymentResponse.json();

        if (paymentData.external_reference) {
          const transactionId = parseInt(paymentData.external_reference);

          if (!isNaN(transactionId)) {
            let status = 'pending';

            switch (paymentData.status) {
              case 'approved':
                status = 'completed';
                break;
              case 'rejected':
              case 'cancelled':
                status = 'failed';
                break;
              case 'refunded':
                status = 'refunded';
                break;
              default:
                status = 'pending';
            }

            await storage.updatePaymentTransaction(transactionId, {
              status: status as 'pending' | 'completed' | 'failed' | 'refunded',
              paymentIntentId: paymentData.id.toString(),
              metadata: {
                ...paymentData,
                mercadopago_status: paymentData.status,
                mercadopago_status_detail: paymentData.status_detail
              }
            });
          }
        }
      }

      res.json({ received: true });
    } catch (error) {
      console.error('Error handling Mercado Pago webhook:', error);
      res.status(500).json({ error: 'Failed to process webhook' });
    }
  });

  app.post("/api/webhooks/paypal", async (req: Request, res: Response) => {
    try {
      const paypalSettingObj = await storage.getAppSetting('payment_paypal');

      if (!paypalSettingObj || !paypalSettingObj.value) {
        return res.status(400).json({ error: "PayPal is not configured" });
      }

      const body = req.body;

      const verificationBody = 'cmd=_notify-validate&' + Object.keys(body).map(key => {
        return `${encodeURIComponent(key)}=${encodeURIComponent(body[key])}`;
      }).join('&');

      const verificationUrl = (paypalSettingObj.value as any).testMode
        ? 'https://ipnpb.sandbox.paypal.com/cgi-bin/webscr'
        : 'https://ipnpb.paypal.com/cgi-bin/webscr';

      const verificationResponse = await fetch(verificationUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: verificationBody
      });

      const verificationText = await verificationResponse.text();

      if (verificationText === 'VERIFIED') {
        const paymentStatus = body.payment_status;
        const transactionId = body.custom;

        if (transactionId) {
          let status = 'pending';

          switch (paymentStatus) {
            case 'Completed':
              status = 'completed';
              break;
            case 'Failed':
            case 'Denied':
            case 'Expired':
              status = 'failed';
              break;
            case 'Refunded':
            case 'Reversed':
              status = 'refunded';
              break;
            default:
              status = 'pending';
          }

          await storage.updatePaymentTransaction(parseInt(transactionId), {
            status: status as 'pending' | 'completed' | 'failed' | 'refunded',
            paymentIntentId: body.txn_id,
            receiptUrl: body.receipt_url || '',
            metadata: {
              ...body,
              paypal_payment_status: paymentStatus
            }
          });
        }
      } else {
        console.error('Invalid PayPal IPN message:', body);
      }

      res.status(200).end();
    } catch (error) {
      console.error('Error handling PayPal webhook:', error);
      res.status(200).end();
    }
  });

  app.post("/api/webhooks/moyasar", async (req: Request, res: Response) => {
    try {
      const moyasarSettingObj = await storage.getAppSetting('payment_moyasar');

      if (!moyasarSettingObj || !moyasarSettingObj.value) {
        console.error('Moyasar webhook received but Moyasar is not configured');
        return res.status(400).json({ error: "Moyasar is not configured" });
      }

      const body = req.body;


      if (!body.type || !body.data) {
        console.error('Invalid Moyasar webhook payload:', body);
        return res.status(400).json({ error: "Invalid webhook payload" });
      }

      const eventType = body.type;
      const paymentData = body.data;

      console.log(`Received Moyasar webhook: ${eventType}`, {
        paymentId: paymentData.id,
        status: paymentData.status,
        amount: paymentData.amount
      });


      if (eventType.startsWith('payment_')) {
        const paymentId = paymentData.id;


        const transactions = await storage.getAllPaymentTransactions();
        const transaction = transactions.find((t: any) =>
          t.paymentIntentId === paymentId || t.externalTransactionId === paymentId
        );

        if (!transaction) {
          console.error(`No transaction found for Moyasar payment ID: ${paymentId}`);
          return res.status(404).json({ error: "Transaction not found" });
        }


        const statusMap: { [key: string]: 'completed' | 'failed' | 'pending' | 'refunded' | 'cancelled' } = {
          'payment_paid': 'completed',
          'payment_failed': 'failed',
          'payment_authorized': 'pending',
          'payment_captured': 'completed',
          'payment_voided': 'cancelled',
          'payment_refunded': 'refunded',
          'payment_verified': 'completed'
        };

        const newStatus = statusMap[eventType];

        if (newStatus && newStatus !== transaction.status) {
          await storage.updatePaymentTransaction(transaction.id, {
            status: newStatus
          });




          if (newStatus === 'completed' && transaction.companyId && transaction.planId) {
            const company = await storage.getCompany(transaction.companyId);
            if (company && (company.planId !== transaction.planId || company.subscriptionStatus !== 'active')) {

              const plan = await storage.getPlan(transaction.planId);
              const updatedCompany = await storage.updateCompany(transaction.companyId, {
                planId: transaction.planId,
                plan: plan?.name || 'unknown',
                subscriptionStatus: 'active',
                subscriptionEndDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
              });




              try {
                if ((global as any).broadcastToCompany && updatedCompany) {
                  (global as any).broadcastToCompany({
                    type: 'plan_updated',
                    data: {
                      companyId: transaction.companyId,
                      newPlan: updatedCompany.plan,
                      planId: updatedCompany.planId,
                      timestamp: new Date().toISOString(),
                      changeType: 'subscription_activation'
                    }
                  }, transaction.companyId);
                }
              } catch (broadcastError) {
                console.error('Error broadcasting plan update after subscription activation:', broadcastError);
              }
            }
          }
        }
      }

      res.status(200).json({ received: true });
    } catch (error) {
      console.error('Error handling Moyasar webhook:', error);
      res.status(500).json({ error: 'Failed to process webhook' });
    }
  });

  app.get("/api/admin/analytics", ensureSuperAdmin, async (req: Request, res: Response) => {
    try {
      const timeRange = req.query.timeRange || '30days';

      let startDate = new Date();
      switch(timeRange) {
        case '7days':
          startDate.setDate(startDate.getDate() - 7);
          break;
        case '30days':
          startDate.setDate(startDate.getDate() - 30);
          break;
        case '90days':
          startDate.setDate(startDate.getDate() - 90);
          break;
        case 'year':
          startDate.setFullYear(startDate.getFullYear() - 1);
          break;
        case 'all':
          startDate = new Date(0);
          break;
        default:
          startDate.setDate(startDate.getDate() - 30);
      }

      const startDateStr = startDate.toISOString();

      const users = await storage.getAllUsers();
      const totalUsers = users.length;

      const companies = await storage.getAllCompanies();
      const totalCompanies = companies.length;
      const activeCompanies = companies.filter(company => company.active).length;

      const totalConversations = await storage.getConversationsCount();

      const totalMessages = await storage.getMessagesCount();

      const contactsData = await storage.getContacts();
      const totalContacts = contactsData.total;

      const userGrowthQuery = `
        SELECT
          DATE_TRUNC('month', created_at) AS date,
          COUNT(*) AS count
        FROM
          users
        WHERE
          created_at >= $1
        GROUP BY
          DATE_TRUNC('month', created_at)
        ORDER BY
          date ASC
      `;

      const userGrowthResult = await pool.query(userGrowthQuery, [startDateStr]);
      const userGrowth = userGrowthResult.rows.map(row => ({
        date: row.date.toISOString().split('T')[0],
        count: parseInt(row.count)
      }));

      const messagesByChannelQuery = `
        SELECT
          c.channel_type AS channel,
          COUNT(m.id) AS count
        FROM
          messages m
        JOIN
          conversations conv ON m.conversation_id = conv.id
        JOIN
          channel_connections c ON conv.channel_id = c.id
        WHERE
          m.created_at >= $1
        GROUP BY
          c.channel_type
        ORDER BY
          count DESC
      `;

      const messagesByChannelResult = await pool.query(messagesByChannelQuery, [startDateStr]);
      const messagesByChannel = messagesByChannelResult.rows.map(row => ({
        channel: row.channel,
        count: parseInt(row.count)
      }));

      const conversationsByCompanyQuery = `
        SELECT
          c.name AS company,
          COUNT(conv.id) AS count
        FROM
          conversations conv
        JOIN
          companies c ON conv.company_id = c.id
        WHERE
          conv.created_at >= $1
        GROUP BY
          c.name
        ORDER BY
          count DESC
        LIMIT 5
      `;

      const conversationsByCompanyResult = await pool.query(conversationsByCompanyQuery, [startDateStr]);
      const conversationsByCompany = conversationsByCompanyResult.rows.map(row => ({
        company: row.company,
        count: parseInt(row.count)
      }));

      const last7Days = new Date();
      last7Days.setDate(last7Days.getDate() - 7);

      const activeUsersByDayQuery = `
        SELECT
          DATE_TRUNC('day', sess.expire) AS date,
          COUNT(DISTINCT sess.sid) AS count
        FROM
          session sess
        WHERE
          sess.expire >= $1
        GROUP BY
          DATE_TRUNC('day', sess.expire)
        ORDER BY
          date ASC
      `;

      const activeUsersByDayResult = await pool.query(activeUsersByDayQuery, [last7Days.toISOString()]);
      const activeUsersByDay = activeUsersByDayResult.rows.map(row => ({
        date: row.date.toISOString().split('T')[0],
        count: parseInt(row.count)
      }));

      if (userGrowth.length === 0) {
        userGrowth.push({ date: new Date().toISOString().split('T')[0], count: 0 });
      }

      if (messagesByChannel.length === 0) {
        messagesByChannel.push({ channel: 'No Data', count: 0 });
      }

      if (conversationsByCompany.length === 0) {
        conversationsByCompany.push({ company: 'No Data', count: 0 });
      }

      if (activeUsersByDay.length === 0) {
        activeUsersByDay.push({ date: new Date().toISOString().split('T')[0], count: 0 });
      }

      res.json({
        totalUsers,
        totalCompanies,
        activeCompanies,
        totalConversations,
        totalMessages,
        totalContacts,
        userGrowth,
        messagesByChannel,
        conversationsByCompany,
        activeUsersByDay
      });
    } catch (error) {
      console.error("Error fetching analytics data:", error);
      res.status(500).json({ error: "Failed to fetch analytics data" });
    }
  });




  app.get("/api/admin/payment-transactions", ensureSuperAdmin, async (_req, res) => {
    try {
      const transactions = await storage.getAllPaymentTransactions();
      res.json(transactions);
    } catch (error) {
      console.error("Error fetching payment transactions:", error);
      res.status(500).json({ error: "Failed to fetch payment transactions" });
    }
  });

  app.get("/api/admin/companies/:id/payment-transactions", ensureSuperAdmin, async (req: Request, res: Response) => {
    try {
      const companyId = parseInt(req.params.id);
      const transactions = await storage.getPaymentTransactionsByCompany(companyId);
      res.json(transactions);
    } catch (error) {
      console.error("Error fetching company payment transactions:", error);
      res.status(500).json({ error: "Failed to fetch company payment transactions" });
    }
  });

  app.put("/api/admin/payment-transactions/:id", ensureSuperAdmin, async (req: Request, res: Response) => {
    try {
      const transactionId = parseInt(req.params.id);
      const { status } = req.body;

      if (!status || !['pending', 'completed', 'failed', 'refunded'].includes(status)) {
        return res.status(400).json({ error: "Valid status is required" });
      }

      const transaction = await storage.updatePaymentTransaction(transactionId, { status });

      res.json({
        message: "Payment transaction updated successfully",
        transaction
      });
    } catch (error) {
      console.error("Error updating payment transaction:", error);
      res.status(500).json({ error: "Failed to update payment transaction" });
    }
  });

  app.get('/api/registration/status', async (_req, res) => {
    try {
      const registrationSettingObj = await storage.getAppSetting('registration_settings');
      const registrationSettings = (registrationSettingObj?.value as any) || { enabled: true, requireApproval: false };

      res.json({
        enabled: registrationSettings.enabled ?? true,
        requireApproval: registrationSettings.requireApproval ?? false
      });
    } catch (error) {
      console.error('Error getting registration status:', error);
      res.status(500).json({ error: 'Failed to get registration status' });
    }
  });


  app.get("/api/admin/backup/config", ensureSuperAdmin, async (_req, res) => {
    try {
      const config = await storage.getAppSetting('backup_config');
      const defaultConfig = {
        enabled: false,
        schedules: [],
        retention_days: 30,
        storage_locations: ['local'],
        google_drive: {
          enabled: false,
          folder_id: null,
          credentials: null
        },
        encryption: {
          enabled: false,
          key: null
        }
      };

      res.json(config?.value || defaultConfig);
    } catch (error) {
      console.error("Error fetching backup config:", error);
      res.status(500).json({ error: "Failed to fetch backup configuration" });
    }
  });

  app.post("/api/admin/backup/config", ensureSuperAdmin, async (req, res) => {
    try {
      const config = req.body;
      await storage.saveAppSetting('backup_config', config);

      res.json({ message: "Backup configuration saved successfully" });
    } catch (error) {
      console.error("Error saving backup config:", error);
      res.status(500).json({ error: "Failed to save backup configuration" });
    }
  });

  app.post("/api/admin/backup/create", ensureSuperAdmin, async (req, res) => {
    try {
      const { description, storage_locations } = req.body;

      const { BackupService } = await import('./services/backup-service');
      const backupService = new BackupService();

      const backup = await backupService.createBackup({
        type: 'manual',
        description: description || 'Manual backup',
        storage_locations: storage_locations || ['local']
      });

      res.json(backup);
    } catch (error) {
      console.error("Error creating backup:", error);
      res.status(500).json({ error: "Failed to create backup" });
    }
  });

  app.get("/api/admin/backup/list", ensureSuperAdmin, async (_req, res) => {
    try {
      const { BackupService } = await import('./services/backup-service');
      const backupService = new BackupService();

      const backups = await backupService.listBackups();
      res.json(backups);
    } catch (error) {
      console.error("Error listing backups:", error);
      res.status(500).json({ error: "Failed to list backups" });
    }
  });

  app.get("/api/admin/backup/download/:id", ensureSuperAdmin, async (req, res) => {
    try {
      const { id } = req.params;
      const { BackupService } = await import('./services/backup-service');
      const backupService = new BackupService();

      const backup = await backupService.getBackup(id);
      if (!backup) {
        return res.status(404).json({ error: "Backup not found" });
      }

      const filePath = await backupService.getBackupFilePath(backup);
      res.download(filePath, backup.filename);
    } catch (error) {
      console.error("Error downloading backup:", error);
      res.status(500).json({ error: "Failed to download backup" });
    }
  });

  app.post("/api/admin/backup/restore/:id", ensureSuperAdmin, async (req, res) => {
    try {
      const { id } = req.params;
      const { confirmationText } = req.body;
      const { BackupService } = await import('./services/backup-service');
      const backupService = new BackupService();

      const user = (req as any).user;

      const result = await backupService.restoreBackup(id, {
        userId: user?.id,
        userEmail: user?.email,
        confirmationText
      });

      res.json(result);
    } catch (error) {
      console.error("Error restoring backup:", error);
      res.status(500).json({ error: "Failed to restore backup" });
    }
  });

  app.delete("/api/admin/backup/:id", ensureSuperAdmin, async (req, res) => {
    try {
      const { id } = req.params;
      const { BackupService } = await import('./services/backup-service');
      const backupService = new BackupService();

      await backupService.deleteBackup(id);
      res.json({ message: "Backup deleted successfully" });
    } catch (error) {
      console.error("Error deleting backup:", error);
      res.status(500).json({ error: "Failed to delete backup" });
    }
  });

  app.post("/api/admin/backup/verify/:id", ensureSuperAdmin, async (req, res) => {
    try {
      const { id } = req.params;
      const { BackupService } = await import('./services/backup-service');
      const backupService = new BackupService();

      const result = await backupService.verifyBackup(id);
      res.json(result);
    } catch (error) {
      console.error("Error verifying backup:", error);
      res.status(500).json({ error: "Failed to verify backup" });
    }
  });


  const backupUploadStorage = multer.diskStorage({
    destination: (_req, _file, cb) => {
      const backupPath = path.join(process.cwd(), 'backups');
      if (!fs.existsSync(backupPath)) {
        fs.mkdirSync(backupPath, { recursive: true });
      }
      cb(null, backupPath);
    },
    filename: (_req, file, cb) => {

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const originalName = file.originalname.replace(/[^a-zA-Z0-9.-]/g, '_');
      const filename = `uploaded-${timestamp}-${originalName}`;
      cb(null, filename);
    }
  });

  const backupUpload = multer({
    storage: backupUploadStorage,
    limits: {
      fileSize: 500 * 1024 * 1024, // 500MB limit for backup files
      files: 1
    },
    fileFilter: (_req, file, cb) => {

      const allowedExtensions = ['.sql', '.backup', '.dump', '.bak'];
      const fileExt = path.extname(file.originalname).toLowerCase();

      if (allowedExtensions.includes(fileExt)) {
        cb(null, true);
      } else {
        cb(new Error('Invalid file type. Only .sql, .backup, .dump, and .bak files are allowed.'));
      }
    }
  });

  app.post("/api/admin/backup/upload", ensureSuperAdmin, backupUpload.single('backup'), async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ error: "No backup file provided" });
      }

      const { description, storage_locations } = req.body;
      const { BackupService } = await import('./services/backup-service');
      const backupService = new BackupService();


      let parsedStorageLocations = ['local'];
      if (storage_locations) {
        try {
          parsedStorageLocations = typeof storage_locations === 'string'
            ? JSON.parse(storage_locations)
            : storage_locations;
        } catch (e) {
          parsedStorageLocations = [storage_locations];
        }
      }

      const result = await backupService.processUploadedBackup({
        filePath: req.file.path,
        originalName: req.file.originalname,
        filename: req.file.filename,
        size: req.file.size,
        description: description || `Uploaded backup: ${req.file.originalname}`,
        storage_locations: parsedStorageLocations
      });

      res.json(result);
    } catch (error) {
      console.error("Error uploading backup:", error);


      if (req.file && req.file.path) {
        try {
          fs.unlinkSync(req.file.path);
        } catch (cleanupError) {
          console.error("Error cleaning up uploaded file:", cleanupError);
        }
      }

      res.status(500).json({
        error: error instanceof Error ? error.message : "Failed to upload backup"
      });
    }
  });

  app.get("/api/admin/backup/google-drive/auth-url", ensureSuperAdmin, async (_req, res) => {
    try {
      const { GoogleDriveService } = await import('./services/google-drive-service');
      const googleDriveService = new GoogleDriveService();

      const authUrl = await googleDriveService.getAuthUrl();
      res.json({ authUrl });
    } catch (error) {
      console.error("Error getting Google Drive auth URL:", error);
      res.status(500).json({ error: "Failed to get Google Drive auth URL" });
    }
  });

  app.post("/api/admin/backup/google-drive/callback", ensureSuperAdmin, async (req, res) => {
    try {
      const { code } = req.body;
      const { GoogleDriveService } = await import('./services/google-drive-service');
      const googleDriveService = new GoogleDriveService();

      const tokens = await googleDriveService.exchangeCodeForTokens(code);

      const config = await storage.getAppSetting('backup_config');
      const updatedConfig = {
        ...(config?.value as any) || {},
        google_drive: {
          ...((config?.value as any)?.google_drive) || {},
          enabled: true,
          credentials: tokens
        }
      };

      await storage.saveAppSetting('backup_config', updatedConfig);

      res.json({ message: "Google Drive connected successfully" });
    } catch (error) {
      console.error("Error handling Google Drive callback:", error);
      res.status(500).json({ error: "Failed to connect Google Drive" });
    }
  });

  app.post("/api/admin/backup/google-drive/test", ensureSuperAdmin, async (_req, res) => {
    try {
      const { GoogleDriveService } = await import('./services/google-drive-service');
      const googleDriveService = new GoogleDriveService();

      const result = await googleDriveService.testConnection();
      res.json(result);
    } catch (error) {
      console.error("Error testing Google Drive connection:", error);
      res.status(500).json({ error: "Failed to test Google Drive connection" });
    }
  });

  app.get("/api/admin/backup/google-drive/oauth-config", ensureSuperAdmin, async (_req, res) => {
    try {
      const { GoogleDriveService } = await import('./services/google-drive-service');
      const googleDriveService = new GoogleDriveService();

      const config = await googleDriveService.getOAuthConfig();
      res.json(config);
    } catch (error) {
      console.error("Error getting OAuth config:", error);
      res.status(500).json({ error: "Failed to get OAuth configuration" });
    }
  });

  app.post("/api/admin/backup/google-drive/oauth-config", ensureSuperAdmin, async (req, res) => {
    try {
      const { client_id, client_secret, redirect_uri } = req.body;

      if (!client_id || !client_secret) {
        return res.status(400).json({ error: "Client ID and Client Secret are required" });
      }

      const { GoogleDriveService } = await import('./services/google-drive-service');
      const googleDriveService = new GoogleDriveService();

      const result = await googleDriveService.saveOAuthConfig({
        client_id,
        client_secret,
        redirect_uri
      });

      res.json(result);
    } catch (error) {
      console.error("Error saving OAuth config:", error);
      res.status(500).json({ error: "Failed to save OAuth configuration" });
    }
  });

  app.delete("/api/admin/backup/google-drive/oauth-config", ensureSuperAdmin, async (_req, res) => {
    try {
      const { GoogleDriveService } = await import('./services/google-drive-service');
      const googleDriveService = new GoogleDriveService();

      const result = await googleDriveService.clearOAuthConfig();
      res.json(result);
    } catch (error) {
      console.error("Error clearing OAuth config:", error);
      res.status(500).json({ error: "Failed to clear OAuth configuration" });
    }
  });

  app.post("/api/admin/backup/google-drive/oauth-validate", ensureSuperAdmin, async (req, res) => {
    try {
      const { GoogleDriveService } = await import('./services/google-drive-service');
      const googleDriveService = new GoogleDriveService();

      const result = await googleDriveService.validateOAuthConfig();
      res.json(result);
    } catch (error) {
      console.error("Error validating OAuth config:", error);
      res.status(500).json({ error: "Failed to validate OAuth configuration" });
    }
  });

  app.get("/api/admin/backup/stats", ensureSuperAdmin, async (req, res) => {
    try {
      const { BackupService } = await import('./services/backup-service');
      const backupService = new BackupService();

      const stats = await backupService.getBackupStats();
      res.json(stats);
    } catch (error) {
      console.error("Error getting backup stats:", error);
      res.status(500).json({ error: "Failed to get backup statistics" });
    }
  });




  app.get('/api/admin/partner-configurations/:provider', ensureSuperAdmin, async (req: Request, res: Response) => {
    try {
      const { provider } = req.params;
      const config = await storage.getPartnerConfiguration(provider);

      if (!config) {
        return res.status(404).json({ error: 'Partner configuration not found' });
      }

      res.json(config);
    } catch (error) {
      console.error('Error getting partner configuration:', error);
      res.status(500).json({ error: 'Failed to get partner configuration' });
    }
  });


  app.post('/api/admin/partner-configurations', ensureSuperAdmin, async (req: Request, res: Response) => {
    try {
      const configData = req.body;
      const config = await storage.createPartnerConfiguration(configData);
      res.status(201).json(config);
    } catch (error) {
      console.error('Error creating partner configuration:', error);
      res.status(500).json({ error: 'Failed to create partner configuration' });
    }
  });


  app.put('/api/admin/partner-configurations/:id', ensureSuperAdmin, async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const configData = req.body;
      const config = await storage.updatePartnerConfiguration(parseInt(id), configData);
      res.json(config);
    } catch (error) {
      console.error('Error updating partner configuration:', error);
      res.status(500).json({ error: 'Failed to update partner configuration' });
    }
  });


  app.post('/api/admin/partner-configurations/validate', ensureSuperAdmin, async (req: Request, res: Response) => {
    const { provider, partnerApiKey, partnerId } = req.body;

    try {

      if (provider === '360dialog') {

        if (!partnerApiKey || !partnerId) {
          return res.status(400).json({
            valid: false,
            error: 'Partner API Key and Partner ID are required'
          });
        }

        console.log('Validating 360Dialog Partner credentials:', {
          partnerId,
          apiKeyLength: partnerApiKey?.length || 0,
          apiKeyPrefix: partnerApiKey?.substring(0, 8) + '...'
        });


        const response = await axios.get(`https://hub.360dialog.io/api/v2/partners/${partnerId}`, {
          headers: {
            'x-api-key': partnerApiKey,
            'Content-Type': 'application/json'
          },
          timeout: 10000
        });

        console.log('360Dialog API Response:', {
          status: response.status,
          partnerId: response.data?.id,
          expectedPartnerId: partnerId,
          brandName: response.data?.brand_name
        });

        if (response.status === 200) {
          if (response.data.id === partnerId) {
            res.json({
              valid: true,
              message: 'Partner credentials are valid',
              partnerInfo: {
                id: response.data.id,
                name: response.data.brand_name || 'Unknown Partner',
                authMethod: 'api_key'
              }
            });
          } else {
            res.status(400).json({
              valid: false,
              error: `Partner ID mismatch. Expected: ${partnerId}, Got: ${response.data.id}`,
              details: 'The Partner ID in your configuration does not match the one associated with your API key.'
            });
          }
        } else {
          res.status(400).json({
            valid: false,
            error: 'Invalid partner credentials',
            details: 'The API returned an unexpected status code.'
          });
        }
      } else if (provider === 'meta') {

        const { appId, appSecret, businessManagerId, accessToken } = req.body;

        if (!appId || !appSecret || !businessManagerId) {
          return res.status(400).json({ valid: false, error: 'App ID, App Secret, and Business Manager ID are required' });
        }


        const testUrl = `https://graph.facebook.com/v22.0/${businessManagerId}`;
        const response = await axios.get(testUrl, {
          params: {
            access_token: accessToken || `${appId}|${appSecret}`,
            fields: 'id,name'
          }
        });

        if (response.status === 200 && response.data.id === businessManagerId) {
          res.json({ valid: true, message: 'Meta Partner API credentials are valid' });
        } else {
          res.status(400).json({ valid: false, error: 'Business Manager ID mismatch' });
        }
      } else {
        res.status(400).json({ valid: false, error: 'Unsupported provider' });
      }
    } catch (error: any) {
      console.error('Error validating partner credentials:', error);


      if (provider === '360dialog') {
        const dialog360Error = parseDialog360Error(error);
        const errorResponse = createErrorResponse(dialog360Error);


        let specificGuidance = '';
        if (error.response?.status === 401) {
          specificGuidance = 'The API key is invalid or has been revoked. Please check your 360Dialog Partner dashboard and generate a new API key.';
        } else if (error.response?.status === 403) {
          specificGuidance = 'The API key does not have sufficient permissions. Ensure your partner account has the necessary permissions.';
        } else if (error.response?.status === 404) {
          specificGuidance = 'The partner endpoint was not found. This may indicate an issue with the 360Dialog API.';
        } else if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
          specificGuidance = 'Unable to connect to 360Dialog API. Please check your internet connection.';
        }

        res.status(400).json({
          valid: false,
          error: dialog360Error.userMessage,
          details: specificGuidance || dialog360Error.suggestedAction,
          errorCode: dialog360Error.code,
          retryable: dialog360Error.retryable
        });
      } else {

        res.status(400).json({
          valid: false,
          error: error.response?.data?.message || 'Failed to validate partner credentials'
        });
      }
    }
  });


  app.get('/api/admin/partner-configurations', ensureSuperAdmin, async (req: Request, res: Response) => {
    try {
      const configs = await storage.getAllPartnerConfigurations();
      res.json(configs);
    } catch (error) {
      console.error('Error getting partner configurations:', error);
      res.status(500).json({ error: 'Failed to get partner configurations' });
    }
  });


  app.delete('/api/admin/partner-configurations/:id', ensureSuperAdmin, async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      await storage.deletePartnerConfiguration(parseInt(id));
      res.status(204).send();
    } catch (error) {
      console.error('Error deleting partner configuration:', error);
      res.status(500).json({ error: 'Failed to delete partner configuration' });
    }
  });



  app.get("/api/admin/websites", ensureSuperAdmin, async (req: Request, res: Response) => {
    try {
      const websites = await storage.getAllWebsites();
      res.json(websites);
    } catch (error) {
      console.error("Error fetching websites:", error);
      res.status(500).json({ error: "Failed to fetch websites" });
    }
  });

  app.get("/api/admin/websites/:id", ensureSuperAdmin, async (req: Request, res: Response) => {
    try {
      const websiteId = parseInt(req.params.id);
      const website = await storage.getWebsite(websiteId);

      if (!website) {
        return res.status(404).json({ error: "Website not found" });
      }

      res.json(website);
    } catch (error) {
      console.error("Error fetching website:", error);
      res.status(500).json({ error: "Failed to fetch website" });
    }
  });

  app.post("/api/admin/websites", ensureSuperAdmin, async (req: Request, res: Response) => {
    try {
      const {
        title,
        slug,
        description,
        metaTitle,
        metaDescription,
        metaKeywords,
        grapesData,
        grapesHtml,
        grapesCss,
        grapesJs,
        favicon,
        customCss,
        customJs,
        customHead,
        status,
        googleAnalyticsId,
        facebookPixelId,
        theme
      } = req.body;

      if (!title || !slug) {
        return res.status(400).json({ error: "Title and slug are required" });
      }


      const existingWebsite = await storage.getWebsiteBySlug(slug);
      if (existingWebsite) {
        return res.status(400).json({ error: "A website with this slug already exists" });
      }

      const user = req.user as any;
      const newWebsite = await storage.createWebsite({
        title,
        slug,
        description,
        metaTitle,
        metaDescription,
        metaKeywords,
        grapesData: grapesData || {},
        grapesHtml,
        grapesCss,
        grapesJs,
        favicon,
        customCss,
        customJs,
        customHead,
        status: status || 'draft',
        googleAnalyticsId,
        facebookPixelId,
        theme: theme || 'default',
        createdById: user.id
      });

      res.status(201).json(newWebsite);
    } catch (error) {
      console.error("Error creating website:", error);
      res.status(500).json({ error: "Failed to create website" });
    }
  });

  app.put("/api/admin/websites/:id", ensureSuperAdmin, async (req: Request, res: Response) => {
    try {
      const websiteId = parseInt(req.params.id);
      const {
        title,
        slug,
        description,
        metaTitle,
        metaDescription,
        metaKeywords,
        grapesData,
        grapesHtml,
        grapesCss,
        grapesJs,
        favicon,
        customCss,
        customJs,
        customHead,
        status,
        googleAnalyticsId,
        facebookPixelId,
        theme
      } = req.body;

      const existingWebsite = await storage.getWebsite(websiteId);
      if (!existingWebsite) {
        return res.status(404).json({ error: "Website not found" });
      }


      if (slug && slug !== existingWebsite.slug) {
        const conflictingWebsite = await storage.getWebsiteBySlug(slug);
        if (conflictingWebsite && conflictingWebsite.id !== websiteId) {
          return res.status(400).json({ error: "A website with this slug already exists" });
        }
      }

      const updatedWebsite = await storage.updateWebsite(websiteId, {
        title,
        slug,
        description,
        metaTitle,
        metaDescription,
        metaKeywords,
        grapesData,
        grapesHtml,
        grapesCss,
        grapesJs,
        favicon,
        customCss,
        customJs,
        customHead,
        status,
        googleAnalyticsId,
        facebookPixelId,
        theme
      });

      res.json(updatedWebsite);
    } catch (error) {
      console.error("Error updating website:", error);
      res.status(500).json({ error: "Failed to update website" });
    }
  });

  app.delete("/api/admin/websites/:id", ensureSuperAdmin, async (req: Request, res: Response) => {
    try {
      const websiteId = parseInt(req.params.id);
      const success = await storage.deleteWebsite(websiteId);

      if (!success) {
        return res.status(404).json({ error: "Website not found" });
      }

      res.json({ message: "Website deleted successfully" });
    } catch (error) {
      console.error("Error deleting website:", error);
      res.status(500).json({ error: "Failed to delete website" });
    }
  });

  app.post("/api/admin/websites/:id/publish", ensureSuperAdmin, async (req: Request, res: Response) => {
    try {
      const websiteId = parseInt(req.params.id);
      const publishedWebsite = await storage.publishWebsite(websiteId);
      res.json(publishedWebsite);
    } catch (error) {
      console.error("Error publishing website:", error);
      res.status(500).json({ error: "Failed to publish website" });
    }
  });

  app.post("/api/admin/websites/:id/unpublish", ensureSuperAdmin, async (req: Request, res: Response) => {
    try {
      const websiteId = parseInt(req.params.id);
      const unpublishedWebsite = await storage.unpublishWebsite(websiteId);
      res.json(unpublishedWebsite);
    } catch (error) {
      console.error("Error unpublishing website:", error);
      res.status(500).json({ error: "Failed to unpublish website" });
    }
  });



  registerAffiliateRoutes(app);


  app.use('/api/admin/ai-credentials', adminAiCredentialsRoutes);


  app.get("/api/admin/companies/:id/data-preview", ensureSuperAdmin, async (req: Request, res: Response) => {
    try {
      const companyId = parseInt(req.params.id);

      if (isNaN(companyId)) {
        return res.status(400).json({ error: "Invalid company ID" });
      }

      const company = await storage.getCompany(companyId);
      if (!company) {
        return res.status(404).json({ error: "Company not found" });
      }


      const [
        contactsData,
        conversationsCount,
        messagesCount,
        channelConnectionsData,
        usersData,
        templatesData,
        campaignsData
      ] = await Promise.all([
        storage.getContacts({ companyId }),
        storage.getConversationsCountByCompany(companyId),
        storage.getMessagesCountByCompany(companyId),
        storage.getChannelConnectionsByCompany(companyId),
        storage.getUsersByCompany(companyId),
        storage.getFollowUpTemplatesByCompany(companyId),
        Promise.resolve([]) // Campaigns - placeholder for now
      ]);

      const contactsCount = contactsData.total;
      const channelConnectionsCount = channelConnectionsData.length;
      const usersCount = usersData.length;
      const templatesCount = templatesData.length;
      const campaignsCount = campaignsData.length;

      const dataCategories = [
        {
          id: 'media',
          name: 'Media Files',
          description: 'Images, documents, videos, and other uploaded files',
          icon: 'Files',
          count: 0, // TODO: Implement media count
          estimatedSize: '0 MB',
          color: 'text-blue-600',
          canClear: true,
          warning: 'This will permanently delete all uploaded media files'
        },
        {
          id: 'contacts',
          name: 'Contacts & Lists',
          description: 'Contact information, contact lists, and contact groups',
          icon: 'Users',
          count: contactsCount,
          color: 'text-green-600',
          canClear: true,
          warning: 'This will remove all contact information and lists'
        },
        {
          id: 'conversations',
          name: 'Conversation History',
          description: 'All conversation threads and message history',
          icon: 'MessageSquare',
          count: conversationsCount,
          color: 'text-purple-600',
          canClear: true,
          warning: 'This will permanently delete all conversation history'
        },
        {
          id: 'messages',
          name: 'Individual Messages',
          description: 'All individual messages within conversations',
          icon: 'Mail',
          count: messagesCount,
          color: 'text-indigo-600',
          canClear: true,
          warning: 'This will delete all message content and attachments'
        },
        {
          id: 'templates',
          name: 'Message Templates',
          description: 'Saved message templates and quick replies',
          icon: 'Settings',
          count: templatesCount,
          color: 'text-orange-600',
          canClear: true
        },
        {
          id: 'campaigns',
          name: 'Marketing Campaigns',
          description: 'Campaign data, broadcast messages, and campaign analytics',
          icon: 'BarChart3',
          count: campaignsCount,
          color: 'text-pink-600',
          canClear: true,
          warning: 'This will remove all campaign history and analytics'
        },
        {
          id: 'analytics',
          name: 'Analytics Data',
          description: 'Performance metrics, reports, and statistical data',
          icon: 'BarChart3',
          count: 0, // TODO: Implement analytics count
          color: 'text-cyan-600',
          canClear: true,
          warning: 'This will clear all historical analytics and reports'
        },
        {
          id: 'channel_connections',
          name: 'Channel Connections',
          description: 'WhatsApp, SMS, and other messaging channel configurations',
          icon: 'Settings',
          count: channelConnectionsCount,
          color: 'text-red-600',
          canClear: false,
          warning: 'Channel connections cannot be cleared - they must be manually disconnected'
        },
        {
          id: 'users',
          name: 'User Accounts',
          description: 'Company user accounts and permissions',
          icon: 'Users',
          count: usersCount,
          color: 'text-gray-600',
          canClear: false,
          warning: 'User accounts cannot be cleared through this interface'
        }
      ];

      const warnings = [
        'Data clearing is permanent and cannot be undone',
        'Users and channel connections will remain active',
        'Company settings and configuration will be preserved',
        'Billing and subscription information will not be affected'
      ];

      const totalEstimatedSize = '0 MB'; // TODO: Calculate total data size

      res.json({
        companyId,
        companyName: company.name,
        dataCategories,
        warnings,
        totalEstimatedSize
      });
    } catch (error) {
      console.error("Error fetching company data preview:", error);
      res.status(500).json({ error: "Failed to fetch company data preview" });
    }
  });

  app.post("/api/admin/companies/:id/clear-data", ensureSuperAdmin, async (req: Request, res: Response) => {
    try {
      const companyId = parseInt(req.params.id);
      const { categories, confirmationName } = req.body;

      if (isNaN(companyId)) {
        return res.status(400).json({ error: "Invalid company ID" });
      }

      if (!categories || !Array.isArray(categories) || categories.length === 0) {
        return res.status(400).json({ error: "At least one data category must be selected" });
      }

      const company = await storage.getCompany(companyId);
      if (!company) {
        return res.status(404).json({ error: "Company not found" });
      }

      if (confirmationName !== company.name) {
        return res.status(400).json({ error: "Company name confirmation does not match" });
      }

      if (company.slug === 'system') {
        return res.status(400).json({ error: "Cannot clear data for the system company" });
      }

      let clearedItems = 0;
      const results: string[] = [];


      for (const category of categories) {
        try {
          switch (category) {
            case 'contacts':
              const contactsResult = await storage.clearCompanyContacts(companyId);
              clearedItems += contactsResult.deletedCount;
              results.push(`Cleared ${contactsResult.deletedCount} contacts`);
              break;

            case 'conversations':
              const conversationsResult = await storage.clearCompanyConversations(companyId);
              clearedItems += conversationsResult.deletedCount;
              results.push(`Cleared ${conversationsResult.deletedCount} conversations`);
              break;

            case 'messages':
              const messagesResult = await storage.clearCompanyMessages(companyId);
              clearedItems += messagesResult.deletedCount;
              results.push(`Cleared ${messagesResult.deletedCount} messages`);
              break;

            case 'templates':
              const templatesResult = await storage.clearCompanyTemplates(companyId);
              clearedItems += templatesResult.deletedCount;
              results.push(`Cleared ${templatesResult.deletedCount} templates`);
              break;

            case 'campaigns':
              const campaignsResult = await storage.clearCompanyCampaigns(companyId);
              clearedItems += campaignsResult.deletedCount;
              results.push(`Cleared ${campaignsResult.deletedCount} campaigns`);
              break;

            case 'media':
              const mediaResult = await storage.clearCompanyMedia(companyId);
              clearedItems += mediaResult.deletedCount;
              results.push(`Cleared ${mediaResult.deletedCount} media files`);
              break;

            case 'analytics':
              const analyticsResult = await storage.clearCompanyAnalytics(companyId);
              clearedItems += analyticsResult.deletedCount;
              results.push(`Cleared ${analyticsResult.deletedCount} analytics records`);
              break;

            default:
              console.warn(`Unknown data category: ${category}`);
          }
        } catch (categoryError) {
          console.error(`Error clearing ${category} for company ${companyId}:`, categoryError);
          results.push(`Failed to clear ${category}: ${categoryError instanceof Error ? categoryError.message : 'Unknown error'}`);
        }
      }

      res.json({
        message: `Successfully cleared data for ${company.name}. ${clearedItems} total items cleared.`,
        details: results,
        clearedCategories: categories,
        totalItemsCleared: clearedItems
      });
    } catch (error) {
      console.error("Error clearing company data:", error);
      res.status(500).json({ error: "Failed to clear company data" });
    }
  });
}

export { registerAdminRoutes };
