import { useRef, useEffect, memo, useCallback } from 'react';
import { useTranslation } from '@/hooks/use-translation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Skeleton } from '@/components/ui/skeleton';
import {
  MoreVertical,
  Star,
  Archive,
  Trash2,
  Clock,
  Check,
  CheckCheck
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';

interface MessengerConversation {
  id: number;
  contactId: number;
  contact: {
    id: number;
    name: string;
    phone: string;
    profilePicture?: string;
  };
  lastMessage?: {
    id: number;
    content: string;
    createdAt: string;
    direction: 'inbound' | 'outbound';
    type: string;
    status?: 'sent' | 'delivered' | 'read';
  };
  unreadCount: number;
  isOnline?: boolean;
  lastSeen?: string;
  createdAt: string;
  updatedAt: string;
  isStarred?: boolean;
  isArchived?: boolean;
}

interface MessengerListProps {
  conversations: MessengerConversation[];
  selectedConversation: MessengerConversation | null;
  onConversationSelect: (conversation: MessengerConversation) => void;
  isLoading: boolean;
  onLoadMore: () => void;
  hasMore: boolean;
  isLoadingMore: boolean;
  onStarConversation?: (conversationId: number) => void;
  onArchiveConversation?: (conversationId: number) => void;
  onDeleteConversation?: (conversationId: number) => void;
}

export default function MessengerList({
  conversations,
  selectedConversation,
  onConversationSelect,
  isLoading,
  onLoadMore,
  hasMore,
  isLoadingMore,
  onStarConversation,
  onArchiveConversation,
  onDeleteConversation
}: MessengerListProps) {
  const { t } = useTranslation();
  const scrollContainerRef = useRef<HTMLDivElement>(null);


  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    let ticking = false;

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          const { scrollTop, scrollHeight, clientHeight } = container;
          const isNearBottom = scrollTop + clientHeight >= scrollHeight - 100;

          if (isNearBottom && hasMore && !isLoadingMore) {
            onLoadMore();
          }
          ticking = false;
        });
        ticking = true;
      }
    };

    container.addEventListener('scroll', handleScroll, { passive: true });
    return () => container.removeEventListener('scroll', handleScroll);
  }, [hasMore, isLoadingMore, onLoadMore]);

  const getMessageStatusIcon = (message: MessengerConversation['lastMessage']) => {
    if (!message || message.direction !== 'outbound') return null;

    switch (message.status) {
      case 'sent':
        return <Check className="h-3 w-3 text-gray-400" />;
      case 'delivered':
        return <CheckCheck className="h-3 w-3 text-gray-400" />;
      case 'read':
        return <CheckCheck className="h-3 w-3 text-blue-500" />;
      default:
        return <Clock className="h-3 w-3 text-gray-400" />;
    }
  };

  const formatLastMessageTime = useCallback((dateString: string): string => {
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

      if (diffInHours < 24) {
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
      } else if (diffInHours < 168) { // 7 days
        return date.toLocaleDateString([], { weekday: 'short' });
      } else {
        return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
      }
    } catch {
      return '';
    }
  }, []);

  const getInitials = useCallback((name: string): string => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .substring(0, 2);
  }, []);

  const getMessagePreview = useCallback((conversation: MessengerConversation): string => {
    if (!conversation.lastMessage) {
      return t('messenger.no_messages', 'No messages yet');
    }

    const { content, type, direction } = conversation.lastMessage;

    if (type === 'media') {
      return direction === 'outbound'
        ? t('messenger.you_sent_media', 'You sent a photo')
        : t('messenger.sent_media', 'Sent a photo');
    }

    if (type === 'quick_reply') {
      return direction === 'outbound'
        ? t('messenger.you_sent_quick_reply', 'You sent a quick reply')
        : t('messenger.sent_quick_reply', 'Sent a quick reply');
    }

    const prefix = direction === 'outbound' ? t('messenger.you', 'You') + ': ' : '';
    return prefix + (content.length > 50 ? content.substring(0, 50) + '...' : content);
  }, [t]);

  if (isLoading && conversations.length === 0) {
    return (
      <div className="flex-1 overflow-hidden">
        <div className="p-4 space-y-4">
          {Array.from({ length: 8 }).map((_, index) => (
            <div key={index} className="flex items-center space-x-3 animate-pulse">
              <Skeleton className="h-12 w-12 rounded-full bg-gray-200" />
              <div className="flex-1 space-y-2">
                <Skeleton className="h-4 bg-gray-200" style={{ width: `${Math.random() * 30 + 60}%` }} />
                <Skeleton className="h-3 bg-gray-100" style={{ width: `${Math.random() * 40 + 40}%` }} />
              </div>
              <div className="flex flex-col items-end space-y-1">
                <Skeleton className="h-3 w-12 bg-gray-100" />
                <Skeleton className="h-4 w-4 rounded-full bg-gray-200" />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (!isLoading && conversations.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="text-center text-gray-500">
          <div className="mx-auto mb-4 w-12 h-12 flex items-center justify-center">
            <i className="ri-messenger-line text-4xl text-gray-300"></i>
          </div>
          <h3 className="text-lg font-medium mb-2">
            {t('messenger.no_conversations', 'No Conversations')}
          </h3>
          <p className="text-sm">
            {t('messenger.no_conversations_desc', 'Start a conversation to see it here')}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-hidden">
      <div 
        ref={scrollContainerRef}
        className="h-full overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent"
      >
        <div className="divide-y divide-gray-100">
          {conversations.map((conversation) => {

            if (!conversation || !conversation.contact) {
              console.warn('Invalid conversation data:', conversation);
              return null;
            }

            return (
              <div
                key={conversation.id}
                className={cn(
                  "flex items-center p-4 hover:bg-gray-50 cursor-pointer transition-colors relative group",
                  selectedConversation?.id === conversation.id && "bg-blue-50 border-r-2 border-blue-500",
                  conversation.isArchived && "opacity-60"
                )}
                onClick={() => onConversationSelect(conversation)}
              >
                {/* Profile Picture */}
                <div className="relative">
                  <Avatar className="h-12 w-12">
                    <AvatarImage
                      src={conversation.contact?.profilePicture || ''}
                      alt={conversation.contact?.name || 'Contact'}
                    />
                    <AvatarFallback className="bg-blue-100 text-blue-700 font-medium">
                      {getInitials(conversation.contact?.name || 'Unknown')}
                    </AvatarFallback>
                  </Avatar>
                  {conversation.isOnline && (
                    <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white rounded-full"></div>
                  )}
                </div>

              {/* Conversation Info */}
              <div className="flex-1 min-w-0 ml-3">
                <div className="flex items-center justify-between mb-1">
                  <div className="flex items-center gap-1">
                    <h3 className={cn(
                      "text-sm font-medium truncate",
                      conversation.unreadCount > 0 ? "text-gray-900" : "text-gray-700"
                    )}>
                      {conversation.contact?.name || 'Unknown Contact'}
                    </h3>
                    {conversation.isStarred && (
                      <Star className="h-3 w-3 text-yellow-500 fill-current flex-shrink-0" />
                    )}
                  </div>
                  <div className="flex items-center gap-1 text-xs text-gray-500">
                    {getMessageStatusIcon(conversation.lastMessage)}
                    {conversation.lastMessage && (
                      <span>{formatLastMessageTime(conversation.lastMessage.createdAt)}</span>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <p className={cn(
                    "text-sm truncate",
                    conversation.unreadCount > 0 ? "text-gray-900 font-medium" : "text-gray-500"
                  )}>
                    {getMessagePreview(conversation)}
                  </p>
                  
                  <div className="flex items-center gap-2 ml-2">
                    {conversation.isStarred && (
                      <Star className="h-3 w-3 text-yellow-500 fill-current" />
                    )}
                    {conversation.unreadCount > 0 && (
                      <Badge className="bg-blue-500 text-white text-xs h-5 px-2 min-w-[20px] flex items-center justify-center">
                        {conversation.unreadCount > 99 ? '99+' : conversation.unreadCount}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>

              {/* Actions Menu */}
              <div className="opacity-0 group-hover:opacity-100 transition-opacity ml-2">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => onStarConversation?.(conversation.id)}>
                      <Star className="h-4 w-4 mr-2" />
                      {conversation.isStarred 
                        ? t('messenger.unstar', 'Unstar') 
                        : t('messenger.star', 'Star')
                      }
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onArchiveConversation?.(conversation.id)}>
                      <Archive className="h-4 w-4 mr-2" />
                      {conversation.isArchived
                        ? t('messenger.unarchive', 'Unarchive')
                        : t('messenger.archive', 'Archive')
                      }
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      onClick={() => onDeleteConversation?.(conversation.id)}
                      className="text-red-600"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      {t('messenger.delete', 'Delete')}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
            );
          })}
        </div>

        {/* Loading More */}
        {isLoadingMore && (
          <div className="p-4 space-y-4 border-t border-gray-100">
            <div className="text-center text-xs text-gray-500 mb-2">
              {t('messenger.loading_more', 'Loading more conversations...')}
            </div>
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="flex items-center space-x-3 animate-pulse">
                <Skeleton className="h-12 w-12 rounded-full bg-gray-200" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 bg-gray-200" style={{ width: `${Math.random() * 30 + 60}%` }} />
                  <Skeleton className="h-3 bg-gray-100" style={{ width: `${Math.random() * 40 + 40}%` }} />
                </div>
                <div className="flex flex-col items-end space-y-1">
                  <Skeleton className="h-3 w-12 bg-gray-100" />
                  <Skeleton className="h-4 w-4 rounded-full bg-gray-200" />
                </div>
              </div>
            ))}
          </div>
        )}

        {/* End of list indicator */}
        {!hasMore && conversations.length > 0 && (
          <div className="p-4 text-center text-sm text-gray-500">
            {t('messenger.end_of_conversations', 'You\'ve reached the end of your conversations')}
          </div>
        )}
      </div>
    </div>
  );
}
