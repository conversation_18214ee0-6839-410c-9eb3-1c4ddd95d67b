#!/usr/bin/env tsx

/**
 * <PERSON><PERSON><PERSON> to fix contact duplicates and add database constraints
 * This script should be run once to clean up existing duplicates
 * and prevent future duplicates from being created.
 */

import { db } from '../db';
import { sql } from 'drizzle-orm';
import fs from 'fs';
import path from 'path';

async function runMigration() {

  
  try {

    const migrationPath = path.join(__dirname, '../migrations/20241214_add_contact_unique_constraints.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    

    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    

    

    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];

      
      try {
        await db.execute(sql.raw(statement));

      } catch (error) {
        console.error(`❌ Error executing statement ${i + 1}:`, error);
        console.error(`Statement was: ${statement.substring(0, 100)}...`);
        

        continue;
      }
    }
    


    

    const duplicateReport = await db.execute(sql`
      SELECT
        COUNT(*) as total_contacts,
        COUNT(CASE WHEN is_active = false AND name LIKE '%(DUPLICATE - MERGED)%' THEN 1 END) as merged_duplicates,
        COUNT(DISTINCT phone) as unique_phones,
        COUNT(DISTINCT email) as unique_emails
      FROM contacts
    `);


    const reportData = duplicateReport.rows?.[0] as any;




    

    
  } catch (error) {
    console.error('💥 Fatal error during migration:', error);
    process.exit(1);
  }
}


if (require.main === module) {
  runMigration()
    .then(() => {

      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Migration failed:', error);
      process.exit(1);
    });
}

export { runMigration };
