
@keyframes voiceWave {
  0%, 100% {
    height: 8px;
    opacity: 0.4;
  }
  50% {
    height: 24px;
    opacity: 1;
  }
}

.animate-voiceWave {
  animation: voiceWave 1.5s ease-in-out infinite;
}

@keyframes voiceWaveWhite {
  0%, 100% {
    height: 8px;
    opacity: 0.6;
  }
  50% {
    height: 24px;
    opacity: 1;
  }
}

@keyframes sendPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(37, 211, 102, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(37, 211, 102, 0);
  }
}

.send-button-pulse {
  animation: sendPulse 2s infinite;
}

.send-button-transition {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.mic-button-hover:hover {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
}

.send-button-gradient {
  background: linear-gradient(135deg, #25D366 0%, #20b358 100%);
}

.send-button-gradient:hover {
  background: linear-gradient(135deg, #20b358 0%, #1da851 100%);
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

.button-scale-hover:hover {
  transform: scale(1.05);
}

.button-scale-active:active {
  transform: scale(0.95);
}

.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(37, 211, 102, 0.5);
}

.tooltip {
  position: relative;
}

.tooltip::before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s;
  margin-bottom: 4px;
}

.tooltip:hover::before {
  opacity: 1;
}

.recording-interface-white {
  background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
  border: 1px solid #e5e7eb;
}

.recording-timer-white {
  color: #059669;
  font-weight: 600;
}

.recording-wave-white {
  background: #10b981;
}

@media (prefers-color-scheme: dark) {
  .mic-button-hover:hover {
    background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
  }

  .tooltip::before {
    background: rgba(255, 255, 255, 0.9);
    color: black;
  }

  .recording-interface-white {
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
    border: 1px solid #4b5563;
  }

  .recording-timer-white {
    color: #34d399;
  }

  .recording-wave-white {
    background: #34d399;
  }
}

@media (max-width: 768px) {
  .send-button-mobile {
    width: 36px;
    height: 36px;
  }
  
  .send-button-mobile i {
    font-size: 16px;
  }
  
  .send-button-mobile .h-5 {
    height: 16px;
    width: 16px;
  }
}

@media (prefers-contrast: high) {
  .send-button-gradient {
    background: #25D366;
    border: 2px solid #000;
  }
  
  .mic-button-hover {
    border: 2px solid #666;
  }
}

@media (prefers-reduced-motion: reduce) {
  .animate-voiceWave,
  .send-button-pulse,
  .loading-spinner,
  .button-scale-hover,
  .button-scale-active,
  .send-button-transition {
    animation: none;
    transition: none;
  }
}
