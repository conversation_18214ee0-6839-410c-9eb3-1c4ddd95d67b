[{"key": "admin.languages.cannot_delete_default_desc", "value": "Please set another language as default first."}, {"key": "admin.languages.cannot_delete_default_title", "value": "Cannot delete default language"}, {"key": "admin.languages.confirm_delete", "value": "Are you sure you want to delete {{name}}?"}, {"key": "admin.languages.default_updated_desc", "value": "The default language has been updated successfully."}, {"key": "admin.languages.default_updated_title", "value": "Default language updated"}, {"key": "admin.languages.deleted_desc", "value": "The language has been deleted successfully."}, {"key": "admin.languages.deleted_title", "value": "Language deleted"}, {"key": "admin.settings.updates", "value": "System Updates"}, {"key": "settings.updates.title", "value": "System Updates"}, {"key": "settings.updates.current_version", "value": "Current Version"}, {"key": "settings.updates.checking", "value": "Checking..."}, {"key": "settings.updates.check_updates", "value": "Check for Updates"}, {"key": "settings.updates.update_available", "value": "Update Available: v{{version}}"}, {"key": "settings.updates.package_size", "value": "Package Size: {{size}}"}, {"key": "settings.updates.release_notes", "value": "Release Notes:"}, {"key": "settings.updates.starting", "value": "Starting..."}, {"key": "settings.updates.install_update", "value": "Install Update"}, {"key": "settings.updates.up_to_date", "value": "Your system is up to date"}, {"key": "settings.updates.update_progress", "value": "Update Progress"}, {"key": "settings.updates.preparing", "value": "Preparing update..."}, {"key": "settings.updates.update_warning", "value": "Do not close this page or restart the system during the update process."}, {"key": "settings.updates.history", "value": "Update History"}, {"key": "settings.updates.no_history", "value": "No update history available"}, {"key": "settings.updates.update_started", "value": "Update Started"}, {"key": "settings.updates.update_started_desc", "value": "System update has been initiated"}, {"key": "settings.updates.update_failed", "value": "Failed to start update"}, {"key": "settings.updates.status.pending", "value": "Pending"}, {"key": "settings.updates.status.downloading", "value": "Downloading"}, {"key": "settings.updates.status.validating", "value": "Validating"}, {"key": "settings.updates.status.applying", "value": "Applying"}, {"key": "settings.updates.status.completed", "value": "Completed"}, {"key": "settings.updates.status.failed", "value": "Failed"}, {"key": "settings.updates.status.rolled_back", "value": "Rolled Back"}, {"key": "trial.status_title", "value": "Trial Period"}, {"key": "trial.days_remaining", "value": "{{days}} days remaining"}, {"key": "trial.expires_today", "value": "Expires today"}, {"key": "trial.expired", "value": "Trial expired"}, {"key": "trial.upgrade_now", "value": "Upgrade now"}, {"key": "plans.trial_days", "value": "Trial Days"}, {"key": "plans.trial_period", "value": "Trial Period"}, {"key": "plans.has_trial", "value": "Has Trial"}, {"key": "plans.no_trial", "value": "No Trial"}, {"key": "plans.free_plan", "value": "Free Plan"}, {"key": "plans.free", "value": "Free"}, {"key": "plans.paid", "value": "Paid"}, {"key": "registration.trial_available", "value": "{{days}} day trial available"}, {"key": "registration.free_plan_available", "value": "Free plan"}, {"key": "admin.languages.description", "value": "Manage the languages available in your application. The default language will be used when a user's preferred language is not available."}, {"key": "admin.languages.table.actions", "value": "Actions"}, {"key": "admin.languages.table.active", "value": "Active"}, {"key": "admin.languages.table.code", "value": "Code"}, {"key": "admin.languages.table.default", "value": "<PERSON><PERSON><PERSON>"}, {"key": "admin.languages.table.direction", "value": "Direction"}, {"key": "admin.languages.table.language", "value": "Language"}, {"key": "admin.languages.title", "value": "Available Languages"}, {"key": "admin.languages.updated_desc", "value": "The language has been updated successfully."}, {"key": "admin.languages.updated_title", "value": "Language updated"}, {"key": "admin.namespaces.confirm_delete", "value": "Are you sure you want to delete the \"{{name}}\" namespace? This will delete all keys and translations in this namespace."}, {"key": "admin.namespaces.deleted_desc", "value": "The namespace has been deleted successfully."}, {"key": "admin.namespaces.deleted_title", "value": "Namespace deleted"}, {"key": "admin.namespaces.description", "value": "Namespaces help organize translations into logical groups."}, {"key": "admin.namespaces.edit_description", "value": "Update the namespace settings."}, {"key": "admin.namespaces.edit_title", "value": "Edit Namespace"}, {"key": "admin.namespaces.form.description", "value": "Description"}, {"key": "admin.namespaces.form.description_placeholder", "value": "Common translations used throughout the application"}, {"key": "admin.namespaces.form.name", "value": "Name"}, {"key": "admin.namespaces.form.name_placeholder", "value": "common"}, {"key": "admin.namespaces.table.actions", "value": "Actions"}, {"key": "admin.namespaces.table.description", "value": "Description"}, {"key": "admin.namespaces.table.name", "value": "Name"}, {"key": "admin.namespaces.title", "value": "Translation Namespaces"}, {"key": "admin.namespaces.update_button", "value": "Update Namespace"}, {"key": "admin.namespaces.updated_desc", "value": "The namespace has been updated successfully."}, {"key": "admin.namespaces.updated_title", "value": "Namespace updated"}, {"key": "admin.translations.description", "value": "Select a language and namespace to manage translations."}, {"key": "admin.translations.language_label", "value": "Language"}, {"key": "admin.translations.namespace_label", "value": "Namespace"}, {"key": "admin.translations.no_keys", "value": "No translation keys found in this namespace. Add a new key to get started."}, {"key": "admin.translations.no_translation", "value": "No translation"}, {"key": "admin.translations.saved_desc", "value": "The translation has been saved successfully."}, {"key": "admin.translations.saved_title", "value": "Translation saved"}, {"key": "admin.translations.select_both", "value": "Please select both a language and a namespace to manage translations."}, {"key": "admin.translations.select_language", "value": "Select language"}, {"key": "admin.translations.select_namespace", "value": "Select namespace"}, {"key": "admin.translations.table.actions", "value": "Actions"}, {"key": "admin.translations.table.key", "value": "Key"}, {"key": "admin.translations.table.translation", "value": "Translation"}, {"key": "admin.translations.title", "value": "Manage Translations"}, {"key": "common.edit", "value": "Edit"}, {"key": "common.error", "value": "Error"}, {"key": "common.updating", "value": "Updating..."}, {"key": "common.search", "value": "Search"}, {"key": "common.searching", "value": "Searching..."}, {"key": "common.search_placeholder", "value": "Search conversations, contacts, templates..."}, {"key": "search.no_results", "value": "No results found for \"{{query}}\""}, {"key": "search.conversations", "value": "Conversations"}, {"key": "search.contacts", "value": "Contacts"}, {"key": "search.templates", "value": "Templates"}, {"key": "emoji.picker_title", "value": "Select Emoji"}, {"key": "emoji.recent", "value": "Recently Used"}, {"key": "emoji.search_placeholder", "value": "Search emojis..."}, {"key": "messages.input.add_emoji", "value": "Add emoji"}, {"key": "quoted_message.unknown_sender", "value": "Unknown"}, {"key": "quoted_message.you", "value": "You"}, {"key": "quoted_message.assistant", "value": "Assistant"}, {"key": "quoted_message.contact", "value": "Contact"}, {"key": "quoted_message.image", "value": "📷 Image"}, {"key": "quoted_message.video", "value": "🎥 Video"}, {"key": "quoted_message.audio", "value": "🎵 Audio"}, {"key": "quoted_message.document", "value": "📄 Document"}, {"key": "quoted_message.message", "value": "Message"}, {"key": "quoted_message.deleted_sender", "value": "Unknown"}, {"key": "quoted_message.deleted_message", "value": "This message was deleted"}, {"key": "message_bubble.confirm_delete_whatsapp_title", "value": "Delete Message for Everyone"}, {"key": "message_bubble.confirm_delete_whatsapp_message", "value": "This message will be deleted from both {{appName}} and the recipient's WhatsApp chat. This action cannot be undone."}, {"key": "message_bubble.confirm_delete_whatsapp_old", "value": "This message is too old to be deleted from WhatsApp (72-minute limit). It will only be deleted from {{appName}}."}, {"key": "welcome.message", "value": "welcome to pointer"}, {"key": "admin.payments.title", "value": "Payment Management"}, {"key": "admin.payments.description", "value": "Comprehensive payment tracking and management dashboard"}, {"key": "admin.payments.dashboard.title", "value": "Payment Dashboard"}, {"key": "admin.payments.metrics.total_revenue", "value": "Total Revenue"}, {"key": "admin.payments.metrics.monthly_revenue", "value": "Monthly Revenue"}, {"key": "admin.payments.metrics.yearly_revenue", "value": "Yearly Revenue"}, {"key": "admin.payments.metrics.monthly_growth", "value": "Monthly Growth"}, {"key": "admin.payments.metrics.active_subscriptions", "value": "Active Subscriptions"}, {"key": "admin.payments.metrics.pending_payments", "value": "Pending Payments"}, {"key": "admin.payments.metrics.payment_success_rate", "value": "Payment Success Rate"}, {"key": "admin.payments.trends.title", "value": "Payment Trends"}, {"key": "admin.payments.trends.revenue", "value": "Revenue"}, {"key": "admin.payments.trends.transactions", "value": "Transactions"}, {"key": "admin.payments.trends.period.7days", "value": "Last 7 Days"}, {"key": "admin.payments.trends.period.30days", "value": "Last 30 Days"}, {"key": "admin.payments.trends.period.12months", "value": "Last 12 Months"}, {"key": "admin.payments.companies.title", "value": "Company Payment Details"}, {"key": "admin.payments.companies.search_placeholder", "value": "Search companies..."}, {"key": "admin.payments.companies.table.company", "value": "Company"}, {"key": "admin.payments.companies.table.plan", "value": "Plan"}, {"key": "admin.payments.companies.table.status", "value": "Status"}, {"key": "admin.payments.companies.table.last_payment", "value": "Last Payment"}, {"key": "admin.payments.companies.table.next_renewal", "value": "Next Renewal"}, {"key": "admin.payments.companies.table.payment_method", "value": "Payment Method"}, {"key": "admin.payments.companies.table.total_paid", "value": "Total Paid"}, {"key": "admin.payments.companies.table.actions", "value": "Actions"}, {"key": "admin.payments.companies.status.active", "value": "Active"}, {"key": "admin.payments.companies.status.pending", "value": "Pending"}, {"key": "admin.payments.companies.status.overdue", "value": "Overdue"}, {"key": "admin.payments.companies.status.cancelled", "value": "Cancelled"}, {"key": "admin.payments.transactions.title", "value": "Payment Transactions"}, {"key": "admin.payments.transactions.filter.all_methods", "value": "All Payment Methods"}, {"key": "admin.payments.transactions.filter.all_statuses", "value": "All Statuses"}, {"key": "admin.payments.transactions.filter.date_range", "value": "Date Range"}, {"key": "admin.payments.transactions.table.id", "value": "Transaction ID"}, {"key": "admin.payments.transactions.table.company", "value": "Company"}, {"key": "admin.payments.transactions.table.plan", "value": "Plan"}, {"key": "admin.payments.transactions.table.amount", "value": "Amount"}, {"key": "admin.payments.transactions.table.method", "value": "Method"}, {"key": "admin.payments.transactions.table.status", "value": "Status"}, {"key": "admin.payments.transactions.table.date", "value": "Date"}, {"key": "admin.payments.transactions.table.actions", "value": "Actions"}, {"key": "admin.payments.transactions.status.completed", "value": "Completed"}, {"key": "admin.payments.transactions.status.pending", "value": "Pending"}, {"key": "admin.payments.transactions.status.failed", "value": "Failed"}, {"key": "admin.payments.transactions.status.cancelled", "value": "Cancelled"}, {"key": "admin.payments.pending.title", "value": "Pending Payments"}, {"key": "admin.payments.pending.description", "value": "Manage overdue and pending payments"}, {"key": "admin.payments.pending.table.company", "value": "Company"}, {"key": "admin.payments.pending.table.plan", "value": "Plan"}, {"key": "admin.payments.pending.table.amount", "value": "Amount"}, {"key": "admin.payments.pending.table.days_overdue", "value": "Days Overdue"}, {"key": "admin.payments.pending.table.method", "value": "Method"}, {"key": "admin.payments.pending.table.actions", "value": "Actions"}, {"key": "admin.payments.actions.mark_received", "value": "<PERSON> as Received"}, {"key": "admin.payments.actions.send_reminder", "value": "Send Reminder"}, {"key": "admin.payments.actions.view_details", "value": "View Details"}, {"key": "admin.payments.performance.title", "value": "Payment Method Performance"}, {"key": "admin.payments.performance.table.method", "value": "Payment Method"}, {"key": "admin.payments.performance.table.transactions", "value": "Total Transactions"}, {"key": "admin.payments.performance.table.success_rate", "value": "Success Rate"}, {"key": "admin.payments.performance.table.revenue", "value": "Total Revenue"}, {"key": "admin.payments.performance.table.avg_amount", "value": "Average Amount"}, {"key": "admin.payments.export.title", "value": "Export Payment Data"}, {"key": "admin.payments.export.format.csv", "value": "CSV"}, {"key": "admin.payments.export.format.json", "value": "JSON"}, {"key": "admin.payments.export.button", "value": "Export Data"}, {"key": "admin.payments.reminders.title", "value": "Send Payment Reminder"}, {"key": "admin.payments.reminders.message_placeholder", "value": "Enter reminder message..."}, {"key": "admin.payments.reminders.send_button", "value": "Send Reminder"}, {"key": "admin.payments.reminders.success", "value": "<PERSON><PERSON><PERSON> sent successfully"}, {"key": "admin.payments.status_update.title", "value": "Update Payment Status"}, {"key": "admin.payments.status_update.notes_placeholder", "value": "Add notes (optional)..."}, {"key": "admin.payments.status_update.success", "value": "Payment status updated successfully"}, {"key": "common.save", "value": "Save"}, {"key": "common.close", "value": "Close"}, {"key": "common.hide", "value": "<PERSON>de"}, {"key": "common.something_went_wrong", "value": "Something went wrong"}, {"key": "flow_builder.send_message", "value": "Send Message"}, {"key": "flow_builder.enter_message", "value": "Enter your message..."}, {"key": "flow_builder.condition", "value": "Condition"}, {"key": "flow_builder.condition_example", "value": "if message.contains('hello')"}, {"key": "flow_builder.yes", "value": "Yes"}, {"key": "flow_builder.no", "value": "No"}, {"key": "flow_builder.input", "value": "Input"}, {"key": "flow_builder.collect_response", "value": "Collect user response"}, {"key": "flow_builder.action", "value": "Action"}, {"key": "flow_builder.perform_api_call", "value": "Perform API call"}, {"key": "flow_builder.add_node", "value": "Add Node"}, {"key": "flow_builder.message", "value": "Message"}, {"key": "flow_builder.error_loading_flow", "value": "Error loading flow"}, {"key": "flow_builder.parse_error", "value": "Could not parse flow data"}, {"key": "flow_builder.flow_created", "value": "Flow created"}, {"key": "flow_builder.flow_created_success", "value": "Your flow has been created successfully."}, {"key": "flow_builder.plan_limit_reached", "value": "Plan Limit Reached"}, {"key": "flow_builder.upgrade_plan_message", "value": "To create more flows, please contact your administrator to upgrade your plan."}, {"key": "flow_builder.error_creating_flow", "value": "Error creating flow"}, {"key": "flow_builder.flow_updated", "value": "Flow updated"}, {"key": "flow_builder.flow_updated_success", "value": "Your flow has been updated successfully."}, {"key": "flow_builder.error_updating_flow", "value": "Error updating flow"}, {"key": "flow_builder.name_required", "value": "Name required"}, {"key": "flow_builder.provide_name", "value": "Please provide a name for your flow"}, {"key": "flow_builder.flow_name_placeholder", "value": "Flow name"}, {"key": "flow_builder.active", "value": "Active"}, {"key": "flow_builder.draft", "value": "Draft"}, {"key": "flow_builder.creating_new_flow", "value": "Creating New Flow"}, {"key": "flow_builder.current_flow_status", "value": "Current flow status"}, {"key": "flow_builder.ai_assistant", "value": "AI Assistant"}, {"key": "flow_builder.ai_history", "value": "History"}, {"key": "flow_builder.ai_messages", "value": "messages"}, {"key": "flow_builder.ai_audio_enabled", "value": "Audio enabled"}, {"key": "flow_builder.ai_image_enabled", "value": "Image enabled"}, {"key": "flow_builder.ai_video_enabled", "value": "Video enabled"}, {"key": "flow_builder.ai_functions_enabled", "value": "Functions enabled"}, {"key": "flow_builder.ai_provider", "value": "AI Provider"}, {"key": "flow_builder.ai_select_provider", "value": "Select provider"}, {"key": "flow_builder.ai_model", "value": "Model"}, {"key": "flow_builder.ai_select_model", "value": "Select model"}, {"key": "flow_builder.ai_api_key", "value": "API Key"}, {"key": "flow_builder.ai_api_key_placeholder", "value": "Enter your {{provider}} API key"}, {"key": "flow_builder.ai_get_api_key", "value": "Get your API key here"}, {"key": "flow_builder.ai_system_prompt", "value": "Assistant Mode (System Prompt)"}, {"key": "flow_builder.ai_prompt_placeholder", "value": "Enter instructions for the AI"}, {"key": "flow_builder.ai_enable_history", "value": "Enable conversation history"}, {"key": "flow_builder.ai_history_limit", "value": "History messages limit"}, {"key": "flow_builder.ai_history_description", "value": "Sets how many previous messages to include for context."}, {"key": "flow_builder.ai_enable_audio", "value": "Enable audio processing"}, {"key": "flow_builder.ai_enable_image", "value": "Enable image processing"}, {"key": "flow_builder.ai_image_tooltip", "value": "Allows the AI to process images sent by the user. Note: Only available with multimodal models like Gemini 2.5."}, {"key": "flow_builder.ai_enable_video", "value": "Enable video processing"}, {"key": "flow_builder.ai_video_tooltip", "value": "Allows the AI to process video files sent by the user. Note: Only available with multimodal models like Gemini 2.5."}, {"key": "flow_builder.ai_enable_functions", "value": "Enable function calling"}, {"key": "flow_builder.ai_functions_tooltip", "value": "Only available with certain providers and models. Allows the AI to perform actions like scheduling appointments, checking data, etc."}, {"key": "flow_builder.ai_enable_task_execution", "value": "Enable Task Execution"}, {"key": "flow_builder.ai_task_execution_tooltip", "value": "Allows the AI to execute predefined tasks and trigger different flow paths based on user intent. Each task can connect to different nodes in the flow."}, {"key": "flow_builder.ai_tasks", "value": "Tasks"}, {"key": "flow_builder.ai_add_task", "value": "Add Task"}, {"key": "flow_builder.ai_no_tasks", "value": "No tasks configured. Add a task to enable AI function calling."}, {"key": "flow_builder.ai_tasks_description", "value": "Tasks allow the AI to execute specific functions when user intent is detected. Each task creates an output handle that can be connected to other nodes."}, {"key": "flow_builder.ai_tasks_enabled", "value": "Tasks"}, {"key": "flow_builder.ai_default_output", "value": "Default Output"}, {"key": "flow_builder.ai_default_output_desc", "value": "Used when no tasks are triggered or task execution is disabled"}, {"key": "flow_builder.ai_session_stopped", "value": "Session Stopped"}, {"key": "flow_builder.ai_session_stopped_desc", "value": "Triggered when user types the stop keyword to exit AI session"}, {"key": "flow_builder.ai_description", "value": "The AI Assistant node will process incoming messages and generate responses based on the context. It can access conversation history, contact data, and other variables from the flow."}, {"key": "flow_builder.http_request", "value": "HTTP Request"}, {"key": "flow_builder.http_no_url", "value": "No URL configured"}, {"key": "flow_builder.http_auth", "value": "<PERSON><PERSON>"}, {"key": "flow_builder.http_header", "value": "header"}, {"key": "flow_builder.http_body_configured", "value": "Body configured"}, {"key": "flow_builder.http_retry", "value": "Retry"}, {"key": "flow_builder.http_mapping", "value": "mapping"}, {"key": "flow_builder.http_quick_templates", "value": "Quick Templates"}, {"key": "flow_builder.http_choose_template", "value": "Choose a template..."}, {"key": "flow_builder.http_method", "value": "HTTP Method"}, {"key": "flow_builder.http_select_method", "value": "Select method"}, {"key": "flow_builder.http_request_url", "value": "Request URL"}, {"key": "flow_builder.webhook", "value": "Webhook"}, {"key": "flow_builder.webhook_no_url", "value": "No URL configured"}, {"key": "flow_builder.webhook_body_configured", "value": "Body configured"}, {"key": "flow_builder.webhook_method", "value": "HTTP Method"}, {"key": "flow_builder.webhook_select_method", "value": "Select method"}, {"key": "flow_builder.webhook_url", "value": "Webhook URL"}, {"key": "flow_builder.webhook_url_placeholder", "value": "https://api.example.com/webhook"}, {"key": "flow_builder.webhook_test_tooltip", "value": "Test webhook with current configuration"}, {"key": "flow_builder.webhook_authentication", "value": "Authentication"}, {"key": "flow_builder.webhook_select_auth", "value": "Select auth type"}, {"key": "flow_builder.webhook_bearer_token", "value": "Bearer token"}, {"key": "flow_builder.webhook_username", "value": "Username"}, {"key": "flow_builder.webhook_password", "value": "Password"}, {"key": "flow_builder.calendar_event", "value": "Calendar Event"}, {"key": "flow_builder.calendar_invalid_email", "value": "Invalid email"}, {"key": "flow_builder.calendar_valid_email_required", "value": "Please enter a valid email address"}, {"key": "flow_builder.calendar_untitled_event", "value": "Untitled Event"}, {"key": "flow_builder.calendar_start", "value": "Start"}, {"key": "flow_builder.calendar_end", "value": "End"}, {"key": "flow_builder.calendar_attendees", "value": "Attendees"}, {"key": "flow_builder.calendar_event_title", "value": "Event Title"}, {"key": "flow_builder.calendar_enter_title", "value": "Enter event title"}, {"key": "flow_builder.calendar_description", "value": "Description"}, {"key": "flow_builder.calendar_enter_description", "value": "Enter event description"}, {"key": "flow_builder.duplicate_node", "value": "Duplicate node"}, {"key": "flow_builder.delete_node", "value": "Delete node"}, {"key": "flow_builder.hard_reset_keyword", "value": "Hard Reset Keyword"}, {"key": "flow_builder.hard_reset_keyword_placeholder", "value": "reset, restart, newchat, etc."}, {"key": "flow_builder.hard_reset_keyword_help", "value": "When bot is disabled, users can type this keyword to re-enable the bot and start fresh"}, {"key": "flow_builder.hard_reset_confirmation_message", "value": "Reset Confirmation Message"}, {"key": "flow_builder.hard_reset_confirmation_placeholder", "value": "<PERSON><PERSON> has been reactivated. Starting fresh conversation..."}, {"key": "flow_builder.hard_reset_confirmation_help", "value": "Message sent to user when hard reset is triggered"}, {"key": "flow_builder.hard_reset_label", "value": "Hard Reset"}, {"key": "admin.translations.select_both", "value": "Please select both a language and a namespace to manage translations."}, {"key": "errors.404_title", "value": "404 Page Not Found"}, {"key": "errors.404_description", "value": "Did you forget to add the page to the router?"}, {"key": "auth.welcome_team", "value": "Welcome to the team!"}, {"key": "auth.account_created_success", "value": "Your account has been created successfully. You are now logged in."}, {"key": "auth.validation.username_min_length", "value": "Username must be at least 3 characters long"}, {"key": "auth.validation.password_min_length", "value": "Password must be at least 6 characters long"}, {"key": "auth.validation.passwords_no_match", "value": "Passwords do not match"}, {"key": "auth.validation.full_name_required", "value": "Full name is required"}, {"key": "auth.invalid_invitation_title", "value": "Invalid Invitation Link"}, {"key": "auth.invalid_invitation_desc", "value": "The invitation link is missing or invalid. Please check your email and try again."}, {"key": "auth.go_to_login", "value": "Go to Login"}, {"key": "auth.verifying_invitation", "value": "Verifying invitation..."}, {"key": "auth.invitation_not_found_title", "value": "Invitation Not Found"}, {"key": "auth.invitation_not_found_desc", "value": "This invitation link is invalid, expired, or has already been used."}, {"key": "auth.accept_invitation_title", "value": "Accept Team Invitation"}, {"key": "auth.invited_as_role", "value": "You've been invited to join as a {{role}}"}, {"key": "auth.full_name", "value": "Full Name"}, {"key": "auth.enter_full_name", "value": "Enter your full name"}, {"key": "auth.username", "value": "Username"}, {"key": "auth.choose_username", "value": "Choose a username"}, {"key": "auth.password", "value": "Password"}, {"key": "auth.create_password", "value": "Create a password"}, {"key": "auth.confirm_password", "value": "Confirm Password"}, {"key": "auth.confirm_password_placeholder", "value": "Confirm your password"}, {"key": "auth.creating_account", "value": "Creating Account..."}, {"key": "auth.accept_invitation_button", "value": "Accept Invitation & Create Account"}, {"key": "auth.already_have_account", "value": "Already have an account?"}, {"key": "auth.sign_in_here", "value": "Sign in here"}, {"key": "auth.invitation_agreement", "value": "By accepting this invitation, you agree to join the team and follow the company's policies."}, {"key": "auth.login_success", "value": "Login successful"}, {"key": "auth.welcome_back", "value": "Welcome back, {{name}}!"}, {"key": "auth.login_failed", "value": "<PERSON><PERSON> failed"}, {"key": "auth.invalid_credentials", "value": "Invalid username or password"}, {"key": "auth.email", "value": "Email"}, {"key": "auth.email_placeholder", "value": "<EMAIL>"}, {"key": "auth.password_placeholder", "value": "â€¢â€¢â€¢â€¢â€¢â€¢â€¢â€¢"}, {"key": "auth.logging_in", "value": "Logging in..."}, {"key": "auth.login", "value": "<PERSON><PERSON>"}, {"key": "auth.welcome_title", "value": "Welcome to {{appName}}"}, {"key": "auth.welcome_subtitle", "value": "Multi-channel team inbox and AI chatbot platform"}, {"key": "auth.login_title", "value": "Login to your account"}, {"key": "auth.login_description", "value": "Enter your username and password to access your account"}, {"key": "auth.username_placeholder", "value": "Your username"}, {"key": "auth.register_company", "value": "Register a new company"}, {"key": "auth.hero_title", "value": "Unify Your Customer Communications"}, {"key": "auth.hero_subtitle", "value": "The all-in-one platform for managing customer conversations across WhatsApp, Facebook, Instagram, and more."}, {"key": "auth.feature_inbox_title", "value": "Multi-channel inbox"}, {"key": "auth.feature_inbox_desc", "value": "Manage all customer conversations in one unified inbox"}, {"key": "auth.feature_ai_title", "value": "AI-powered chatbots"}, {"key": "auth.feature_ai_desc", "value": "Automate responses with customizable AI chatbots"}, {"key": "auth.feature_team_title", "value": "Team collaboration"}, {"key": "auth.feature_team_desc", "value": "Assign conversations, leave notes, and collaborate effectively"}, {"key": "admin.login_title", "value": "<PERSON><PERSON>"}, {"key": "admin.login_description", "value": "Enter your credentials to access the admin panel"}, {"key": "admin.restricted_area", "value": "This area is restricted to administrators only"}, {"key": "nav.dashboard", "value": "Dashboard"}, {"key": "dashboard.total_conversations", "value": "Total Conversations"}, {"key": "dashboard.active_conversations", "value": "Active Conversations"}, {"key": "dashboard.response_time", "value": "Response Time"}, {"key": "nav.inbox", "value": "Inbox"}, {"key": "nav.flow_builder", "value": "Flow Builder"}, {"key": "nav.contacts", "value": "Contacts"}, {"key": "nav.pipeline", "value": "Pipeline"}, {"key": "nav.calendar", "value": "Calendar"}, {"key": "nav.campaigns", "value": "Campaigns"}, {"key": "nav.analytics", "value": "Analytics"}, {"key": "nav.channels", "value": "Channels"}, {"key": "nav.settings", "value": "Settings"}, {"key": "nav.help_support", "value": "Help & Support"}, {"key": "nav.company", "value": "Company"}, {"key": "nav.plan", "value": "Plan"}, {"key": "nav.profile", "value": "Profile"}, {"key": "admin.nav.dashboard", "value": "Dashboard"}, {"key": "admin.nav.companies", "value": "Companies"}, {"key": "admin.nav.users", "value": "Users"}, {"key": "admin.nav.plans", "value": "Plans"}, {"key": "admin.nav.analytics", "value": "Analytics"}, {"key": "admin.nav.translations", "value": "Translations"}, {"key": "admin.nav.settings", "value": "Settings"}, {"key": "admin.companies.new_company", "value": "New Company"}, {"key": "admin.companies.total_companies", "value": "Total Companies"}, {"key": "admin.companies.active_companies", "value": "Active Companies"}, {"key": "admin.companies.inactive_companies", "value": "Inactive Companies"}, {"key": "admin.companies.manage_description", "value": "Manage all companies in the system"}, {"key": "admin.companies.no_companies_found", "value": "No companies found. Create your first company to get started."}, {"key": "admin.companies.table.name", "value": "Name"}, {"key": "admin.companies.table.slug", "value": "Slug"}, {"key": "admin.companies.table.plan", "value": "Plan"}, {"key": "admin.companies.table.status", "value": "Status"}, {"key": "admin.companies.table.actions", "value": "Actions"}, {"key": "common.active", "value": "Active"}, {"key": "common.inactive", "value": "Inactive"}, {"key": "common.manage", "value": "Manage"}, {"key": "common.please_wait", "value": "Please wait..."}, {"key": "common.search", "value": "Search"}, {"key": "common.searching_for", "value": "Searching for: {{query}}"}, {"key": "common.search_placeholder", "value": "Search conversations, templates..."}, {"key": "admin.returning_to_admin", "value": "Returning to admin account"}, {"key": "admin.error_returning", "value": "Error returning to admin"}, {"key": "admin.trying_fallback", "value": "Trying fallback method..."}, {"key": "admin.returning", "value": "Returning..."}, {"key": "admin.return_to_admin", "value": "Return to Admin"}, {"key": "admin.impersonating", "value": "Impersonating"}, {"key": "auth.logged_out", "value": "Logged out"}, {"key": "auth.logged_out_success", "value": "You have been successfully logged out"}, {"key": "auth.logout_failed", "value": "Failed to logout: {{error}}"}, {"key": "auth.back_to_login", "value": "Back to Login"}, {"key": "registration.success_title", "value": "Registration successful!"}, {"key": "registration.approval_required", "value": "Your company registration has been submitted for approval. You will receive an email once approved."}, {"key": "registration.success_desc", "value": "Your company has been registered successfully. You can now log in."}, {"key": "registration.failed_title", "value": "Registration failed"}, {"key": "registration.unavailable_title", "value": "Registration Unavailable"}, {"key": "registration.unavailable_desc", "value": "Company registration is currently disabled. Please contact the administrator for more information."}, {"key": "registration.title", "value": "Company Registration"}, {"key": "registration.description", "value": "Fill in the details below to register your company and create an admin account"}, {"key": "registration.approval_notice", "value": "New registrations require admin approval. You will receive an email once your registration is approved."}, {"key": "registration.company_info", "value": "Company Information"}, {"key": "registration.company_name", "value": "Company Name"}, {"key": "registration.company_name_placeholder", "value": "Your Company Name"}, {"key": "registration.admin_details", "value": "Admin User Details"}, {"key": "registration.plan_selection", "value": "Plan Selection"}, {"key": "registration.registering", "value": "Registering..."}, {"key": "registration.register_button", "value": "Register Company"}, {"key": "registration.page_title", "value": "Register Your Company"}, {"key": "registration.page_subtitle", "value": "Create your company account and start managing conversations"}, {"key": "registration.company_slug", "value": "Company Slug"}, {"key": "registration.company_slug_placeholder", "value": "your-company"}, {"key": "registration.company_slug_description", "value": "This will be your unique identifier (e.g., your-company.app.com)"}, {"key": "registration.company_email", "value": "Company Email"}, {"key": "registration.company_email_placeholder", "value": "<EMAIL>"}, {"key": "registration.company_phone", "value": "Company Phone"}, {"key": "registration.company_phone_placeholder", "value": "+****************"}, {"key": "registration.company_website", "value": "Company Website"}, {"key": "registration.company_website_placeholder", "value": "https://yourcompany.com"}, {"key": "registration.admin_full_name", "value": "Full Name"}, {"key": "registration.admin_full_name_placeholder", "value": "<PERSON>"}, {"key": "registration.admin_email", "value": "Email Address"}, {"key": "registration.admin_email_placeholder", "value": "<EMAIL>"}, {"key": "registration.admin_username", "value": "Username"}, {"key": "registration.admin_username_placeholder", "value": "johndoe"}, {"key": "registration.admin_username_description", "value": "This will be used to log in to your account"}, {"key": "registration.admin_password", "value": "Password"}, {"key": "registration.admin_password_placeholder", "value": "Create a strong password"}, {"key": "registration.admin_confirm_password", "value": "Confirm Password"}, {"key": "registration.admin_confirm_password_placeholder", "value": "Confirm your password"}, {"key": "registration.select_plan", "value": "Select Plan"}, {"key": "registration.select_plan_placeholder", "value": "Choose a plan"}, {"key": "registration.plan_change_note", "value": "You can change your plan later from the settings"}, {"key": "registration.validation.company_name_min", "value": "Company name must be at least 2 characters"}, {"key": "registration.validation.company_slug_min", "value": "Company slug must be at least 3 characters"}, {"key": "registration.validation.company_slug_format", "value": "Slug can only contain lowercase letters, numbers, and hyphens"}, {"key": "registration.validation.company_email_invalid", "value": "Please enter a valid company email address"}, {"key": "registration.validation.company_website_invalid", "value": "Please enter a valid website URL"}, {"key": "registration.validation.admin_name_min", "value": "Full name must be at least 2 characters"}, {"key": "registration.validation.admin_email_invalid", "value": "Please enter a valid email address"}, {"key": "registration.validation.admin_username_min", "value": "Username must be at least 3 characters"}, {"key": "registration.validation.admin_password_min", "value": "Password must be at least 6 characters"}, {"key": "registration.validation.confirm_password_required", "value": "Please confirm your password"}, {"key": "registration.validation.passwords_no_match", "value": "Passwords don't match"}, {"key": "registration.validation.plan_required", "value": "Please select a plan"}, {"key": "registration.validation.slug_taken", "value": "This slug is already taken"}, {"key": "registration.error.status_check_failed", "value": "Failed to check registration status"}, {"key": "registration.error.plans_fetch_failed", "value": "Failed to fetch plans"}, {"key": "registration.error.slug_check_failed", "value": "Failed to check slug availability"}, {"key": "registration.error.register_failed", "value": "Failed to register company"}, {"key": "flow_builder.error.no_file", "value": "No file selected"}, {"key": "flow_builder.error.no_file_desc", "value": "Please select a file to upload"}, {"key": "flow_builder.error.invalid_file_type", "value": "Invalid file type"}, {"key": "flow_builder.error.invalid_image_type", "value": "Please select a valid image file (JPEG, PNG, GIF, WebP, SVG)"}, {"key": "flow_builder.error.file_too_large", "value": "File too large"}, {"key": "flow_builder.error.max_file_size", "value": "Maximum file size is 30MB"}, {"key": "flow_builder.error.upload_failed", "value": "Upload failed"}, {"key": "flow_builder.error.upload_error", "value": "An error occurred while uploading the file"}, {"key": "flow_builder.success.upload_complete", "value": "Upload complete"}, {"key": "flow_builder.success.image_uploaded", "value": "Image uploaded successfully"}, {"key": "flow_builder.success.image_removed", "value": "Image removed"}, {"key": "flow_builder.success.image_removed_desc", "value": "Image has been removed from the node"}, {"key": "flow_builder.remove_image", "value": "Remove Image"}, {"key": "flow_builder.preview.loading_audio", "value": "Loading audio..."}, {"key": "flow_builder.preview.loading_video", "value": "Loading video..."}, {"key": "flow_builder.preview.loading_image", "value": "Loading image..."}, {"key": "flow_builder.preview.failed_audio", "value": "Failed to load audio"}, {"key": "flow_builder.preview.failed_video", "value": "Failed to load video"}, {"key": "flow_builder.preview.failed_image", "value": "Failed to load image"}, {"key": "flow_builder.preview.duration", "value": "Duration"}, {"key": "flow_builder.preview.resolution", "value": "Resolution"}, {"key": "flow_builder.preview.click_to_download", "value": "Click to download/view"}, {"key": "flow_builder.preview.open_document", "value": "Open"}, {"key": "flow_builder.preview_audio", "value": "Preview Audio"}, {"key": "flow_builder.preview_video", "value": "Preview Video"}, {"key": "flow_builder.preview_document", "value": "Preview Document"}, {"key": "flow_builder.hide_preview", "value": "Hide Preview"}, {"key": "inbox.conversations", "value": "Conversations"}, {"key": "inbox.filtered_by_channel", "value": "Filtered by channel"}, {"key": "inbox.clear_channel_filter", "value": "Clear channel filter"}, {"key": "inbox.filter_conversations", "value": "Filter conversations"}, {"key": "inbox.start_new_conversation", "value": "Start new conversation"}, {"key": "inbox.search_conversations", "value": "Search conversations"}, {"key": "inbox.search_conversations_enhanced", "value": "Search by name, tag, phone, email..."}, {"key": "inbox.filter.all", "value": "All"}, {"key": "inbox.filter.unassigned", "value": "Unassigned"}, {"key": "inbox.filter.my_chats", "value": "My Chats"}, {"key": "inbox.filter.assigned", "value": "Assigned"}, {"key": "inbox.no_conversations_found", "value": "No conversations found"}, {"key": "inbox.no_conversation_selected", "value": "No Conversation Selected"}, {"key": "inbox.select_conversation_hint", "value": "Select a conversation from the list to view messages"}, {"key": "inbox.show_conversations", "value": "Show conversations"}, {"key": "inbox.close_conversations", "value": "Close conversations"}, {"key": "inbox.new_lead", "value": "New Lead"}, {"key": "inbox.last_active_time", "value": "Last active 10 min ago"}, {"key": "inbox.conversation_started_via", "value": "Conversation started via {{channel}}"}, {"key": "inbox.scroll_to_bottom", "value": "Scroll to bottom"}, {"key": "inbox.new_conversation", "value": "New Conversation"}, {"key": "inbox.not_connected", "value": "Not Connected"}, {"key": "inbox.cannot_send_message", "value": "Cannot send message, not connected to server"}, {"key": "inbox.failed_send_media", "value": "Failed to send media message"}, {"key": "contacts.redirecting_to_inbox", "value": "Redirecting to inbox"}, {"key": "contacts.opening_conversation_with", "value": "Opening conversation with {{name}}"}, {"key": "contacts.add_contact", "value": "Add Contact"}, {"key": "contacts.search_placeholder", "value": "Search contacts..."}, {"key": "contacts.all_channels", "value": "All Channels"}, {"key": "contacts.whatsapp_official", "value": "WhatsApp Official"}, {"key": "contacts.whatsapp_unofficial", "value": "WhatsApp Unofficial"}, {"key": "contacts.add.title", "value": "Add New Contact"}, {"key": "contacts.add.description", "value": "Create a new contact with the information below."}, {"key": "contacts.add.name_label", "value": "Name"}, {"key": "contacts.add.name_placeholder", "value": "Enter contact name"}, {"key": "contacts.add.name_required", "value": "Contact name is required"}, {"key": "contacts.add.email_label", "value": "Email"}, {"key": "contacts.add.email_placeholder", "value": "Enter email address"}, {"key": "contacts.add.phone_label", "value": "Phone"}, {"key": "contacts.add.phone_placeholder", "value": "Enter phone number"}, {"key": "contacts.add.company_label", "value": "Company"}, {"key": "contacts.add.company_placeholder", "value": "Enter company name"}, {"key": "contacts.add.channel_label", "value": "Channel"}, {"key": "contacts.add.select_channel_placeholder", "value": "Select channel"}, {"key": "contacts.add.channel.whatsapp_official", "value": "WhatsApp Official"}, {"key": "contacts.add.channel.whatsapp_unofficial", "value": "WhatsApp Unofficial"}, {"key": "contacts.add.channel.messenger", "value": "Facebook Messenger"}, {"key": "contacts.add.channel.instagram", "value": "Instagram"}, {"key": "contacts.add.channel_identifier_label", "value": "Channel Identifier"}, {"key": "contacts.add.channel_identifier_placeholder", "value": "Phone number or ID"}, {"key": "contacts.add.tags_label", "value": "Tags (comma separated)"}, {"key": "contacts.add.tags_placeholder", "value": "lead, customer, etc."}, {"key": "contacts.add.notes_label", "value": "Notes"}, {"key": "contacts.add.notes_placeholder", "value": "Additional notes about this contact..."}, {"key": "contacts.add.creating", "value": "Creating..."}, {"key": "contacts.add.create_button", "value": "Create Contact"}, {"key": "contacts.add.success_title", "value": "Contact created"}, {"key": "contacts.add.success_description", "value": "The contact has been successfully created."}, {"key": "contacts.add.error_title", "value": "Creation failed"}, {"key": "contacts.import.button", "value": "Import CSV"}, {"key": "contacts.import.title", "value": "Import Contacts from CSV"}, {"key": "contacts.import.description", "value": "Upload a CSV file to import multiple contacts at once. Download the template to see the required format."}, {"key": "contacts.import.download_template", "value": "Download Template"}, {"key": "contacts.import.file_label", "value": "CSV File"}, {"key": "contacts.import.file_help", "value": "Maximum file size: 10MB. Only CSV files are supported."}, {"key": "contacts.import.preview_label", "value": "Preview (first 5 rows)"}, {"key": "contacts.import.duplicate_handling_label", "value": "Duplicate Handling"}, {"key": "contacts.import.duplicate.skip", "value": "Skip duplicates"}, {"key": "contacts.import.duplicate.update", "value": "Update existing"}, {"key": "contacts.import.duplicate.create", "value": "Create new"}, {"key": "contacts.import.duplicate_help", "value": "How to handle contacts with duplicate email addresses"}, {"key": "contacts.import.importing", "value": "Importing..."}, {"key": "contacts.import.results_label", "value": "Import Results"}, {"key": "contacts.import.successful", "value": "Successfully imported: {{count}}"}, {"key": "contacts.import.failed", "value": "Failed to import: {{count}}"}, {"key": "contacts.import.errors", "value": "Errors:"}, {"key": "contacts.import.more_errors", "value": "And {{count}} more errors..."}, {"key": "contacts.import.import_button", "value": "Import Contacts"}, {"key": "contacts.import.no_file_selected", "value": "Please select a CSV file to import"}, {"key": "contacts.import.success_title", "value": "Import completed"}, {"key": "contacts.import.success_description", "value": "Successfully imported {{count}} contacts"}, {"key": "contacts.import.error_title", "value": "Import failed"}, {"key": "contacts.bulk_actions.selected_count", "value": "{{count}} contacts selected"}, {"key": "contacts.bulk_actions.clear_selection", "value": "Clear Selection"}, {"key": "contacts.bulk_actions.delete_selected", "value": "Delete Selected"}, {"key": "contacts.bulk_actions.deleting", "value": "Deleting..."}, {"key": "contacts.bulk_actions.select_all", "value": "Select all contacts"}, {"key": "contacts.bulk_actions.select_contact", "value": "Select contact {{name}}"}, {"key": "contacts.bulk_delete.title", "value": "Delete {{count}} Contacts"}, {"key": "contacts.bulk_delete.warning", "value": "This will permanently delete these contacts and all associated conversations, messages, and notes. This action cannot be undone."}, {"key": "contacts.bulk_delete.confirm", "value": "Delete {{count}} Contacts"}, {"key": "contacts.bulk_delete.deleting", "value": "Deleting..."}, {"key": "contacts.bulk_delete.success_title", "value": "Contacts deleted"}, {"key": "contacts.bulk_delete.success_description", "value": "Successfully deleted {{count}} of {{total}} contacts"}, {"key": "contacts.bulk_delete.partial_failure_title", "value": "Some deletions failed"}, {"key": "contacts.bulk_delete.partial_failure_description", "value": "{{count}} contacts could not be deleted"}, {"key": "contacts.bulk_delete.error_title", "value": "Bulk delete failed"}, {"key": "contacts.facebook_messenger", "value": "Facebook Messenger"}, {"key": "contacts.instagram", "value": "Instagram"}, {"key": "contacts.no_contacts_found", "value": "No contacts found"}, {"key": "contacts.try_adjusting_filters", "value": "Try adjusting your search or filters"}, {"key": "contacts.add_first_contact", "value": "Add your first contact to get started"}, {"key": "contacts.table.name", "value": "Name"}, {"key": "contacts.table.contact_info", "value": "Contact Info"}, {"key": "contacts.table.company", "value": "Company"}, {"key": "contacts.table.channel", "value": "Channel"}, {"key": "contacts.table.tags", "value": "Tags"}, {"key": "contacts.table.last_updated", "value": "Last Updated"}, {"key": "contacts.table.actions", "value": "Actions"}, {"key": "contacts.message", "value": "Message"}, {"key": "contacts.delete_contact", "value": "Delete Contact"}, {"key": "contacts.delete_warning", "value": "This will permanently delete this contact and all associated conversations, messages, and notes. This action cannot be undone."}, {"key": "common.today", "value": "Today"}, {"key": "common.unknown_contact", "value": "Unknown Contact"}, {"key": "common.cancel", "value": "Cancel"}, {"key": "common.delete", "value": "Delete"}, {"key": "common.filter", "value": "Filter"}, {"key": "common.success", "value": "Success"}, {"key": "common.missing_information", "value": "Missing Information"}, {"key": "common.fill_required_fields", "value": "Please fill in all required fields"}, {"key": "pipeline.stage_created_success", "value": "Stage created successfully"}, {"key": "pipeline.stage_create_failed", "value": "Failed to create stage: {{error}}"}, {"key": "pipeline.stage_updated_success", "value": "Stage updated successfully"}, {"key": "pipeline.stage_update_failed", "value": "Failed to update stage: {{error}}"}, {"key": "pipeline.stage_deleted_success", "value": "Stage deleted successfully"}, {"key": "pipeline.stage_delete_failed", "value": "Failed to delete stage: {{error}}"}, {"key": "pipeline.deal_update_failed", "value": "Failed to update deal stage: {{error}}"}, {"key": "pipeline.stage_name_required", "value": "Stage name cannot be empty"}, {"key": "pipeline.add_stage", "value": "Add Stage"}, {"key": "pipeline.add_deal", "value": "Add Deal"}, {"key": "pipeline.no_stages", "value": "No Pipeline Stages"}, {"key": "pipeline.create_first_stage", "value": "Create your first stage to get started"}, {"key": "pipeline.add_first_stage", "value": "Add First Stage"}, {"key": "pipeline.add_stage_title", "value": "Add Pipeline Stage"}, {"key": "pipeline.add_stage_description", "value": "Create a new stage for your pipeline. Stages help organize your deals and track their progress."}, {"key": "pipeline.stage_name", "value": "Stage Name"}, {"key": "pipeline.stage_name_placeholder", "value": "e.g., Discovery, Negotiation, Proposal"}, {"key": "pipeline.stage_color", "value": "Stage Color"}, {"key": "pipeline.create_stage", "value": "Create Stage"}, {"key": "pipeline.edit_stage_title", "value": "Edit Pipeline Stage"}, {"key": "pipeline.edit_stage_description", "value": "Update the stage name and color."}, {"key": "pipeline.update_stage", "value": "Update Stage"}, {"key": "pipeline.delete_stage_title", "value": "Delete Pipeline Stage"}, {"key": "pipeline.stage_contains_deals", "value": "This stage contains {{count}} deals. Where would you like to move them?"}, {"key": "pipeline.delete_stage_confirmation", "value": "Are you sure you want to delete this stage? This action cannot be undone."}, {"key": "pipeline.move_deals_to", "value": "Move deals to"}, {"key": "pipeline.select_stage", "value": "Select a stage"}, {"key": "pipeline.select_target_stage_warning", "value": "You must select a target stage or the deals in this stage will be lost."}, {"key": "pipeline.delete_stage", "value": "Delete Stage"}, {"key": "calendar.event_created", "value": "Event Created"}, {"key": "calendar.appointment_created_success", "value": "Your appointment has been successfully created"}, {"key": "calendar.event_create_failed", "value": "Failed to create event: {{error}}"}, {"key": "calendar.event_updated", "value": "Event Updated"}, {"key": "calendar.appointment_updated_success", "value": "Your appointment has been successfully updated"}, {"key": "calendar.event_update_failed", "value": "Failed to update event: {{error}}"}, {"key": "calendar.event_canceled", "value": "Event Canceled"}, {"key": "calendar.appointment_canceled_success", "value": "Your appointment has been successfully canceled"}, {"key": "calendar.event_cancel_failed", "value": "Failed to cancel event: {{error}}"}, {"key": "calendar.not_connected", "value": "Google Calendar Not Connected"}, {"key": "calendar.connect_first", "value": "Please connect your Google Calendar in Settings first"}, {"key": "calendar.enter_schedule_name", "value": "Please enter a schedule name"}, {"key": "calendar.schedule_added", "value": "Schedule Added"}, {"key": "calendar.schedule_added_success", "value": "New schedule \"{{name}}\" has been added"}, {"key": "calendar.schedule_updated", "value": "Schedule Updated"}, {"key": "calendar.schedule_updated_success", "value": "Schedule \"{{name}}\" has been updated"}, {"key": "calendar.schedule_deleted", "value": "Schedule Deleted"}, {"key": "calendar.schedule_deleted_success", "value": "Schedule \"{{name}}\" has been deleted"}, {"key": "calendar.more", "value": "more"}, {"key": "calendar.personal_teams", "value": "Personal, Teams"}, {"key": "calendar.today", "value": "Today"}, {"key": "calendar.month", "value": "Month"}, {"key": "calendar.week", "value": "Week"}, {"key": "calendar.day", "value": "Day"}, {"key": "calendar.my_schedules", "value": "My Schedules"}, {"key": "calendar.categories", "value": "Categories"}, {"key": "calendar.sun", "value": "Sun"}, {"key": "calendar.mon", "value": "Mon"}, {"key": "calendar.tue", "value": "<PERSON><PERSON>"}, {"key": "calendar.wed", "value": "Wed"}, {"key": "calendar.thu", "value": "<PERSON>hu"}, {"key": "calendar.fri", "value": "<PERSON><PERSON>"}, {"key": "calendar.sat", "value": "Sat"}, {"key": "calendar.check_availability", "value": "Check Availability"}, {"key": "calendar.schedule_appointment", "value": "Schedule Appointment"}, {"key": "calendar.schedule_new_appointment", "value": "Schedule New Appointment"}, {"key": "calendar.create_new_event", "value": "Create a new event on your calendar."}, {"key": "calendar.title", "value": "Title"}, {"key": "calendar.description", "value": "Description"}, {"key": "calendar.location", "value": "Location"}, {"key": "calendar.category", "value": "Category"}, {"key": "calendar.select_category", "value": "Select category"}, {"key": "campaigns.completed", "value": "Campaign Completed"}, {"key": "campaigns.finished_processing", "value": "Campaign \"{{name}}\" has finished processing."}, {"key": "campaigns.fetch_failed", "value": "Failed to fetch campaigns"}, {"key": "campaigns.stats_fetch_failed", "value": "Failed to fetch campaign statistics"}, {"key": "campaigns.action_failed", "value": "Failed to {{action}} campaign"}, {"key": "campaigns.deleted_successfully", "value": "Campaign \"{{name}}\" deleted successfully"}, {"key": "campaigns.delete_failed", "value": "Failed to delete campaign"}, {"key": "campaigns.stats_recalculated", "value": "Stats Recalculated"}, {"key": "campaigns.stats_updated", "value": "Campaign statistics have been updated."}, {"key": "campaigns.stats_recalc_failed", "value": "Failed to recalculate stats: {{error}}"}, {"key": "campaigns.network_error", "value": "Network error: {{error}}"}, {"key": "campaigns.dashboard_description", "value": "Manage and monitor your mass messaging campaigns"}, {"key": "campaigns.live_updates", "value": "Live updates"}, {"key": "campaigns.polling_mode", "value": "Polling mode"}, {"key": "campaigns.refresh", "value": "Refresh"}, {"key": "campaigns.create_campaign", "value": "Create Campaign"}, {"key": "campaigns.total_campaigns", "value": "Total Campaigns"}, {"key": "campaigns.active_campaigns", "value": "Active Campaigns"}, {"key": "campaigns.total_recipients", "value": "Total Recipients"}, {"key": "campaigns.messages_delivered", "value": "Messages Delivered"}, {"key": "campaigns.delivery_rate", "value": "Delivery Rate"}, {"key": "campaigns.no_campaigns_found", "value": "No campaigns found"}, {"key": "campaigns.get_started_message", "value": "Get started by creating your first mass messaging campaign"}, {"key": "campaigns.details", "value": "Details"}, {"key": "campaigns.recipients", "value": "Recipients"}, {"key": "campaigns.progress", "value": "Progress"}, {"key": "campaigns.delivered", "value": "Delivered"}, {"key": "campaigns.failed", "value": "Failed"}, {"key": "campaigns.processed", "value": "processed"}, {"key": "campaigns.total", "value": "total"}, {"key": "campaigns.delete_campaign", "value": "Delete Campaign"}, {"key": "campaigns.delete_confirmation", "value": "Are you sure you want to delete the campaign \"{{name}}\"? This action cannot be undone and all campaign data will be permanently removed."}, {"key": "campaigns.start", "value": "Start"}, {"key": "campaigns.pause", "value": "Pause"}, {"key": "campaigns.resume", "value": "Resume"}, {"key": "campaigns.status.draft", "value": "Draft"}, {"key": "campaigns.status.scheduled", "value": "Scheduled"}, {"key": "campaigns.status.running", "value": "Running"}, {"key": "campaigns.status.paused", "value": "Paused"}, {"key": "campaigns.status.completed", "value": "Completed"}, {"key": "campaigns.status.cancelled", "value": "Cancelled"}, {"key": "campaigns.status.failed", "value": "Failed"}, {"key": "campaigns.filter.all", "value": "All"}, {"key": "campaigns.filter.draft", "value": "Draft"}, {"key": "campaigns.filter.running", "value": "Running"}, {"key": "campaigns.filter.paused", "value": "Paused"}, {"key": "campaigns.filter.completed", "value": "Completed"}, {"key": "flows.flow_assigned", "value": "Flow assigned"}, {"key": "flows.flow_assigned_success", "value": "Flow has been assigned to channel successfully."}, {"key": "flows.error_assigning_flow", "value": "Error assigning flow"}, {"key": "flows.assignment_updated", "value": "Assignment updated"}, {"key": "flows.assignment_updated_success", "value": "Flow assignment status has been updated."}, {"key": "flows.error_updating_assignment", "value": "Error updating assignment"}, {"key": "flows.assign", "value": "Assign"}, {"key": "flows.manage_flow_assignments", "value": "Manage Flow Assignments"}, {"key": "flows.assign_flow_description", "value": "Assign \"{{flowName}}\" to channels and manage active assignments."}, {"key": "flows.active_assignments", "value": "Active Assignments"}, {"key": "flows.channel", "value": "Channel"}, {"key": "flows.status", "value": "Status"}, {"key": "flows.actions", "value": "Actions"}, {"key": "flows.active", "value": "Active"}, {"key": "flows.inactive", "value": "Inactive"}, {"key": "flows.deactivate", "value": "Deactivate"}, {"key": "flows.activate", "value": "Activate"}, {"key": "flows.no_assignments_yet", "value": "No assignments yet"}, {"key": "flows.available_channels", "value": "Available Channels"}, {"key": "flows.no_channels_available", "value": "No channels available"}, {"key": "flows.flow_deleted", "value": "Flow deleted"}, {"key": "flows.flow_deleted_success", "value": "Flow has been deleted successfully."}, {"key": "flows.error_deleting_flow", "value": "Error deleting flow"}, {"key": "flows.delete_flow", "value": "Delete Flow"}, {"key": "flows.delete_flow_confirmation", "value": "Are you sure you want to delete \"{{flowName}}\"? This action cannot be undone."}, {"key": "flows.draft", "value": "Draft"}, {"key": "flows.archived", "value": "Archived"}, {"key": "flows.page_description", "value": "Create and manage automated conversation flows for your channels."}, {"key": "flows.flows", "value": "flows"}, {"key": "flows.limit_reached", "value": "Limit reached"}, {"key": "flows.contact_admin_upgrade", "value": "Contact your administrator to upgrade your plan"}, {"key": "flows.plan_limit_tooltip", "value": "You've reached your plan's flow limit"}, {"key": "flows.new_flow", "value": "New Flow"}, {"key": "flows.your_flows", "value": "Your Flows"}, {"key": "flows.flows_description", "value": "Flows can be assigned to one or more channels to handle conversations automatically."}, {"key": "flows.name", "value": "Name"}, {"key": "flows.updated", "value": "Updated"}, {"key": "flows.sorted_descending", "value": "Sorted descending"}, {"key": "flows.sorted_ascending", "value": "Sorted ascending"}, {"key": "flows.version", "value": "Version"}, {"key": "flows.no_flows_yet", "value": "No flows yet"}, {"key": "flows.create_first_flow", "value": "Create your first flow to start automating conversations."}, {"key": "flows.create_flow", "value": "Create Flow"}, {"key": "common.language_selector.title", "value": "Select Language"}, {"key": "common.language_selector.no_languages", "value": "No languages available"}, {"key": "common.language_selector.changing", "value": "Changing language..."}, {"key": "admin.backup.title", "value": "Backup Management"}, {"key": "admin.backup.loading", "value": "Loading backup system..."}, {"key": "admin.backup.tabs.overview", "value": "Overview"}, {"key": "admin.backup.tabs.backups", "value": "Backups"}, {"key": "admin.backup.tabs.schedules", "value": "Schedules"}, {"key": "admin.backup.tabs.settings", "value": "Settings"}, {"key": "admin.backup.stats.total_backups", "value": "Total Backups"}, {"key": "admin.backup.stats.total_size", "value": "Total Size"}, {"key": "admin.backup.stats.local_backups", "value": "Local Backups"}, {"key": "admin.backup.stats.cloud_backups", "value": "Cloud Backups"}, {"key": "admin.backup.quick_actions.title", "value": "Quick Actions"}, {"key": "admin.backup.quick_actions.description", "value": "Create manual backups and manage your backup system"}, {"key": "admin.backup.quick_actions.backup_description", "value": "Backup Description"}, {"key": "admin.backup.quick_actions.backup_description_placeholder", "value": "Enter backup description..."}, {"key": "admin.backup.quick_actions.storage_locations", "value": "Storage Locations"}, {"key": "admin.backup.storage.local", "value": "Local Storage"}, {"key": "admin.backup.storage.google_drive", "value": "Google Drive"}, {"key": "admin.backup.storage.drive", "value": "Drive"}, {"key": "admin.backup.storage.not_connected", "value": "(Not Connected)"}, {"key": "admin.backup.actions.create_backup", "value": "Create Backup"}, {"key": "admin.backup.history.title", "value": "Backup History"}, {"key": "admin.backup.history.description", "value": "View and manage all database backups"}, {"key": "admin.backup.table.filename", "value": "Filename"}, {"key": "admin.backup.table.type", "value": "Type"}, {"key": "admin.backup.table.size", "value": "Size"}, {"key": "admin.backup.table.status", "value": "Status"}, {"key": "admin.backup.table.storage", "value": "Storage"}, {"key": "admin.backup.table.created", "value": "Created"}, {"key": "admin.backup.table.actions", "value": "Actions"}, {"key": "admin.backup.status.creating", "value": "Creating"}, {"key": "admin.backup.status.completed", "value": "Completed"}, {"key": "admin.backup.status.failed", "value": "Failed"}, {"key": "admin.backup.status.uploading", "value": "Uploading"}, {"key": "admin.backup.status.uploaded", "value": "Uploaded"}, {"key": "admin.backup.empty.title", "value": "No Backups Found"}, {"key": "admin.backup.empty.description", "value": "Create your first backup to get started"}, {"key": "admin.backup.messages.config_saved", "value": "Backup configuration saved successfully"}, {"key": "admin.backup.messages.backup_started", "value": "Backup creation started successfully"}, {"key": "admin.backup.schedules.title", "value": "Backup Schedules"}, {"key": "admin.backup.schedules.description", "value": "Configure automated backup schedules"}, {"key": "admin.backup.schedules.new_schedule", "value": "New Schedule"}, {"key": "admin.backup.schedules.active_title", "value": "Active Schedules"}, {"key": "admin.backup.schedules.active_description", "value": "Manage your automated backup schedules"}, {"key": "admin.backup.schedules.table.name", "value": "Schedule Name"}, {"key": "admin.backup.schedules.table.frequency", "value": "Frequency"}, {"key": "admin.backup.schedules.table.time", "value": "Time"}, {"key": "admin.backup.schedules.table.next_run", "value": "Next Run"}, {"key": "admin.backup.schedules.table.storage", "value": "Storage"}, {"key": "admin.backup.schedules.table.status", "value": "Status"}, {"key": "admin.backup.schedules.table.actions", "value": "Actions"}, {"key": "admin.backup.schedules.status.enabled", "value": "Enabled"}, {"key": "admin.backup.schedules.status.disabled", "value": "Disabled"}, {"key": "admin.backup.schedules.empty.title", "value": "No Schedules Configured"}, {"key": "admin.backup.schedules.empty.description", "value": "Create your first automated backup schedule to get started"}, {"key": "admin.backup.schedules.actions.create", "value": "Create Schedule"}, {"key": "admin.backup.restore.title", "value": "Database Restoration"}, {"key": "admin.backup.restore.warning_step", "value": "Review the backup details and understand the implications"}, {"key": "admin.backup.restore.confirmation_step", "value": "Confirm your intention to restore the database"}, {"key": "admin.backup.restore.progress_step", "value": "Database restoration in progress"}, {"key": "admin.backup.restore.backup_info", "value": "Backup Information"}, {"key": "admin.backup.restore.filename", "value": "Filename"}, {"key": "admin.backup.restore.created", "value": "Created"}, {"key": "admin.backup.restore.size", "value": "Size"}, {"key": "admin.backup.restore.type", "value": "Type"}, {"key": "admin.backup.restore.description", "value": "Description"}, {"key": "admin.backup.restore.storage", "value": "Storage"}, {"key": "admin.backup.restore.warning_title", "value": "Critical Warning"}, {"key": "admin.backup.restore.warning_replace", "value": "This action will completely replace your current database"}, {"key": "admin.backup.restore.warning_data_lost", "value": "All current data will be permanently lost"}, {"key": "admin.backup.restore.warning_disconnect", "value": "All users will be disconnected during the restoration"}, {"key": "admin.backup.restore.warning_undone", "value": "The restoration process cannot be undone"}, {"key": "admin.backup.restore.warning_backup", "value": "Make sure you have a recent backup of the current state if needed"}, {"key": "admin.backup.restore.process_title", "value": "Restoration Process"}, {"key": "admin.backup.restore.process_download", "value": "Backup file will be downloaded if stored in cloud"}, {"key": "admin.backup.restore.process_verify", "value": "Backup integrity will be verified"}, {"key": "admin.backup.restore.process_clean", "value": "Current database will be cleaned"}, {"key": "admin.backup.restore.process_restore", "value": "Backup data will be restored"}, {"key": "admin.backup.restore.process_ready", "value": "System will be ready for use"}, {"key": "admin.backup.restore.confirmation_title", "value": "Final Confirmation Required"}, {"key": "admin.backup.restore.confirmation_text", "value": "To proceed with the database restoration, please type the backup filename exactly as shown below:"}, {"key": "admin.backup.restore.confirmation_label", "value": "Confirmation Text"}, {"key": "admin.backup.restore.confirmation_placeholder", "value": "Type \"{filename}\" to confirm"}, {"key": "admin.backup.restore.confirmation_help", "value": "This confirmation ensures you understand the consequences of this action."}, {"key": "admin.backup.restore.progress_title", "value": "Restoration in Progress"}, {"key": "admin.backup.restore.progress_processing", "value": "Processing..."}, {"key": "admin.backup.restore.completed_title", "value": "Restoration Completed"}, {"key": "admin.backup.restore.completed_message", "value": "Database has been successfully restored. The dialog will close automatically."}, {"key": "admin.backup.restore.failed_title", "value": "Restoration Failed"}, {"key": "admin.backup.restore.cancel", "value": "Cancel"}, {"key": "admin.backup.restore.understand_continue", "value": "I Understand, Continue"}, {"key": "admin.backup.restore.back", "value": "Back"}, {"key": "admin.backup.restore.restore_database", "value": "Restore Database"}, {"key": "admin.backup.restore.close", "value": "Close"}, {"key": "admin.backup.schedule_dialog.create_title", "value": "Create Backup Schedule"}, {"key": "admin.backup.schedule_dialog.edit_title", "value": "Edit Backup Schedule"}, {"key": "admin.backup.schedule_dialog.description", "value": "Configure automated backup schedule settings"}, {"key": "admin.backup.schedule_dialog.name_label", "value": "Schedule Name"}, {"key": "admin.backup.schedule_dialog.name_placeholder", "value": "e.g., Daily Backup, Weekly Archive"}, {"key": "admin.backup.schedule_dialog.name_help", "value": "A descriptive name for this backup schedule"}, {"key": "admin.backup.schedule_dialog.frequency_label", "value": "Backup Frequency"}, {"key": "admin.backup.schedule_dialog.frequency_daily", "value": "Daily"}, {"key": "admin.backup.schedule_dialog.frequency_weekly", "value": "Weekly"}, {"key": "admin.backup.schedule_dialog.frequency_monthly", "value": "Monthly"}, {"key": "admin.backup.schedule_dialog.time_label", "value": "Backup Time"}, {"key": "admin.backup.schedule_dialog.time_help", "value": "Time when the backup will be executed (24-hour format)"}, {"key": "admin.backup.schedule_dialog.day_of_week", "value": "Day of Week"}, {"key": "admin.backup.schedule_dialog.day_of_month", "value": "Day of Month"}, {"key": "admin.backup.schedule_dialog.day_of_month_help", "value": "Day of the month when backup will be executed (1-31)"}, {"key": "admin.backup.schedule_dialog.storage_label", "value": "Storage Locations"}, {"key": "admin.backup.schedule_dialog.storage_help", "value": "Where to store the backup files"}, {"key": "admin.backup.schedule_dialog.enable_title", "value": "Enable Schedule"}, {"key": "admin.backup.schedule_dialog.enable_help", "value": "Start this schedule immediately after creation"}, {"key": "admin.backup.schedule_dialog.update_button", "value": "Update Schedule"}, {"key": "admin.backup.schedule_dialog.create_button", "value": "Create Schedule"}, {"key": "admin.backup.days.sunday", "value": "Sunday"}, {"key": "admin.backup.days.monday", "value": "Monday"}, {"key": "admin.backup.days.tuesday", "value": "Tuesday"}, {"key": "admin.backup.days.wednesday", "value": "Wednesday"}, {"key": "admin.backup.days.thursday", "value": "Thursday"}, {"key": "admin.backup.days.friday", "value": "Friday"}, {"key": "admin.backup.days.saturday", "value": "Saturday"}, {"key": "admin.backup.messages.schedule_created", "value": "Schedule created successfully"}, {"key": "admin.backup.messages.schedule_updated", "value": "Schedule updated successfully"}, {"key": "admin.backup.messages.schedule_deleted", "value": "Schedule deleted successfully"}, {"key": "admin.backup.messages.backup_deleted", "value": "Backup deleted successfully"}, {"key": "admin.backup.messages.backup_valid", "value": "Backup Valid"}, {"key": "admin.backup.messages.backup_invalid", "value": "Backup Invalid"}, {"key": "admin.backup.messages.restore_successful", "value": "Restore Successful"}, {"key": "admin.backup.messages.restore_failed", "value": "Restore Failed"}, {"key": "admin.backup.messages.restore_error", "value": "<PERSON><PERSON>"}, {"key": "admin.backup.messages.settings_saved", "value": "Setting<PERSON> saved successfully"}, {"key": "admin.backup.messages.connection_successful", "value": "Connection Successful"}, {"key": "admin.backup.messages.connection_failed", "value": "Connection Failed"}, {"key": "admin.backup.actions.reset_defaults", "value": "Reset to Defaults"}, {"key": "admin.backup.actions.save_settings", "value": "Save Settings"}, {"key": "common.cancel", "value": "Cancel"}, {"key": "common.validation_error", "value": "Validation Error"}, {"key": "common.file_size.bytes", "value": "Bytes"}, {"key": "common.file_size.kb", "value": "KB"}, {"key": "common.file_size.mb", "value": "MB"}, {"key": "common.file_size.gb", "value": "GB"}, {"key": "common.file_size.tb", "value": "TB"}, {"key": "admin.backup.messages.validation_successful", "value": "Validation Successful"}, {"key": "admin.backup.messages.validation_failed", "value": "Validation Failed"}, {"key": "admin.backup.validation.schedule_name_required", "value": "Schedule name is required"}, {"key": "admin.backup.validation.oauth_credentials_required", "value": "Client ID and Client Secret are required"}, {"key": "admin.backup.default_description", "value": "Manual backup"}, {"key": "admin.backup.restore.preparing", "value": "Preparing restoration..."}, {"key": "admin.backup.oauth.credentials_configured", "value": "OAuth Credentials Configured"}, {"key": "admin.backup.oauth.client_id", "value": "Client ID"}, {"key": "admin.backup.oauth.redirect_uri", "value": "Redirect URI"}, {"key": "admin.backup.oauth.source", "value": "Source"}, {"key": "admin.backup.oauth.admin_interface", "value": "Admin Interface"}, {"key": "admin.backup.oauth.environment_variables", "value": "Environment Variables"}, {"key": "admin.backup.oauth.configured", "value": "Configured"}, {"key": "admin.backup.actions.download_backup", "value": "Download backup"}, {"key": "admin.backup.actions.verify_backup", "value": "Verify backup integrity"}, {"key": "admin.backup.actions.restore_backup", "value": "Restore database from this backup"}, {"key": "admin.backup.actions.delete_backup", "value": "Delete backup"}, {"key": "admin.backup.actions.edit_schedule", "value": "Edit schedule"}, {"key": "admin.backup.actions.disable_schedule", "value": "Disable schedule"}, {"key": "admin.backup.actions.enable_schedule", "value": "Enable schedule"}, {"key": "admin.backup.actions.delete_schedule", "value": "Delete schedule"}, {"key": "admin.backup.actions.upload_backup", "value": "Upload Backup"}, {"key": "admin.backup.upload.title", "value": "Upload Database Backup"}, {"key": "admin.backup.upload.description", "value": "Upload an external database backup file to add it to your backup collection. Supported formats: .sql, .backup, .dump, .bak"}, {"key": "admin.backup.upload.file_label", "value": "Backup File"}, {"key": "admin.backup.upload.file_help", "value": "Maximum file size: 500MB. Supported formats: .sql, .backup, .dump, .bak"}, {"key": "admin.backup.upload.description_label", "value": "Description (Optional)"}, {"key": "admin.backup.upload.description_placeholder", "value": "Enter a description for this backup..."}, {"key": "admin.backup.upload.storage_label", "value": "Storage Locations"}, {"key": "admin.backup.upload.storage_help", "value": "Choose where to store the uploaded backup"}, {"key": "admin.backup.upload.uploading", "value": "Uploading..."}, {"key": "admin.backup.upload.upload_button", "value": "Upload Backup"}, {"key": "admin.backup.upload.no_file_selected", "value": "Please select a backup file to upload"}, {"key": "admin.backup.messages.backup_uploaded", "value": "Backup uploaded successfully"}, {"key": "admin.backup.dialogs.delete_backup_title", "value": "Delete Backup"}, {"key": "admin.backup.dialogs.delete_backup_description", "value": "Are you sure you want to delete this backup? This action cannot be undone."}, {"key": "admin.backup.dialogs.delete_schedule_title", "value": "Delete Schedule"}, {"key": "admin.backup.dialogs.delete_schedule_description", "value": "Are you sure you want to delete this backup schedule? This action cannot be undone."}, {"key": "admin.backup.settings.default_storage_locations", "value": "Default Storage Locations"}, {"key": "admin.backup.settings.default_storage_help", "value": "Default storage locations for new backups"}, {"key": "admin.backup.google_drive.title", "value": "Google Drive Integration"}, {"key": "admin.backup.google_drive.description", "value": "Configure OAuth 2.0 credentials and cloud storage backup options"}, {"key": "admin.backup.google_drive.connection_title", "value": "Google Drive Connection"}, {"key": "admin.backup.google_drive.connection_description", "value": "Authorize {{appName}} to access your Google Drive for backup storage."}, {"key": "admin.backup.google_drive.connect_drive", "value": "Connect Google Drive"}, {"key": "admin.backup.google_drive.test_connection", "value": "Test Connection"}, {"key": "admin.backup.oauth.configuration_title", "value": "OAuth 2.0 Configuration"}, {"key": "admin.backup.oauth.configuration_description", "value": "Configure Google OAuth credentials for Drive access"}, {"key": "admin.backup.oauth.configured_status", "value": "Configured"}, {"key": "admin.backup.oauth.not_configured_status", "value": "Not Configured"}, {"key": "admin.backup.oauth.validate_credentials", "value": "Validate Credentials"}, {"key": "admin.backup.oauth.clear_credentials", "value": "Clear Credentials"}, {"key": "admin.backup.oauth.setup_required", "value": "OAuth Setup Required"}, {"key": "admin.backup.oauth.setup_description", "value": "To enable Google Drive backups, you need to configure OAuth 2.0 credentials from Google Cloud Console."}, {"key": "admin.backup.oauth.setup_instructions", "value": "Setup Instructions"}, {"key": "admin.backup.oauth.step_1", "value": "Go to"}, {"key": "admin.backup.oauth.google_cloud_console", "value": "Google Cloud Console"}, {"key": "admin.backup.oauth.step_2", "value": "Create a new project or select existing one"}, {"key": "admin.backup.oauth.step_3", "value": "Enable the Google Drive API"}, {"key": "admin.backup.oauth.step_4", "value": "Create OAuth 2.0 credentials (Web application)"}, {"key": "admin.backup.oauth.step_5", "value": "Add your redirect URI to authorized redirect URIs"}, {"key": "admin.backup.oauth.step_6", "value": "Copy the Client ID and Client Secret"}, {"key": "admin.backup.oauth.configure_credentials", "value": "Configure OAuth Credentials"}, {"key": "admin.backup.oauth.client_id_label", "value": "Google Client ID"}, {"key": "admin.backup.oauth.client_id_placeholder", "value": "Enter your Google OAuth Client ID"}, {"key": "admin.backup.oauth.client_id_help", "value": "The Client ID from your Google Cloud Console OAuth credentials"}, {"key": "admin.backup.oauth.client_secret_label", "value": "Google Client Secret"}, {"key": "admin.backup.oauth.client_secret_placeholder", "value": "Enter your Google OAuth Client Secret"}, {"key": "admin.backup.oauth.client_secret_help", "value": "The Client Secret from your Google Cloud Console OAuth credentials"}, {"key": "admin.backup.oauth.redirect_uri_label", "value": "Redirect URI"}, {"key": "admin.backup.oauth.redirect_uri_help", "value": "This URI must be added to your Google OAuth authorized redirect URIs"}, {"key": "admin.backup.oauth.save_credentials", "value": "Save Credentials"}, {"key": "admin.backup.dialogs.clear_oauth_title", "value": "Clear OAuth Credentials"}, {"key": "admin.backup.dialogs.clear_oauth_description", "value": "This will remove all stored Google OAuth credentials and disable Google Drive integration. You will need to reconfigure credentials to use Google Drive backups."}, {"key": "common.delete", "value": "Delete"}, {"key": "admin.backup.storage.local", "value": "Local Storage"}, {"key": "admin.backup.storage.google_drive", "value": "Google Drive"}, {"key": "admin.backup.storage.not_connected", "value": "(Not Connected)"}, {"key": "admin.backup.oauth.credentials_form_title", "value": "OAuth 2.0 Credentials"}, {"key": "admin.backup.google_drive.enable_title", "value": "Enable Google Drive Backups"}, {"key": "admin.backup.google_drive.enable_description", "value": "Store backups in Google Drive for cloud redundancy"}, {"key": "admin.backup.google_drive.connected_title", "value": "Google Drive Connected"}, {"key": "admin.backup.google_drive.connected_description", "value": "Your Google Drive account is connected and ready for backups."}, {"key": "admin.backup.google_drive.oauth_required_title", "value": "OAuth Configuration Required"}, {"key": "admin.backup.google_drive.oauth_required_description", "value": "Please configure OAuth 2.0 credentials above before enabling Google Drive backups."}, {"key": "admin.backup.settings.general_title", "value": "General Settings"}, {"key": "admin.backup.settings.general_description", "value": "Configure global backup system preferences"}, {"key": "admin.backup.settings.retention_label", "value": "Backup Retention Period (days)"}, {"key": "admin.backup.settings.retention_help", "value": "Backups older than this will be automatically deleted"}, {"key": "admin.backup.oauth.update_credentials", "value": "Update Credentials"}, {"key": "common.back", "value": "Back"}, {"key": "admin.company_deletion.title", "value": "Delete Company"}, {"key": "admin.company_deletion.messages.success_title", "value": "Company Deleted"}, {"key": "admin.company_deletion.messages.error_title", "value": "Deletion Failed"}, {"key": "admin.company_deletion.steps.preview_description", "value": "Review what will be permanently deleted"}, {"key": "admin.company_deletion.steps.confirm_description", "value": "Confirm the deletion by typing the company name"}, {"key": "admin.company_deletion.steps.final_description", "value": "Final confirmation - this action cannot be undone"}, {"key": "admin.company_deletion.data_types.users", "value": "Users"}, {"key": "admin.company_deletion.data_types.conversations", "value": "Conversations"}, {"key": "admin.company_deletion.data_types.messages", "value": "Messages"}, {"key": "admin.company_deletion.data_types.contacts", "value": "Contacts"}, {"key": "admin.company_deletion.data_types.flows", "value": "Flows"}, {"key": "admin.company_deletion.data_types.deals", "value": "Deals"}, {"key": "admin.company_deletion.data_types.payment_records", "value": "Payment Records"}, {"key": "admin.company_deletion.data_types.media_files", "value": "Media Files"}, {"key": "admin.company_deletion.data_types.whatsapp_sessions", "value": "WhatsApp Sessions"}, {"key": "admin.company_deletion.preview.warning_title", "value": "Warning: Irreversible Action"}, {"key": "admin.company_deletion.preview.warning_description", "value": "This will permanently delete the company and ALL associated data. This action cannot be undone."}, {"key": "admin.company_deletion.preview.data_title", "value": "Data to be permanently deleted"}, {"key": "admin.company_deletion.preview.warnings_title", "value": "Critical Warnings"}, {"key": "admin.company_deletion.preview.load_error", "value": "Failed to load deletion preview"}, {"key": "admin.company_deletion.confirm.title", "value": "Type Company Name to Continue"}, {"key": "admin.company_deletion.confirm.description", "value": "To confirm deletion, type the exact company name"}, {"key": "admin.company_deletion.confirm.label", "value": "Company Name"}, {"key": "admin.company_deletion.confirm.placeholder", "value": "Type \"{{name}}\" to confirm"}, {"key": "admin.company_deletion.final.title", "value": "Final Confirmation"}, {"key": "admin.company_deletion.final.description", "value": "You are about to permanently delete {{name}} and all its data. This action is irreversible and will immediately remove all associated information."}, {"key": "admin.company_deletion.final.consequences_title", "value": "What happens next"}, {"key": "admin.company_deletion.final.consequence_users", "value": "All user accounts will be deleted"}, {"key": "admin.company_deletion.final.consequence_conversations", "value": "All conversations and messages will be removed"}, {"key": "admin.company_deletion.final.consequence_contacts", "value": "All contacts and their data will be deleted"}, {"key": "admin.company_deletion.final.consequence_media", "value": "All media files will be permanently removed"}, {"key": "admin.company_deletion.final.consequence_whatsapp", "value": "All WhatsApp sessions will be terminated"}, {"key": "admin.company_deletion.final.consequence_payments", "value": "All payment records will be deleted"}, {"key": "admin.company_deletion.final.consequence_company", "value": "The company will be completely removed from the system"}, {"key": "admin.company_deletion.buttons.continue_to_confirmation", "value": "Continue to Confirmation"}, {"key": "admin.company_deletion.buttons.proceed_to_final", "value": "Proceed to Final Step"}, {"key": "admin.company_deletion.buttons.deleting", "value": "Deleting..."}, {"key": "admin.company_deletion.buttons.delete_permanently", "value": "Delete Company Permanently"}, {"key": "campaigns.builder.steps.basic", "value": "Basic Info"}, {"key": "campaigns.builder.steps.audience", "value": "Audience"}, {"key": "campaigns.builder.steps.content", "value": "Content"}, {"key": "campaigns.builder.steps.settings", "value": "Settings"}, {"key": "campaigns.builder.steps.antiban", "value": "Anti-Ban"}, {"key": "campaigns.builder.steps.review", "value": "Review"}, {"key": "campaigns.builder.messages.load_error", "value": "Failed to load campaign data"}, {"key": "campaigns.builder.messages.segment_created", "value": "Segment \"{{name}}\" created and selected"}, {"key": "campaigns.builder.messages.template_created", "value": "Template \"{{name}}\" created and selected"}, {"key": "campaigns.builder.messages.update_success", "value": "Campaign updated successfully"}, {"key": "campaigns.builder.messages.save_success", "value": "Campaign saved as draft"}, {"key": "campaigns.builder.messages.update_error", "value": "Failed to update campaign"}, {"key": "campaigns.builder.messages.save_error", "value": "Failed to save campaign"}, {"key": "campaigns.builder.messages.launch_success_title", "value": "Campaign Launched Successfully! 🚀"}, {"key": "campaigns.builder.messages.launch_success_description", "value": "\"{{name}}\" is now running and will send messages to {{count}} contacts"}, {"key": "campaigns.builder.messages.launch_error_title", "value": "Launch Failed"}, {"key": "campaigns.builder.messages.launch_error_description", "value": "Failed to launch campaign. Please try again."}, {"key": "campaigns.builder.validation.error_title", "value": "Validation Error"}, {"key": "campaigns.builder.validation.name_required", "value": "Campaign name is required"}, {"key": "campaigns.builder.validation.connection_required", "value": "At least one WhatsApp connection is required"}, {"key": "campaigns.builder.validation.segment_required", "value": "Audience segment is required"}, {"key": "campaigns.builder.validation.content_required", "value": "Message content is required"}, {"key": "campaigns.builder.validation.schedule_required", "value": "Scheduled date and time is required for scheduled campaigns"}, {"key": "campaigns.builder.basic.name_label", "value": "Campaign Name"}, {"key": "campaigns.builder.basic.name_placeholder", "value": "Enter campaign name"}, {"key": "campaigns.builder.basic.description_label", "value": "Description"}, {"key": "campaigns.builder.basic.description_placeholder", "value": "Describe your campaign"}, {"key": "campaigns.builder.basic.connections_label", "value": "WhatsApp Connections"}, {"key": "campaigns.builder.basic.connections_description", "value": "Select multiple WhatsApp accounts for better distribution and anti-ban protection"}, {"key": "campaigns.builder.basic.no_connections", "value": "No WhatsApp connections available"}, {"key": "campaigns.builder.basic.setup_connection", "value": "Please set up a WhatsApp connection in Settings > Channel Connections first"}, {"key": "campaigns.builder.basic.accounts_selected", "value": "account(s) selected for distribution"}, {"key": "campaigns.builder.basic.select_all", "value": "Select All"}, {"key": "campaigns.builder.basic.clear_all", "value": "Clear All"}, {"key": "campaigns.builder.basic.type_label", "value": "Campaign Type"}, {"key": "campaigns.builder.basic.type_immediate", "value": "Send Immediately"}, {"key": "campaigns.builder.basic.type_scheduled", "value": "Schedule for Later"}, {"key": "campaigns.builder.basic.type_drip", "value": "Drip Campaign"}, {"key": "campaigns.builder.basic.schedule_label", "value": "Scheduled Date & Time"}, {"key": "campaigns.builder.audience.segment_label", "value": "Select Audience Segment"}, {"key": "campaigns.builder.audience.create_segment", "value": "Create New Segment"}, {"key": "campaigns.builder.audience.segment_placeholder", "value": "Choose a segment"}, {"key": "campaigns.builder.audience.contacts", "value": "contacts"}, {"key": "campaigns.builder.audience.will_receive", "value": "contacts will receive this campaign"}, {"key": "campaigns.builder.content.template_label", "value": "Use Template (Optional)"}, {"key": "campaigns.builder.content.create_template", "value": "Create New Template"}, {"key": "campaigns.builder.content.template_placeholder", "value": "Choose a template"}, {"key": "campaigns.builder.content.message_label", "value": "Message Content"}, {"key": "campaigns.builder.content.message_placeholder", "value": "Enter your message content. Click 'Insert Variable' to add personalization..."}, {"key": "campaigns.builder.content.validation_title", "value": "Content Validation"}, {"key": "campaigns.builder.content.validation_score", "value": "Score"}, {"key": "campaigns.builder.content.validation_issues", "value": "Issues"}, {"key": "campaigns.builder.header.edit", "value": "Edit Campaign"}, {"key": "campaigns.builder.header.create", "value": "Create Campaign"}, {"key": "campaigns.builder.header.step_progress", "value": "Step {{current}} of {{total}}: {{title}}"}, {"key": "campaigns.builder.navigation.previous", "value": "Previous"}, {"key": "campaigns.builder.navigation.next", "value": "Next"}, {"key": "campaigns.builder.navigation.save_draft", "value": "Save Draft"}, {"key": "campaigns.builder.navigation.launch", "value": "Launch Campaign"}, {"key": "campaigns.builder.navigation.launching", "value": "Launching..."}, {"key": "campaigns.builder.launch.confirmation_title", "value": "Launch Campaign Confirmation"}, {"key": "campaigns.builder.launch.confirmation_description", "value": "Are you sure you want to launch this campaign? This action cannot be undone."}, {"key": "campaigns.builder.launch.summary_title", "value": "Campaign Summary"}, {"key": "campaigns.builder.launch.summary_name", "value": "Name"}, {"key": "campaigns.builder.launch.summary_type", "value": "Type"}, {"key": "campaigns.builder.launch.summary_recipients", "value": "Recipients"}, {"key": "campaigns.builder.launch.summary_scheduled", "value": "Scheduled for"}, {"key": "campaigns.builder.launch.warning_title", "value": "Important"}, {"key": "campaigns.builder.launch.warning_description", "value": "Once launched, this campaign will start sending messages immediately and cannot be stopped."}, {"key": "campaigns.builder.launch.confirm_button", "value": "Yes, Launch"}, {"key": "campaigns.builder.settings.rate_limiting_title", "value": "Rate Limiting & Anti-Ban Settings"}, {"key": "campaigns.builder.settings.messages_per_minute", "value": "Messages per Minute"}, {"key": "campaigns.builder.settings.messages_per_hour", "value": "Messages per Hour"}, {"key": "campaigns.builder.settings.delay_between_messages", "value": "Delay Between Messages (seconds)"}, {"key": "campaigns.builder.antiban.title", "value": "Anti-Ban Protection"}, {"key": "campaigns.builder.antiban.enable_label", "value": "Enable Anti-Ban Protection"}, {"key": "campaigns.builder.antiban.enable_description", "value": "Automatically apply intelligent rate limiting and account rotation"}, {"key": "campaigns.builder.antiban.mode_label", "value": "Protection Mode"}, {"key": "campaigns.builder.antiban.mode_description", "value": "Choose how aggressive the anti-ban protection should be"}, {"key": "campaigns.builder.antiban.mode_conservative", "value": "Conservative"}, {"key": "campaigns.builder.antiban.mode_conservative_desc", "value": "Slowest, safest"}, {"key": "campaigns.builder.antiban.mode_moderate", "value": "Moderate"}, {"key": "campaigns.builder.antiban.mode_moderate_desc", "value": "Balanced approach"}, {"key": "campaigns.builder.antiban.mode_aggressive", "value": "Aggressive"}, {"key": "campaigns.builder.antiban.mode_aggressive_desc", "value": "Faster, higher risk"}, {"key": "campaigns.builder.antiban.business_hours_label", "value": "Business Hours Only"}, {"key": "campaigns.builder.antiban.business_hours_desc", "value": "Send only during 9 AM - 6 PM"}, {"key": "campaigns.builder.antiban.respect_weekends_label", "value": "Respect Weekends"}, {"key": "campaigns.builder.antiban.respect_weekends_desc", "value": "Pause on weekends"}, {"key": "campaigns.builder.antiban.randomize_delays_label", "value": "Randomize Delays"}, {"key": "campaigns.builder.antiban.randomize_delays_desc", "value": "Add human-like variance to message timing"}, {"key": "campaigns.builder.antiban.min_delay_label", "value": "<PERSON> (seconds)"}, {"key": "campaigns.builder.antiban.max_delay_label", "value": "<PERSON> (seconds)"}, {"key": "campaigns.builder.antiban.account_rotation_label", "value": "Smart Account Rotation"}, {"key": "campaigns.builder.antiban.account_rotation_desc", "value": "Distribute messages across selected accounts"}, {"key": "campaigns.builder.antiban.cooldown_period_label", "value": "Account Cooldown Period (minutes)"}, {"key": "campaigns.builder.antiban.cooldown_period_desc", "value": "Rest time between high-volume sending for each account"}, {"key": "campaigns.builder.antiban.message_variation_label", "value": "Message Variation"}, {"key": "campaigns.builder.antiban.message_variation_desc", "value": "Add slight variations to avoid identical content"}, {"key": "campaigns.builder.antiban.account_health_title", "value": "Account Health Status"}, {"key": "campaigns.builder.antiban.last_active", "value": "Last active"}, {"key": "campaigns.builder.antiban.account_status_healthy", "value": "Healthy"}, {"key": "campaigns.builder.review.campaign_summary_title", "value": "Campaign Summary"}, {"key": "campaigns.builder.review.basic_info_title", "value": "Basic Information"}, {"key": "campaigns.builder.review.name_label", "value": "Name"}, {"key": "campaigns.builder.review.description_label", "value": "Description"}, {"key": "campaigns.builder.review.channel_label", "value": "Channel"}, {"key": "campaigns.builder.review.type_label", "value": "Type"}, {"key": "campaigns.builder.review.scheduled_for_label", "value": "Scheduled for"}, {"key": "campaigns.builder.review.whatsapp_accounts_title", "value": "WhatsApp Accounts"}, {"key": "campaigns.builder.review.accounts_selected_for_distribution", "value": "account(s) selected for distribution"}, {"key": "campaigns.builder.review.single_account_label", "value": "Single Account"}, {"key": "campaigns.builder.review.no_account_selected", "value": "No WhatsApp account selected"}, {"key": "campaigns.builder.review.audience_title", "value": "Audience"}, {"key": "campaigns.builder.review.segment_label", "value": "Segment"}, {"key": "campaigns.builder.review.no_segment_selected", "value": "No segment selected"}, {"key": "campaigns.builder.review.antiban_title", "value": "Anti-Ban Protection"}, {"key": "campaigns.builder.review.antiban_mode_label", "value": "Mode"}, {"key": "campaigns.builder.review.random_delays_badge", "value": "Random Delays"}, {"key": "campaigns.builder.review.account_rotation_badge", "value": "Account Rotation"}, {"key": "campaigns.builder.review.antiban_disabled", "value": "Anti-ban protection disabled"}, {"key": "campaigns.builder.review.rate_limiting_title", "value": "Rate Limiting"}, {"key": "campaigns.builder.review.per_minute_label", "value": "Per <PERSON>"}, {"key": "campaigns.builder.review.per_hour_label", "value": "Per Hour"}, {"key": "campaigns.builder.review.per_day_label", "value": "Per Day"}, {"key": "campaigns.builder.review.base_delay_label", "value": "Base Delay"}, {"key": "campaigns.builder.review.messages_unit", "value": "messages"}, {"key": "campaigns.builder.review.seconds_unit", "value": "seconds"}, {"key": "campaigns.builder.review.message_content_title", "value": "Message Content"}, {"key": "campaigns.builder.review.media_attachments_label", "value": "Media Attachments"}, {"key": "campaigns.builder.review.media_item_label", "value": "Media"}, {"key": "campaigns.details.title", "value": "Campaign Details"}, {"key": "campaigns.details.fetch_failed", "value": "Failed to fetch campaign details"}, {"key": "campaigns.details.export_failed", "value": "Failed to export Excel"}, {"key": "campaigns.details.export_success", "value": "Campaign data exported to Excel"}, {"key": "campaigns.details.export_excel", "value": "Excel"}, {"key": "campaigns.details.search_placeholder", "value": "Search by contact name or phone number..."}, {"key": "campaigns.details.filter_by_status", "value": "Filter by status"}, {"key": "campaigns.details.all_statuses", "value": "All Statuses"}, {"key": "campaigns.details.status.pending", "value": "Pending"}, {"key": "campaigns.details.status.sent", "value": "<PERSON><PERSON>"}, {"key": "campaigns.details.status.delivered", "value": "Delivered"}, {"key": "campaigns.details.status.failed", "value": "Failed"}, {"key": "campaigns.details.status.cancelled", "value": "Cancelled"}, {"key": "campaigns.details.no_data", "value": "No campaign data found"}, {"key": "campaigns.details.no_results", "value": "No results match your search criteria"}, {"key": "campaigns.details.table.contact_name", "value": "Contact Name"}, {"key": "campaigns.details.table.phone_number", "value": "Phone Number"}, {"key": "campaigns.details.table.whatsapp_account", "value": "WhatsApp Account"}, {"key": "campaigns.details.table.status", "value": "Status"}, {"key": "campaigns.details.table.sent_at", "value": "<PERSON><PERSON>"}, {"key": "campaigns.details.table.message_content", "value": "Message Content"}, {"key": "campaigns.details.table.error", "value": "Error"}, {"key": "campaigns.details.pagination.showing", "value": "Showing {{start}} to {{end}} of {{total}} results"}, {"key": "campaigns.details.pagination.previous", "value": "Previous"}, {"key": "campaigns.details.pagination.next", "value": "Next"}, {"key": "campaigns.details.pagination.page_info", "value": "Page {{current}} of {{total}}"}, {"key": "segments.create.title", "value": "Create Contact Segment"}, {"key": "segments.create.name_label", "value": "Segment Name"}, {"key": "segments.create.name_placeholder", "value": "e.g., VIP Customers, New Leads"}, {"key": "segments.create.description_label", "value": "Description (Optional)"}, {"key": "segments.create.description_placeholder", "value": "Describe this segment..."}, {"key": "segments.create.filter_criteria_title", "value": "<PERSON><PERSON>"}, {"key": "segments.create.contact_tags_label", "value": "Contact Tags"}, {"key": "segments.create.tag_placeholder", "value": "Enter tag name"}, {"key": "segments.create.tags_description", "value": "Contacts must have ALL selected tags"}, {"key": "segments.create.created_after_label", "value": "Created After"}, {"key": "segments.create.created_before_label", "value": "Created Before"}, {"key": "segments.create.contact_preview_title", "value": "Contact Preview"}, {"key": "segments.create.showing_first_50", "value": "Showing first 50 of"}, {"key": "segments.create.contacts", "value": "contacts"}, {"key": "segments.create.contacts_match_criteria", "value": "contacts match these criteria"}, {"key": "segments.create.excluded", "value": "excluded"}, {"key": "segments.create.loading_preview", "value": "Loading contact preview..."}, {"key": "segments.create.table.contact_name", "value": "Contact Name"}, {"key": "segments.create.table.phone", "value": "Phone"}, {"key": "segments.create.table.email", "value": "Email"}, {"key": "segments.create.table.tags", "value": "Tags"}, {"key": "segments.create.table.created", "value": "Created"}, {"key": "segments.create.table.last_activity", "value": "Last Activity"}, {"key": "segments.create.table.actions", "value": "Actions"}, {"key": "segments.create.table.unknown", "value": "Unknown"}, {"key": "segments.create.table.no_activity", "value": "No activity"}, {"key": "segments.create.exclude_contact_tooltip", "value": "Exclude contact from segment"}, {"key": "segments.create.all_contacts_excluded", "value": "All contacts have been excluded from this segment"}, {"key": "segments.create.restore_all_contacts", "value": "Restore all contacts"}, {"key": "segments.create.no_contacts_match", "value": "No contacts match the current criteria"}, {"key": "segments.create.try_adjusting_filters", "value": "Try adjusting your filters"}, {"key": "segments.create.add_filter_criteria", "value": "Add filter criteria to preview contacts"}, {"key": "segments.create.select_tags_or_dates", "value": "Select tags or date ranges to see matching contacts"}, {"key": "segments.create.excluded_contacts_title", "value": "Excluded Contacts"}, {"key": "segments.create.restore_all", "value": "Restore All"}, {"key": "segments.create.restore_contact_tooltip", "value": "Restore contact"}, {"key": "segments.create.create_button", "value": "Create Segment"}, {"key": "segments.create.contact_excluded_title", "value": "Contact excluded"}, {"key": "segments.create.contact_excluded_desc", "value": "Contact has been removed from this segment preview"}, {"key": "segments.create.contact_restored_title", "value": "Contact restored"}, {"key": "segments.create.contact_restored_desc", "value": "Contact has been added back to the segment preview"}, {"key": "segments.create.name_required", "value": "Please enter a segment name"}, {"key": "segments.create.criteria_required", "value": "Please add at least one filter criteria"}, {"key": "segments.create.success", "value": "Segment created successfully"}, {"key": "segments.create.failed", "value": "Failed to create segment"}, {"key": "segments.edit.title", "value": "Edit Contact Segment"}, {"key": "segments.edit.loading_segment", "value": "Loading segment..."}, {"key": "segments.edit.load_failed", "value": "Failed to load segment"}, {"key": "segments.edit.name_label", "value": "Segment Name"}, {"key": "segments.edit.name_placeholder", "value": "e.g., VIP Customers, New Leads"}, {"key": "segments.edit.description_label", "value": "Description (Optional)"}, {"key": "segments.edit.description_placeholder", "value": "Describe this segment..."}, {"key": "segments.edit.filter_criteria_title", "value": "<PERSON><PERSON>"}, {"key": "segments.edit.contact_tags_label", "value": "Contact Tags"}, {"key": "segments.edit.tag_placeholder", "value": "Add a tag..."}, {"key": "segments.edit.created_after_label", "value": "Created After"}, {"key": "segments.edit.created_before_label", "value": "Created Before"}, {"key": "segments.edit.contact_preview_title", "value": "Contact Preview"}, {"key": "segments.edit.contact_excluded_title", "value": "Contact excluded"}, {"key": "segments.edit.contact_excluded_desc", "value": "Contact has been removed from this segment preview"}, {"key": "segments.edit.contact_restored_title", "value": "Contact restored"}, {"key": "segments.edit.contact_restored_desc", "value": "Contact has been added back to the segment preview"}, {"key": "segments.edit.validation_error", "value": "Validation Error"}, {"key": "segments.edit.name_required", "value": "Segment name is required"}, {"key": "segments.edit.update_success", "value": "Segment updated successfully"}, {"key": "segments.edit.update_failed", "value": "Failed to update segment"}, {"key": "segments.edit.delete_success", "value": "Segment deleted successfully"}, {"key": "segments.edit.delete_failed", "value": "Failed to delete segment"}, {"key": "segments.edit.delete_button", "value": "Delete Segment"}, {"key": "segments.edit.updating", "value": "Updating..."}, {"key": "segments.edit.update_button", "value": "Update Segment"}, {"key": "segments.edit.delete_confirm_title", "value": "Delete Segment"}, {"key": "segments.edit.delete_confirm_message", "value": "Are you sure you want to delete this segment? This action cannot be undone."}, {"key": "segments.edit.delete_confirm_button", "value": "Delete Segment"}, {"key": "segments.edit.contacts", "value": "contacts"}, {"key": "segments.edit.showing_first_50", "value": "Showing first 50 of"}, {"key": "segments.edit.contacts_match_criteria", "value": "contacts match these criteria"}, {"key": "segments.edit.excluded", "value": "excluded"}, {"key": "segments.edit.loading_preview", "value": "Loading contact preview..."}, {"key": "segments.edit.table.contact_name", "value": "Contact Name"}, {"key": "segments.edit.table.phone", "value": "Phone"}, {"key": "segments.edit.table.email", "value": "Email"}, {"key": "segments.edit.table.tags", "value": "Tags"}, {"key": "segments.edit.table.created", "value": "Created"}, {"key": "segments.edit.table.last_activity", "value": "Last Activity"}, {"key": "segments.edit.table.actions", "value": "Actions"}, {"key": "segments.edit.table.unknown", "value": "Unknown"}, {"key": "segments.edit.table.no_activity", "value": "No activity"}, {"key": "segments.edit.exclude_contact_tooltip", "value": "Exclude contact from segment"}, {"key": "segments.edit.all_contacts_excluded", "value": "All contacts have been excluded from this segment"}, {"key": "segments.edit.restore_all_contacts", "value": "Restore all contacts"}, {"key": "segments.edit.no_contacts_match", "value": "No contacts match the current criteria"}, {"key": "segments.edit.try_adjusting_filters", "value": "Try adjusting your filters"}, {"key": "segments.edit.add_filter_criteria", "value": "Add filter criteria to preview contacts"}, {"key": "segments.edit.select_tags_or_dates", "value": "Select tags or date ranges to see matching contacts"}, {"key": "segments.edit.excluded_contacts_title", "value": "Excluded Contacts"}, {"key": "segments.edit.restore_all", "value": "Restore All"}, {"key": "segments.edit.restore_contact_tooltip", "value": "Restore contact"}, {"key": "templates.create.title", "value": "Create Template"}, {"key": "templates.create.name_label", "value": "Template Name"}, {"key": "templates.create.name_placeholder", "value": "e.g., Welcome Message"}, {"key": "templates.create.category_label", "value": "Category"}, {"key": "templates.create.description_label", "value": "Description (Optional)"}, {"key": "templates.create.description_placeholder", "value": "Describe this template..."}, {"key": "templates.create.content_label", "value": "Template Content"}, {"key": "templates.create.content_placeholder", "value": "Enter your template content. Click 'Insert Variable' to add personalization..."}, {"key": "templates.create.media_files_label", "value": "Media Files (Optional)"}, {"key": "templates.create.add_media", "value": "Add Media"}, {"key": "templates.create.supported_files", "value": "Supported: Images (JPEG, PNG, WebP), Videos (MP4, 3GP), Audio (MP3, AAC, OGG), Documents (PDF, DOC, DOCX). Max 10MB per file."}, {"key": "templates.create.create_button", "value": "Create Template"}, {"key": "templates.create.file_size_error", "value": "File size must be less than 10MB"}, {"key": "templates.create.image_type_error", "value": "Only JPEG, PNG, and WebP images are allowed"}, {"key": "templates.create.video_type_error", "value": "Only MP4 and 3GP videos are allowed"}, {"key": "templates.create.audio_type_error", "value": "Only MP3, AAC, and OGG audio files are allowed"}, {"key": "templates.create.document_type_error", "value": "Only PDF, DOC, and DOCX documents are allowed"}, {"key": "templates.create.invalid_file", "value": "Invalid File"}, {"key": "templates.create.upload_failed", "value": "Upload failed"}, {"key": "templates.create.upload_file_failed", "value": "Failed to upload {{filename}}"}, {"key": "templates.create.name_required", "value": "Please enter a template name"}, {"key": "templates.create.content_required", "value": "Please enter template content"}, {"key": "templates.create.success", "value": "Template created successfully"}, {"key": "templates.create.failed", "value": "Failed to create template"}, {"key": "templates.edit.title", "value": "Edit Template"}, {"key": "templates.edit.loading_template", "value": "Loading template..."}, {"key": "templates.edit.load_failed", "value": "Failed to load template"}, {"key": "templates.edit.name_label", "value": "Template Name"}, {"key": "templates.edit.name_placeholder", "value": "e.g., Welcome Message, Promotion Alert"}, {"key": "templates.edit.description_label", "value": "Description (Optional)"}, {"key": "templates.edit.description_placeholder", "value": "Describe this template..."}, {"key": "templates.edit.category_label", "value": "Category"}, {"key": "templates.edit.category.general", "value": "General"}, {"key": "templates.edit.category.marketing", "value": "Marketing"}, {"key": "templates.edit.category.support", "value": "Support"}, {"key": "templates.edit.category.notification", "value": "Notification"}, {"key": "templates.edit.category.welcome", "value": "Welcome"}, {"key": "templates.edit.content_label", "value": "Message Content"}, {"key": "templates.edit.content_placeholder", "value": "Enter your message content here... Click 'Insert Variable' to add personalization..."}, {"key": "templates.edit.attached_media", "value": "Attached Media"}, {"key": "templates.edit.validation_error", "value": "Validation Error"}, {"key": "templates.edit.name_content_required", "value": "Name and content are required"}, {"key": "templates.edit.update_success", "value": "Template updated successfully"}, {"key": "templates.edit.update_failed", "value": "Failed to update template"}, {"key": "templates.edit.delete_success", "value": "Template deleted successfully"}, {"key": "templates.edit.delete_failed", "value": "Failed to delete template"}, {"key": "templates.edit.delete_button", "value": "Delete Template"}, {"key": "templates.edit.updating", "value": "Updating..."}, {"key": "templates.edit.update_button", "value": "Update Template"}, {"key": "templates.edit.delete_confirm_title", "value": "Delete Template"}, {"key": "templates.edit.delete_confirm_message", "value": "Are you sure you want to delete this template? This action cannot be undone."}, {"key": "templates.edit.delete_confirm_button", "value": "Delete Template"}, {"key": "variables.contact_name", "value": "Contact Name"}, {"key": "variables.contact_name_desc", "value": "Full name of the contact"}, {"key": "variables.phone_number", "value": "Phone Number"}, {"key": "variables.phone_number_desc", "value": "Contact phone number"}, {"key": "variables.email_address", "value": "Email Address"}, {"key": "variables.email_address_desc", "value": "Contact email address"}, {"key": "variables.company_name", "value": "Company Name"}, {"key": "variables.company_name_desc", "value": "Contact company or organization"}, {"key": "variables.current_date", "value": "Current Date"}, {"key": "variables.current_date_desc", "value": "Today's date"}, {"key": "variables.current_time", "value": "Current Time"}, {"key": "variables.current_time_desc", "value": "Current time"}, {"key": "variables.custom_variable_desc", "value": "Custom variable: {{variable}}"}, {"key": "variables.category.contact", "value": "Contact Information"}, {"key": "variables.category.custom", "value": "Custom Variables"}, {"key": "variables.category.system", "value": "System Variables"}, {"key": "variables.category.other", "value": "Other"}, {"key": "variables.insert_variable", "value": "Insert Variable"}, {"key": "variables.search_placeholder", "value": "Search variables..."}, {"key": "variables.no_variables_found", "value": "No variables found."}, {"key": "variables.help_text", "value": "Use variables like"}, {"key": "variables.for_personalization", "value": "for personalization"}, {"key": "contacts.avatar.refresh_tooltip", "value": "Refresh WhatsApp profile picture"}, {"key": "contacts.edit.title", "value": "Edit Contact"}, {"key": "contacts.edit.description", "value": "Make changes to the contact information below."}, {"key": "contacts.edit.contact_id_missing", "value": "Contact ID is missing"}, {"key": "contacts.edit.update_failed", "value": "Failed to update contact"}, {"key": "contacts.edit.success_title", "value": "Contact updated"}, {"key": "contacts.edit.success_description", "value": "The contact has been successfully updated."}, {"key": "contacts.edit.error_title", "value": "Update failed"}, {"key": "contacts.edit.name_label", "value": "Name"}, {"key": "contacts.edit.email_label", "value": "Email"}, {"key": "contacts.edit.phone_label", "value": "Phone"}, {"key": "contacts.edit.company_label", "value": "Company"}, {"key": "contacts.edit.channel_label", "value": "Channel"}, {"key": "contacts.edit.select_channel_placeholder", "value": "Select channel"}, {"key": "contacts.edit.channel.whatsapp_official", "value": "WhatsApp Official"}, {"key": "contacts.edit.channel.whatsapp_unofficial", "value": "WhatsApp Unofficial"}, {"key": "contacts.edit.channel.messenger", "value": "Facebook Messenger"}, {"key": "contacts.edit.channel.instagram", "value": "Instagram"}, {"key": "contacts.edit.channel_identifier_label", "value": "Channel Identifier"}, {"key": "contacts.edit.channel_identifier_placeholder", "value": "Phone number or ID"}, {"key": "contacts.edit.tags_label", "value": "Tags (comma separated)"}, {"key": "contacts.edit.tags_placeholder", "value": "lead, customer, etc."}, {"key": "contacts.edit.notes_label", "value": "Notes"}, {"key": "contacts.edit.saving", "value": "Saving..."}, {"key": "contacts.edit.save_changes", "value": "Save Changes"}, {"key": "agents.fetch_failed", "value": "Failed to fetch agents: {{status}} {{error}}"}, {"key": "agents.assign_failed", "value": "Failed to assign conversation"}, {"key": "agents.unassign_failed", "value": "Failed to unassign conversation"}, {"key": "agents.assigned_title", "value": "Conversation Assigned"}, {"key": "agents.unassigned_title", "value": "Conversation Unassigned"}, {"key": "agents.assigned_description", "value": "Conversation assigned to {{<PERSON><PERSON><PERSON>}}"}, {"key": "agents.unassigned_description", "value": "Conversation has been unassigned"}, {"key": "agents.assignment_failed_title", "value": "Assignment Failed"}, {"key": "agents.assignment_failed_description", "value": "Failed to update assignment"}, {"key": "agents.reassign_conversation", "value": "Reassign Conversation"}, {"key": "agents.assign_conversation", "value": "Assign Conversation"}, {"key": "agents.loading_agents", "value": "Loading agents..."}, {"key": "agents.error_loading_agents", "value": "Error loading agents"}, {"key": "agents.no_agents_available", "value": "No agents available"}, {"key": "agents.unassign", "value": "Unassign"}, {"key": "agents.unassigned", "value": "Unassigned"}, {"key": "agents.assign", "value": "Assign"}, {"key": "contacts.details.contact_updated_title", "value": "Contact updated"}, {"key": "contacts.details.contact_updated_description", "value": "Contact information has been updated."}, {"key": "contacts.details.fetch_notes_failed", "value": "Failed to fetch notes"}, {"key": "contacts.details.save_note_failed", "value": "Failed to save note"}, {"key": "contacts.details.note_saved_successfully", "value": "Note saved successfully"}, {"key": "contacts.details.channel.whatsapp", "value": "WhatsApp"}, {"key": "contacts.details.channel.whatsapp_business", "value": "WhatsApp Business"}, {"key": "contacts.details.channel.whatsapp_unofficial", "value": "WhatsApp (Unofficial)"}, {"key": "contacts.details.channel.messenger", "value": "<PERSON>"}, {"key": "contacts.details.channel.instagram", "value": "Instagram"}, {"key": "contacts.details.channel.chat", "value": "Cha<PERSON>"}, {"key": "contacts.details.unknown", "value": "Unknown"}, {"key": "contacts.details.close_details", "value": "Close contact details"}, {"key": "contacts.details.show_details", "value": "Show contact details"}, {"key": "contacts.details.title", "value": "Contact Details"}, {"key": "contacts.details.contact_information", "value": "Contact Information"}, {"key": "contacts.details.full_name", "value": "Full Name"}, {"key": "contacts.details.phone", "value": "Phone"}, {"key": "contacts.details.email", "value": "Email"}, {"key": "contacts.details.company", "value": "Company"}, {"key": "contacts.details.not_provided", "value": "Not provided"}, {"key": "contacts.details.edit_details", "value": "Edit details"}, {"key": "contacts.details.tags", "value": "Tags"}, {"key": "contacts.details.no_tags_added", "value": "No tags added"}, {"key": "contacts.details.conversation_details", "value": "Conversation Details"}, {"key": "contacts.details.first_contacted", "value": "First contacted"}, {"key": "contacts.details.channel", "value": "Channel"}, {"key": "messages.input.microphone_access_error", "value": "Could not access microphone. Please check permissions."}, {"key": "messages.input.microphone_denied", "value": "Microphone access denied. Please allow microphone access and try again."}, {"key": "messages.input.microphone_not_found", "value": "No microphone found. Please connect a microphone and try again."}, {"key": "messages.input.recording_error", "value": "Recording Error"}, {"key": "messages.input.no_recording", "value": "No recording to send"}, {"key": "messages.input.recording_too_short", "value": "Recording is too short or empty"}, {"key": "messages.input.voice_message_sent", "value": "Voice message sent"}, {"key": "messages.input.voice_message_failed", "value": "Failed to send voice message"}, {"key": "messages.input.send_message_failed", "value": "Failed to send message"}, {"key": "messages.input.bot_disabled", "value": "<PERSON><PERSON> Disabled"}, {"key": "messages.input.bot_enabled", "value": "Bot Enabled"}, {"key": "messages.input.messages_as_you", "value": "Messages will now be sent as you"}, {"key": "messages.input.messages_as_bot", "value": "Messages will now be sent as the bot assistant"}, {"key": "messages.input.file_too_large", "value": "File Too Large"}, {"key": "messages.input.max_file_size", "value": "Maximum file size is 10MB"}, {"key": "messages.input.record_voice_message", "value": "Record voice message"}, {"key": "messages.input.type_message", "value": "Type a message..."}, {"key": "messages.input.resume", "value": "Resume"}, {"key": "messages.input.pause", "value": "Pause"}, {"key": "messages.input.stop", "value": "Stop"}, {"key": "messages.input.send", "value": "Send"}, {"key": "conversations.item.channel.whatsapp", "value": "WhatsApp"}, {"key": "conversations.item.channel.messenger", "value": "<PERSON>"}, {"key": "conversations.item.channel.instagram", "value": "Instagram"}, {"key": "conversations.item.channel.email", "value": "Email"}, {"key": "conversations.item.channel.sms", "value": "SMS"}, {"key": "conversations.item.channel.web_chat", "value": "Web Chat"}, {"key": "conversations.item.channel.chat", "value": "Cha<PERSON>"}, {"key": "conversations.item.yesterday", "value": "Yesterday"}, {"key": "conversations.item.no_messages_yet", "value": "No messages yet"}, {"key": "conversations.item.sent_image", "value": "📷 Sent an image"}, {"key": "conversations.item.image", "value": "📷 Image"}, {"key": "conversations.item.sent_video", "value": "🎥 Sent a video"}, {"key": "conversations.item.video", "value": "🎥 Video"}, {"key": "conversations.item.sent_audio", "value": "🎵 Sent an audio"}, {"key": "conversations.item.audio", "value": "🎵 Audio"}, {"key": "conversations.item.sent_document", "value": "📄 Sent a document"}, {"key": "conversations.item.document", "value": "📄 Document"}, {"key": "conversations.item.conversation_with", "value": "Conversation with"}, {"key": "conversations.item.unread_messages", "value": "unread messages"}, {"key": "conversations.item.more", "value": "more"}, {"key": "conversations.item.new_lead", "value": "New Lead"}, {"key": "conversations.item.bot_active", "value": "Bo<PERSON> active"}, {"key": "conversations.item.awaiting_reply", "value": "Awaiting reply"}, {"key": "conversations.item.bot", "value": "Bot"}, {"key": "conversations.item.waiting", "value": "Waiting"}, {"key": "conversations.view.channel.whatsapp", "value": "WhatsApp"}, {"key": "conversations.view.channel.messenger", "value": "<PERSON>"}, {"key": "conversations.view.channel.instagram", "value": "Instagram"}, {"key": "conversations.view.channel.email", "value": "Email"}, {"key": "conversations.view.channel.sms", "value": "SMS"}, {"key": "conversations.view.channel.web_chat", "value": "Web Chat"}, {"key": "conversations.view.channel.chat", "value": "Cha<PERSON>"}, {"key": "conversations.view.add_contact", "value": "Add Contact"}, {"key": "conversations.view.start_call", "value": "Start Call"}, {"key": "conversations.view.schedule", "value": "Schedule"}, {"key": "conversations.view.more_options", "value": "More options"}, {"key": "contacts.edit_dialog.contact_id_missing", "value": "Contact ID is missing"}, {"key": "contacts.edit_dialog.update_failed", "value": "Failed to update contact"}, {"key": "contacts.edit_dialog.success_title", "value": "Contact updated"}, {"key": "contacts.edit_dialog.success_description", "value": "The contact has been successfully updated."}, {"key": "contacts.edit_dialog.error_title", "value": "Update failed"}, {"key": "contacts.edit_dialog.invalid_file_type", "value": "Invalid file type"}, {"key": "contacts.edit_dialog.invalid_file_type_desc", "value": "Please select a valid image file (JPEG, PNG, GIF, or WebP)."}, {"key": "contacts.edit_dialog.file_too_large", "value": "File too large"}, {"key": "contacts.edit_dialog.file_too_large_desc", "value": "File size is {{sizeMB}}MB. Please select an image smaller than 5MB."}, {"key": "contacts.edit_dialog.image_too_large", "value": "Image too large"}, {"key": "contacts.edit_dialog.image_too_large_desc", "value": "Image dimensions are {{width}}x{{height}}. Please use an image smaller than {{maxDimension}}x{{maxDimension}} pixels."}, {"key": "contacts.edit_dialog.image_selected", "value": "Image selected"}, {"key": "contacts.edit_dialog.image_selected_desc", "value": "Profile picture has been selected successfully."}, {"key": "contacts.edit_dialog.error_reading_file", "value": "Error reading file"}, {"key": "contacts.edit_dialog.error_reading_file_desc", "value": "Failed to read the selected image file."}, {"key": "contacts.edit_dialog.invalid_image", "value": "Invalid image"}, {"key": "contacts.edit_dialog.invalid_image_desc", "value": "The selected file is not a valid image."}, {"key": "contacts.edit_dialog.unexpected_error", "value": "An unexpected error occurred while processing the image."}, {"key": "contacts.edit_dialog.image_removed", "value": "Image removed"}, {"key": "contacts.edit_dialog.image_removed_desc", "value": "Profile picture has been removed."}, {"key": "contacts.edit_dialog.upload_disabled", "value": "Upload disabled"}, {"key": "contacts.edit_dialog.upload_disabled_desc", "value": "File upload is currently disabled."}, {"key": "contacts.edit_dialog.upload_error", "value": "Upload error"}, {"key": "contacts.edit_dialog.upload_not_available", "value": "File upload is not available. Please try refreshing the page."}, {"key": "contacts.edit_dialog.upload_failed", "value": "Failed to open file picker. Please try again."}, {"key": "contacts.edit_dialog.validation_error", "value": "Validation error"}, {"key": "contacts.edit_dialog.name_required", "value": "Contact name is required."}, {"key": "contacts.edit_dialog.invalid_email", "value": "Please enter a valid email address."}, {"key": "contacts.edit_dialog.invalid_phone", "value": "Please enter a valid phone number (7-20 digits)."}, {"key": "contacts.edit_dialog.title", "value": "Edit Contact Details"}, {"key": "contacts.edit_dialog.processing", "value": "Processing..."}, {"key": "contacts.edit_dialog.upload_photo", "value": "Upload Photo"}, {"key": "contacts.edit_dialog.sync_from_whatsapp", "value": "Sync from WhatsApp"}, {"key": "contacts.edit_dialog.full_name_required", "value": "Full Name *"}, {"key": "contacts.edit_dialog.enter_full_name", "value": "Enter full name"}, {"key": "contacts.edit_dialog.email", "value": "Email"}, {"key": "contacts.edit_dialog.enter_email", "value": "Enter email address"}, {"key": "contacts.edit_dialog.phone_number", "value": "Phone Number"}, {"key": "contacts.edit_dialog.enter_phone", "value": "Enter phone number"}, {"key": "contacts.edit_dialog.company", "value": "Company"}, {"key": "contacts.edit_dialog.enter_company", "value": "Enter company name"}, {"key": "contacts.edit_dialog.primary_channel", "value": "Primary Channel"}, {"key": "contacts.edit_dialog.select_channel", "value": "Select channel"}, {"key": "contacts.edit_dialog.whatsapp_official", "value": "WhatsApp Official"}, {"key": "contacts.edit_dialog.whatsapp_unofficial", "value": "WhatsApp Unofficial"}, {"key": "contacts.edit_dialog.facebook_messenger", "value": "Facebook Messenger"}, {"key": "contacts.edit_dialog.instagram", "value": "Instagram"}, {"key": "contacts.edit_dialog.channel_identifier", "value": "Channel Identifier"}, {"key": "contacts.edit_dialog.phone_username_id", "value": "Phone number, username, or ID"}, {"key": "contacts.edit_dialog.tags", "value": "Tags"}, {"key": "contacts.edit_dialog.enter_tags", "value": "Enter tags separated by commas (e.g., lead, customer, vip)"}, {"key": "contacts.edit_dialog.tags_help", "value": "Separate multiple tags with commas"}, {"key": "contacts.edit_dialog.notes", "value": "Notes"}, {"key": "contacts.edit_dialog.add_notes", "value": "Add any additional notes about this contact..."}, {"key": "contacts.edit_dialog.saving", "value": "Saving..."}, {"key": "contacts.edit_dialog.save_changes", "value": "Save Changes"}, {"key": "contacts.edit_dialog.unsaved_changes", "value": "Unsaved Changes"}, {"key": "contacts.edit_dialog.unsaved_changes_message", "value": "You have unsaved changes. Are you sure you want to close without saving?"}, {"key": "contacts.edit_dialog.continue_editing", "value": "Continue Editing"}, {"key": "contacts.edit_dialog.discard_changes", "value": "Discard Changes"}, {"key": "contacts.edit_dialog.upload_profile_picture_failed", "value": "Failed to upload profile picture"}, {"key": "contacts.edit_dialog.profile_picture_updated", "value": "Profile picture updated"}, {"key": "contacts.edit_dialog.profile_picture_uploaded", "value": "The profile picture has been uploaded successfully."}, {"key": "contacts.edit_dialog.profile_picture_upload_failed", "value": "Profile picture upload failed"}, {"key": "contacts.edit_dialog.contact_updated_upload_failed", "value": "Contact was updated but profile picture upload failed."}, {"key": "contacts.edit_dialog.profile_preview", "value": "Profile preview"}, {"key": "media_upload.no_file_selected", "value": "No media file selected"}, {"key": "media_upload.sent_successfully", "value": "Media message sent successfully"}, {"key": "media_upload.error_sending", "value": "Error Sending Media"}, {"key": "media_upload.send_failed", "value": "Failed to send media message"}, {"key": "media_upload.image_preview", "value": "Image preview"}, {"key": "media_upload.file_size_kb", "value": "{{size}} KB"}, {"key": "media_upload.title", "value": "Send Media"}, {"key": "media_upload.description", "value": "Preview and send media files through WhatsApp"}, {"key": "media_upload.caption_placeholder", "value": "Add a caption (optional)"}, {"key": "media_upload.sending", "value": "Sending..."}, {"key": "media_upload.send", "value": "Send"}, {"key": "message_bubble.download_failed", "value": "Failed to download media"}, {"key": "message_bubble.media_downloaded", "value": "Media downloaded"}, {"key": "message_bubble.download_success", "value": "Media has been downloaded successfully"}, {"key": "message_bubble.download_failed_title", "value": "Download failed"}, {"key": "message_bubble.image_message", "value": "Image message"}, {"key": "message_bubble.downloading", "value": "Downloading..."}, {"key": "message_bubble.download_image", "value": "Download Image"}, {"key": "message_bubble.download_video", "value": "Download Video"}, {"key": "message_bubble.video_not_supported", "value": "Your browser does not support the video element."}, {"key": "message_bubble.audio_not_supported", "value": "Your browser does not support the audio element."}, {"key": "message_bubble.audio_message", "value": "Audio message"}, {"key": "message_bubble.loading", "value": "Loading..."}, {"key": "message_bubble.download", "value": "Download"}, {"key": "message_bubble.document", "value": "Document"}, {"key": "message_bubble.open", "value": "Open"}, {"key": "message_bubble.reply", "value": "Reply to this message"}, {"key": "message_bubble.delete", "value": "Delete this message"}, {"key": "message_bubble.delete_failed", "value": "Failed to delete message"}, {"key": "message_bubble.delete_failed_title", "value": "Delete failed"}, {"key": "message_bubble.message_deleted", "value": "Message deleted"}, {"key": "message_bubble.delete_success", "value": "Message has been deleted successfully"}, {"key": "message_bubble.confirm_delete_title", "value": "Delete Message"}, {"key": "message_bubble.confirm_delete_message", "value": "Are you sure you want to delete this message? This action cannot be undone."}, {"key": "message_bubble.media_message", "value": "Media message"}, {"key": "message_bubble.deleting", "value": "Deleting..."}, {"key": "message_bubble.delete_confirm", "value": "Delete"}, {"key": "message_input.replying_to", "value": "Replying to"}, {"key": "message_input.cancel_reply", "value": "<PERSON><PERSON> reply"}, {"key": "message_input.reply_failed", "value": "Failed to send reply"}, {"key": "message_input.reply_sent", "value": "Reply sent"}, {"key": "message_input.reply_success", "value": "Your reply has been sent successfully"}, {"key": "channel.reply_not_supported", "value": "Replies are not supported for this channel"}, {"key": "channel.delete_not_supported", "value": "Message deletion is not supported for this channel"}, {"key": "channel.message_too_old", "value": "Message is too old to be deleted"}, {"key": "channel.whatsapp_delete_limit", "value": "WhatsApp messages can only be deleted within 3 days"}, {"key": "message_bubble.sticker", "value": "<PERSON>er"}, {"key": "message_bubble.link_preview", "value": "Link preview"}, {"key": "message_bubble.contact_avatar", "value": "Contact avatar"}, {"key": "message_bubble.bot", "value": "Bot"}, {"key": "message_bubble.powerchat_assistant", "value": "{{appName}} Assistant"}, {"key": "clear_history.confirm_message", "value": "Are you sure you want to clear all chat history with \"{{conversationName}}\"? This will permanently delete all messages and media files from {{appName}}."}, {"key": "clear_history.confirm_group_message", "value": "Are you sure you want to clear all chat history for \"{{conversationName}}\"? This will permanently delete all messages and media files from {{appName}}."}, {"key": "clear_history.warning_title", "value": "Important:"}, {"key": "clear_history.warning_irreversible", "value": "This action cannot be undone"}, {"key": "clear_history.warning_local_only", "value": "Messages will only be deleted from {{appName}}, not from WhatsApp"}, {"key": "clear_history.warning_media", "value": "All associated media files will be permanently deleted"}, {"key": "new_conversation.name_required", "value": "Contact name is required"}, {"key": "new_conversation.phone_required", "value": "Phone number is required"}, {"key": "new_conversation.phone_invalid", "value": "Please enter a valid phone number with at least 10 digits"}, {"key": "new_conversation.connection_required", "value": "Please select a WhatsApp connection"}, {"key": "new_conversation.no_connections", "value": "No active WhatsApp connections found. Please connect WhatsApp in Settings first."}, {"key": "new_conversation.create_failed", "value": "Failed to create conversation"}, {"key": "new_conversation.success_message", "value": "WhatsApp conversation initiated successfully."}, {"key": "new_conversation.create_error", "value": "Failed to create conversation. Please try again."}, {"key": "new_conversation.no_connection_selected", "value": "No Connection Selected"}, {"key": "new_conversation.select_connection", "value": "Please select a WhatsApp connection."}, {"key": "new_conversation.title", "value": "Start New WhatsApp Conversation"}, {"key": "new_conversation.description", "value": "Enter contact details to initiate a new WhatsApp conversation."}, {"key": "new_conversation.contact_name_required", "value": "Contact Name *"}, {"key": "new_conversation.enter_contact_name", "value": "Enter contact's full name"}, {"key": "new_conversation.phone_number_required", "value": "Phone Number *"}, {"key": "new_conversation.enter_phone_number", "value": "Enter phone number (e.g., +1234567890)"}, {"key": "new_conversation.include_country_code", "value": "Include country code for international numbers"}, {"key": "new_conversation.whatsapp_connection_required", "value": "WhatsApp Connection *"}, {"key": "new_conversation.loading_connections", "value": "Loading connections..."}, {"key": "new_conversation.select_whatsapp_connection", "value": "Select WhatsApp connection"}, {"key": "new_conversation.no_connections_available", "value": "No active WhatsApp connections available"}, {"key": "new_conversation.initial_message_optional", "value": "Initial Message (Optional)"}, {"key": "new_conversation.enter_initial_message", "value": "Enter an optional first message to send..."}, {"key": "new_conversation.initial_message_help", "value": "This message will be sent immediately after creating the conversation"}, {"key": "new_conversation.creating", "value": "Creating..."}, {"key": "new_conversation.start_conversation", "value": "Start Conversation"}, {"key": "inbox.failed_load_messages", "value": "Failed to load conversation messages"}, {"key": "inbox.conversation_assigned", "value": "Conversation Assigned"}, {"key": "inbox.conversation_assigned_to_agent", "value": "Conversation has been assigned to an agent"}, {"key": "inbox.conversation_unassigned", "value": "Conversation has been unassigned"}, {"key": "inbox.conversation_unassigned_desc", "value": "Conversation has been unassigned"}, {"key": "inbox.new_conversation", "value": "New Conversation"}, {"key": "inbox.not_connected", "value": "Not Connected"}, {"key": "inbox.cannot_send_message", "value": "Cannot send message, not connected to server"}, {"key": "header.connection_active", "value": "Real-time connection active"}, {"key": "header.connection_lost", "value": "Real-time connection lost"}, {"key": "header.connected", "value": "Connected"}, {"key": "header.disconnected", "value": "Disconnected"}, {"key": "sidebar.expand", "value": "Expand sidebar"}, {"key": "sidebar.collapse", "value": "Collapse sidebar"}, {"key": "auth.password_reset_successful", "value": "Password reset successful"}, {"key": "auth.error", "value": "Error"}, {"key": "auth.invalid_reset_token", "value": "Invalid reset token"}, {"key": "auth.fill_all_fields", "value": "Please fill in all fields"}, {"key": "auth.passwords_do_not_match", "value": "Passwords do not match"}, {"key": "auth.password_min_length", "value": "Password must be at least 6 characters long"}, {"key": "auth.validating_token", "value": "Validating reset token..."}, {"key": "auth.invalid_reset_link", "value": "Invalid Reset Link"}, {"key": "auth.reset_link_expired", "value": "This password reset link is invalid or has expired."}, {"key": "auth.reset_link_invalid", "value": "The reset link is no longer valid."}, {"key": "auth.request_new_reset_link", "value": "Request new reset link"}, {"key": "auth.back_to_login", "value": "Back to login"}, {"key": "auth.password_reset_success_title", "value": "Password Reset Successful"}, {"key": "auth.password_reset_success_desc", "value": "Your password has been successfully reset. You can now log in with your new password."}, {"key": "auth.password_reset_success_message", "value": "You can now use your new password to access your account."}, {"key": "auth.continue_to_login", "value": "Continue to login"}, {"key": "auth.reset_your_password", "value": "Reset your password"}, {"key": "auth.enter_new_password", "value": "Enter a new password for your account."}, {"key": "auth.new_password", "value": "New password"}, {"key": "auth.new_password_placeholder", "value": "Enter your new password"}, {"key": "auth.confirm_new_password", "value": "Confirm new password"}, {"key": "auth.confirm_new_password_placeholder", "value": "Confirm your new password"}, {"key": "auth.reset_password_button", "value": "Reset password"}, {"key": "admin.translations.create_key_failed", "value": "Failed to create translation key"}, {"key": "admin.translations.key_created", "value": "Translation key created"}, {"key": "admin.translations.key_created_desc", "value": "The translation key has been created successfully."}, {"key": "admin.translations.add_key", "value": "Add Translation Key"}, {"key": "admin.translations.add_new_key", "value": "Add New Translation Key"}, {"key": "admin.translations.add_key_desc", "value": "Add a new translation key to the selected namespace."}, {"key": "admin.translations.key_label", "value": "Key"}, {"key": "admin.translations.key_placeholder", "value": "welcome_message"}, {"key": "admin.translations.description_label", "value": "Description"}, {"key": "admin.translations.description_placeholder", "value": "Welcome message shown on the homepage"}, {"key": "admin.translations.creating", "value": "Creating..."}, {"key": "admin.translations.create_key_button", "value": "Create Key"}, {"key": "admin.languages.active_label", "value": "Active"}, {"key": "admin.languages.enable_language", "value": "Enable this language"}, {"key": "admin.languages.updating", "value": "Updating..."}, {"key": "admin.languages.update_language", "value": "Update Language"}, {"key": "admin.users.super_admin", "value": "Super Admin"}, {"key": "admin.users.system", "value": "System"}, {"key": "admin.users.actions", "value": "Actions"}, {"key": "admin.users.edit_user", "value": "Edit User"}, {"key": "admin.users.reset_password", "value": "Reset Password"}, {"key": "settings.whatsapp.settings_saved", "value": "Settings Saved"}, {"key": "settings.whatsapp.settings_saved_desc", "value": "WhatsApp behavior settings have been updated successfully."}, {"key": "settings.whatsapp.save_failed", "value": "Failed to save WhatsApp behavior settings. Please try again."}, {"key": "settings.whatsapp.typing_indicators", "value": "Typing Indicators"}, {"key": "settings.whatsapp.typing_indicators_desc", "value": "Configure human-like typing indicators that appear before sending messages"}, {"key": "settings.whatsapp.enable_typing", "value": "Enable Typing Indicators"}, {"key": "settings.whatsapp.enable_typing_desc", "value": "Show \"Typing...\" and \"Recording...\" indicators before sending messages"}, {"key": "settings.whatsapp.preview", "value": "Preview"}, {"key": "settings.whatsapp.preview_desc", "value": "See how your settings will affect message delivery"}, {"key": "settings.whatsapp.sample_message", "value": "Sample Message"}, {"key": "settings.whatsapp.sample_message_placeholder", "value": "Enter a sample message to see how it will be split and timed..."}, {"key": "common.success", "value": "Success"}, {"key": "common.error", "value": "Error"}, {"key": "common.cancel", "value": "Cancel"}, {"key": "common.edit", "value": "Edit"}, {"key": "common.save", "value": "Save"}, {"key": "common.loading", "value": "Loading..."}, {"key": "common.please_wait", "value": "Please wait..."}, {"key": "common.back", "value": "Back"}, {"key": "common.delete_item", "value": "Delete Item"}, {"key": "common.delete_confirmation", "value": "Are you sure you want to delete this item? This action cannot be undone."}, {"key": "common.delete", "value": "Delete"}, {"key": "common.deleting", "value": "Deleting..."}, {"key": "payment.processing", "value": "Payment Processing"}, {"key": "payment.successful", "value": "Payment Successful"}, {"key": "payment.failed", "value": "Payment Failed"}, {"key": "payment.verification_failed", "value": "Payment Verification Failed"}, {"key": "payment.verifying", "value": "We're verifying your payment..."}, {"key": "payment.subscription_activated", "value": "Your subscription has been activated successfully."}, {"key": "payment.could_not_process", "value": "Your payment could not be processed. Please try again."}, {"key": "payment.contact_support", "value": "We couldn't verify your payment. Please contact support."}, {"key": "payment.processed_successfully", "value": "Your payment has been processed successfully."}, {"key": "payment.missing_info", "value": "Missing payment information. Please contact support if you completed a payment."}, {"key": "payment.details", "value": "Payment Details"}, {"key": "payment.payment_id", "value": "Payment ID:"}, {"key": "payment.transaction_id", "value": "Transaction ID:"}, {"key": "payment.payment_method", "value": "Payment Method:"}, {"key": "payment.status", "value": "Status:"}, {"key": "payment.try_again", "value": "Try Again"}, {"key": "payment.return_to_settings", "value": "Return to Settings"}, {"key": "payment.go_to_inbox", "value": "Go to Inbox"}, {"key": "subdomain.company_not_found", "value": "Company Not Found"}, {"key": "subdomain.no_company_found", "value": "No company found for subdomain \"{{subdomain}}\""}, {"key": "subdomain.company_not_exist", "value": "The company you're trying to access either doesn't exist or has been removed."}, {"key": "subdomain.account_inactive", "value": "Company Account Inactive"}, {"key": "subdomain.account_inactive_desc", "value": "This company account is currently inactive"}, {"key": "subdomain.account_disabled", "value": "The company account has been temporarily disabled. Please contact support for assistance."}, {"key": "subdomain.invalid_subdomain", "value": "Invalid Subdomain"}, {"key": "subdomain.invalid_subdomain_desc", "value": "\"{{subdomain}}\" is not a valid subdomain"}, {"key": "subdomain.subdomain_rules", "value": "Subdomains can only contain letters, numbers, and hyphens."}, {"key": "subdomain.connection_error", "value": "Connection Error"}, {"key": "subdomain.unable_verify", "value": "Unable to verify company information"}, {"key": "subdomain.server_problem", "value": "There was a problem connecting to the server. Please try again."}, {"key": "subdomain.access_error", "value": "Access Error"}, {"key": "subdomain.unable_access", "value": "Unable to access this company"}, {"key": "subdomain.unexpected_error", "value": "An unexpected error occurred."}, {"key": "subdomain.subdomain_label", "value": "Subdomain:"}, {"key": "subdomain.url_label", "value": "URL:"}, {"key": "subdomain.go_to_main_site", "value": "Go to Main Site"}, {"key": "subdomain.go_back", "value": "Go Back"}, {"key": "subdomain.different_company_help", "value": "Looking for a different company? Make sure you have the correct subdomain URL."}, {"key": "subdomain.admin_contact_help", "value": "If you're a company administrator, please contact support to reactivate your account."}, {"key": "whatsapp_360dialog.validation_error", "value": "Validation Error"}, {"key": "whatsapp_360dialog.api_key_required", "value": "API Key and Phone Number are required for validation."}, {"key": "whatsapp_360dialog.credentials_valid", "value": "Credentials Valid"}, {"key": "whatsapp_360dialog.validation_success", "value": "Successfully validated 360Dialog API key for phone number: {{phoneNumber}}"}, {"key": "whatsapp_360dialog.invalid_credentials", "value": "Invalid credentials"}, {"key": "whatsapp_360dialog.validation_failed", "value": "Validation Failed"}, {"key": "whatsapp_360dialog.validation_failed_desc", "value": "Failed to validate 360Dialog credentials."}, {"key": "whatsapp_360dialog.success", "value": "Success"}, {"key": "whatsapp_360dialog.connection_created", "value": "360Dialog WhatsApp connection created successfully!"}, {"key": "whatsapp_360dialog.connection_failed", "value": "Failed to create connection"}, {"key": "whatsapp_360dialog.connection_error", "value": "Connection Error"}, {"key": "whatsapp_360dialog.connection_error_desc", "value": "Failed to connect to 360Dialog WhatsApp"}, {"key": "whatsapp_360dialog.connect_title", "value": "Connect 360Dialog WhatsApp"}, {"key": "whatsapp_360dialog.connection_name", "value": "Connection Name"}, {"key": "whatsapp_360dialog.connection_name_placeholder", "value": "My 360Dialog WhatsApp"}, {"key": "whatsapp_360dialog.connection_name_help", "value": "A friendly name for this connection"}, {"key": "whatsapp_360dialog.api_key", "value": "API Key"}, {"key": "whatsapp_360dialog.api_key_placeholder", "value": "Your 360Dialog API key..."}, {"key": "whatsapp_360dialog.api_key_help", "value": "Your 360Dialog API key from the Hub dashboard"}, {"key": "whatsapp_360dialog.phone_number", "value": "WhatsApp Phone Number"}, {"key": "whatsapp_360dialog.phone_number_placeholder", "value": "+1234567890"}, {"key": "whatsapp_360dialog.phone_number_help", "value": "Your WhatsApp Business phone number registered with 360Dialog"}, {"key": "whatsapp_360dialog.validating", "value": "Validating..."}, {"key": "whatsapp_360dialog.test", "value": "Test"}, {"key": "whatsapp_360dialog.webhook_url", "value": "Webhook URL"}, {"key": "whatsapp_360dialog.webhook_url_placeholder", "value": "https://yourdomain.com/api/webhooks/360dialog-whatsapp"}, {"key": "whatsapp_360dialog.webhook_url_help", "value": "URL where 360Dialog will send webhook notifications"}, {"key": "whatsapp_360dialog.setup_title", "value": "360Dialog WhatsApp Setup"}, {"key": "whatsapp_360dialog.setup_steps", "value": "1. Create a 360Dialog account and get WhatsApp Business approved\\n2. Generate an API key in the 360Dialog Hub\\n3. Configure the webhook URL in your 360Dialog settings\\n4. Test the connection to ensure everything works"}, {"key": "whatsapp_360dialog.connecting", "value": "Connecting..."}, {"key": "whatsapp_360dialog.connect", "value": "Connect"}, {"key": "admin.languages.create_failed", "value": "Failed to create language"}, {"key": "admin.languages.language_created", "value": "Language created"}, {"key": "admin.languages.language_created_desc", "value": "The language has been created successfully."}, {"key": "admin.languages.add_language", "value": "Add Language"}, {"key": "admin.languages.add_new_language", "value": "Add New Language"}, {"key": "admin.languages.add_language_desc", "value": "Create a new language for the application."}, {"key": "admin.languages.code_label", "value": "Code"}, {"key": "admin.languages.code_placeholder", "value": "en"}, {"key": "admin.languages.name_label", "value": "Name"}, {"key": "admin.languages.name_placeholder", "value": "English"}, {"key": "admin.languages.native_name_label", "value": "Native Name"}, {"key": "admin.languages.native_name_placeholder", "value": "English"}, {"key": "admin.languages.flag_icon_label", "value": "Flag Icon"}, {"key": "admin.languages.flag_icon_placeholder", "value": "🇺🇸"}, {"key": "admin.languages.direction_label", "value": "Direction"}, {"key": "admin.languages.select_direction", "value": "Select direction"}, {"key": "admin.languages.ltr", "value": "Left to Right"}, {"key": "admin.languages.rtl", "value": "Right to Left"}, {"key": "admin.languages.default_label", "value": "<PERSON><PERSON><PERSON>"}, {"key": "admin.languages.set_default", "value": "Set as default language"}, {"key": "admin.languages.creating", "value": "Creating..."}, {"key": "admin.languages.create_language", "value": "Create Language"}, {"key": "admin.languages.edit_language", "value": "Edit Language"}, {"key": "admin.languages.edit_language_desc", "value": "Update the language settings."}, {"key": "admin.languages.update_failed", "value": "Failed to update language"}, {"key": "admin.languages.language_updated", "value": "Language updated"}, {"key": "admin.languages.language_updated_desc", "value": "The language has been updated successfully."}, {"key": "admin.namespaces.create_failed", "value": "Failed to create namespace"}, {"key": "admin.namespaces.namespace_created", "value": "Namespace created"}, {"key": "admin.namespaces.namespace_created_desc", "value": "The namespace has been created successfully."}, {"key": "admin.namespaces.add_namespace", "value": "Add Namespace"}, {"key": "admin.namespaces.add_new_namespace", "value": "Add New Namespace"}, {"key": "admin.namespaces.add_namespace_desc", "value": "Create a new namespace for organizing translations."}, {"key": "admin.namespaces.name_placeholder", "value": "common"}, {"key": "admin.namespaces.description_placeholder", "value": "Common translations used throughout the application"}, {"key": "admin.namespaces.create_namespace", "value": "Create Namespace"}, {"key": "admin.translations.export_array", "value": "Export Array"}, {"key": "admin.translations.export_nested", "value": "Export Nested"}, {"key": "admin.translations.import_translations", "value": "Import Translations"}, {"key": "admin.translations.import_translations_desc", "value": "Import translations from a JSON file. Supports both array format and nested format."}, {"key": "admin.translations.select_language_file", "value": "Please select a language and a file"}, {"key": "admin.translations.import_failed", "value": "Failed to import translations"}, {"key": "admin.translations.translations_imported", "value": "Translations imported"}, {"key": "admin.translations.translations_imported_desc", "value": "The translations have been imported successfully."}, {"key": "admin.translations.language_label", "value": "Language"}, {"key": "admin.translations.select_language", "value": "Select language"}, {"key": "admin.translations.file_label", "value": "File"}, {"key": "admin.translations.importing", "value": "Importing..."}, {"key": "email.validation_error", "value": "Validation Error"}, {"key": "email.fill_required_fields", "value": "Please fill in all required fields"}, {"key": "email.provide_passwords_oauth", "value": "Please provide passwords or configure OAuth2"}, {"key": "email.connection_test_successful", "value": "Connection Test Successful"}, {"key": "email.credentials_valid", "value": "Email server credentials are valid"}, {"key": "email.connection_test_failed", "value": "Connection Test Failed"}, {"key": "email.failed_connect_server", "value": "Failed to connect to email server"}, {"key": "email.connection_test_error", "value": "Connection Test Error"}, {"key": "email.failed_test_connection", "value": "Failed to test email connection"}, {"key": "email.create_connection_failed", "value": "Failed to create email channel connection"}, {"key": "email.configure_settings_failed", "value": "Failed to configure email settings"}, {"key": "email.channel_connected_success", "value": "Email channel connected and polling started successfully!"}, {"key": "email.configuration_successful", "value": "Configuration Successful"}, {"key": "email.configured_connection_failed", "value": "Email channel configured but connection failed: {{error}}. You can try connecting manually from Settings."}, {"key": "email.connection_error", "value": "Connection Error"}, {"key": "email.failed_create_connection", "value": "Failed to create email connection"}, {"key": "email.connect_email_channel", "value": "Connect Email Channel"}, {"key": "email.basic_configuration", "value": "Basic Configuration"}, {"key": "email.connection_name", "value": "Connection Name"}, {"key": "email.connection_name_placeholder", "value": "My Email Support"}, {"key": "email.connection_name_help", "value": "A friendly name for this email connection"}, {"key": "email.email_address", "value": "Email Address"}, {"key": "email.email_address_placeholder", "value": "<EMAIL>"}, {"key": "email.email_address_help", "value": "The email address for sending and receiving messages"}, {"key": "email.imap_settings", "value": "IMAP Settings (Receiving)"}, {"key": "email.imap_host", "value": "IMAP Host"}, {"key": "email.imap_host_placeholder", "value": "imap.gmail.com"}, {"key": "email.imap_port", "value": "IMAP Port"}, {"key": "email.use_ssl_tls_imap", "value": "Use SSL/TLS for IMAP"}, {"key": "email.imap_username", "value": "IMAP Username (optional)"}, {"key": "email.imap_username_placeholder", "value": "Leave empty to use email address"}, {"key": "email.imap_password", "value": "IMAP Password"}, {"key": "email.password_placeholder", "value": "Your email password or app password"}, {"key": "email.smtp_settings", "value": "SMTP Settings (Sending)"}, {"key": "email.smtp_host", "value": "SMTP Host"}, {"key": "email.smtp_host_placeholder", "value": "smtp.gmail.com"}, {"key": "email.smtp_port", "value": "SMTP Port"}, {"key": "email.use_ssl_tls_smtp", "value": "Use SSL/TLS for SMTP"}, {"key": "email.smtp_username", "value": "SMTP Username (optional)"}, {"key": "email.smtp_username_placeholder", "value": "Leave empty to use email address"}, {"key": "email.smtp_password", "value": "SMTP Password"}, {"key": "email.advanced_settings", "value": "Advanced Settings"}, {"key": "email.sync_folder", "value": "Sync Folder"}, {"key": "email.sync_folder_placeholder", "value": "INBOX"}, {"key": "email.sync_folder_help", "value": "Email folder to monitor for new messages"}, {"key": "email.max_sync_messages", "value": "Max Messages to Sync"}, {"key": "email.max_sync_messages_help", "value": "Maximum number of messages to sync per check"}, {"key": "messenger.title", "value": "<PERSON>"}, {"key": "messenger.conversations", "value": "Conversations"}, {"key": "messenger.compose", "value": "Compose Message"}, {"key": "messenger.folders.all", "value": "All Conversations"}, {"key": "messenger.folders.unread", "value": "Unread"}, {"key": "messenger.folders.starred", "value": "Starred"}, {"key": "messenger.folders.archived", "value": "Archived"}, {"key": "messenger.quick_actions", "value": "Quick Actions"}, {"key": "messenger.settings", "value": "Settings"}, {"key": "messenger.last_sync", "value": "Last sync"}, {"key": "messenger.status.connected", "value": "Connected"}, {"key": "messenger.status.disconnected", "value": "Disconnected"}, {"key": "messenger.status.connecting", "value": "Connecting"}, {"key": "messenger.status.error", "value": "Error"}, {"key": "messenger.no_channel", "value": "No Channel Selected"}, {"key": "messenger.select_channel", "value": "Please select a Messenger channel to continue"}, {"key": "messenger.search_conversations", "value": "Search conversations..."}, {"key": "messenger.no_conversations", "value": "No Conversations"}, {"key": "messenger.no_conversations_desc", "value": "Start a conversation to see it here"}, {"key": "messenger.end_of_conversations", "value": "You've reached the end of your conversations"}, {"key": "messenger.loading_messages", "value": "Loading messages..."}, {"key": "messenger.select_conversation", "value": "Select a Conversation"}, {"key": "messenger.choose_conversation", "value": "Choose a conversation from the list to start messaging"}, {"key": "messenger.online", "value": "Online"}, {"key": "messenger.offline", "value": "Offline"}, {"key": "messenger.last_seen", "value": "Last seen {{time}}"}, {"key": "messenger.view_profile", "value": "View Profile"}, {"key": "messenger.mute_conversation", "value": "Mute Conversation"}, {"key": "messenger.block_contact", "value": "Block Contact"}, {"key": "messenger.type_message", "value": "Type a message..."}, {"key": "messenger.sent", "value": "Message Sent"}, {"key": "messenger.message_sent_successfully", "value": "Your message has been sent successfully"}, {"key": "messenger.error", "value": "Error"}, {"key": "messenger.recipient_required", "value": "Recipient is required"}, {"key": "messenger.message_required", "value": "Message content is required"}, {"key": "messenger.send_error", "value": "Send Error"}, {"key": "messenger.send_failed", "value": "Failed to send message"}, {"key": "messenger.recipient", "value": "Recipient"}, {"key": "messenger.enter_recipient", "value": "Enter recipient ID or phone number"}, {"key": "messenger.message", "value": "Message"}, {"key": "messenger.enter_message", "value": "Enter your message..."}, {"key": "messenger.text_message", "value": "Text Message"}, {"key": "messenger.quick_reply", "value": "Quick Reply"}, {"key": "messenger.button_template", "value": "<PERSON>ton Template"}, {"key": "messenger.quick_replies", "value": "Quick Replies"}, {"key": "messenger.add_quick_reply", "value": "Add Quick Reply"}, {"key": "messenger.quick_reply_title", "value": "Quick reply title"}, {"key": "messenger.quick_reply_payload", "value": "Payload"}, {"key": "messenger.template_text", "value": "Template Text"}, {"key": "messenger.enter_template_text", "value": "Enter template text..."}, {"key": "messenger.buttons", "value": "Buttons"}, {"key": "messenger.add_button", "value": "<PERSON>d <PERSON>"}, {"key": "messenger.postback", "value": "Postback"}, {"key": "messenger.web_url", "value": "Web URL"}, {"key": "messenger.button_title", "value": "Button title"}, {"key": "messenger.payload", "value": "Payload"}, {"key": "messenger.url", "value": "URL"}, {"key": "messenger.attachments", "value": "Attachments"}, {"key": "messenger.attach_file", "value": "Attach File"}, {"key": "messenger.sending", "value": "Sending..."}, {"key": "messenger.send", "value": "Send Message"}, {"key": "messenger.star", "value": "Star"}, {"key": "messenger.unstar", "value": "Unstar"}, {"key": "messenger.archive", "value": "Archive"}, {"key": "messenger.delete", "value": "Delete"}, {"key": "messenger.no_messages", "value": "No messages yet"}, {"key": "messenger.you_sent_media", "value": "You sent a photo"}, {"key": "messenger.sent_media", "value": "Sent a photo"}, {"key": "messenger.you_sent_quick_reply", "value": "You sent a quick reply"}, {"key": "messenger.sent_quick_reply", "value": "Sent a quick reply"}, {"key": "messenger.you", "value": "You"}, {"key": "messenger.loading_more", "value": "Loading more conversations..."}, {"key": "messenger.error_loading", "value": "Error Loading Conversations"}, {"key": "messenger.error_loading_desc", "value": "Unable to load conversations. Please try again."}, {"key": "common.retry", "value": "Retry"}, {"key": "messenger.error", "value": "Error"}, {"key": "messenger.max_retries", "value": "Maximum Retries Reached"}, {"key": "messenger.max_retries_desc", "value": "Please refresh the page or contact support."}, {"key": "messenger.retry_success", "value": "Retry Successful"}, {"key": "messenger.retry_success_desc", "value": "Operation completed successfully."}, {"key": "messenger.retry_failed", "value": "Retry Failed"}, {"key": "email.use_oauth2", "value": "Use OAuth2 Authentication (Recommended for Gmail)"}, {"key": "email.oauth2_client_id", "value": "OAuth2 Client ID"}, {"key": "email.oauth2_client_id_placeholder", "value": "Your OAuth2 client ID"}, {"key": "email.oauth2_client_secret", "value": "OAuth2 Client Secret"}, {"key": "email.oauth2_client_secret_placeholder", "value": "Your OAuth2 client secret"}, {"key": "email.oauth2_refresh_token", "value": "OAuth2 Refresh Token"}, {"key": "email.oauth2_refresh_token_placeholder", "value": "Your OAuth2 refresh token"}, {"key": "email.oauth2_refresh_token_help", "value": "Obtain this from your OAuth2 provider's authorization flow"}, {"key": "email.testing", "value": "Testing..."}, {"key": "email.test_connection", "value": "Test Connection"}, {"key": "email.creating", "value": "Creating..."}, {"key": "email.create_connection", "value": "Create Connection"}, {"key": "roles.permissions_updated", "value": "Permissions Updated"}, {"key": "roles.admin_permissions_updated", "value": "Administrator permissions have been updated successfully."}, {"key": "roles.agent_permissions_updated", "value": "Agent permissions have been updated successfully."}, {"key": "roles.update_failed", "value": "Failed to update permissions: {{error}}"}, {"key": "roles.roles_permissions", "value": "Roles & Permissions"}, {"key": "roles.administrator", "value": "Administrator"}, {"key": "roles.administrator_desc", "value": "Full access to all features and settings"}, {"key": "roles.agent", "value": "Agent"}, {"key": "roles.agent_desc", "value": "Limited access to core features"}, {"key": "roles.edit_permissions", "value": "Edit Permissions"}, {"key": "roles.edit_admin_permissions", "value": "Edit Administrator Permissions"}, {"key": "roles.edit_agent_permissions", "value": "Edit Agent Permissions"}, {"key": "roles.configure_admin_permissions", "value": "Configure permissions for the Administrator role. Changes will apply to all users with this role."}, {"key": "roles.configure_agent_permissions", "value": "Configure permissions for the Agent role. Changes will apply to all users with this role."}, {"key": "roles.save_permissions", "value": "Save Permissions"}, {"key": "roles.conversation_management", "value": "Conversation Management"}, {"key": "roles.view_all_conversations", "value": "View All Conversations"}, {"key": "roles.view_assigned_conversations", "value": "View Assigned Conversations"}, {"key": "roles.assign_conversations", "value": "Assign Conversations"}, {"key": "roles.manage_conversations", "value": "Manage Conversations"}, {"key": "roles.contact_management", "value": "Contact Management"}, {"key": "roles.view_contacts", "value": "View Contacts"}, {"key": "roles.manage_contacts", "value": "Manage Contacts"}, {"key": "roles.campaign_management", "value": "Campaign Management"}, {"key": "roles.view_campaigns", "value": "View Campaigns"}, {"key": "roles.create_campaigns", "value": "Create Campaigns"}, {"key": "roles.edit_campaigns", "value": "Edit Campaigns"}, {"key": "roles.delete_campaigns", "value": "Delete Campaigns"}, {"key": "roles.manage_templates", "value": "Manage Templates"}, {"key": "roles.manage_segments", "value": "Manage Segments"}, {"key": "roles.view_campaign_analytics", "value": "View Campaign Analytics"}, {"key": "roles.manage_whatsapp_accounts", "value": "Manage WhatsApp Accounts"}, {"key": "roles.pipeline_management", "value": "Pipeline Management"}, {"key": "roles.view_pipeline", "value": "View Pipeline"}, {"key": "roles.manage_pipeline", "value": "Manage Pipeline"}, {"key": "roles.create_deals", "value": "Create Deals"}, {"key": "roles.edit_deals", "value": "Edit Deals"}, {"key": "roles.delete_deals", "value": "Delete Deals"}, {"key": "roles.manage_pipeline_stages", "value": "Manage Pipeline Stages"}, {"key": "roles.channel_management", "value": "Channel Management"}, {"key": "roles.view_channels", "value": "View Channels"}, {"key": "roles.manage_channels", "value": "Manage Channels"}, {"key": "roles.configure_channels", "value": "Configure Channels"}, {"key": "roles.flow_management", "value": "Flow Management"}, {"key": "roles.view_flows", "value": "View Flows"}, {"key": "roles.manage_flows", "value": "Manage Flows"}, {"key": "roles.analytics", "value": "Analytics"}, {"key": "roles.view_analytics", "value": "View Analytics"}, {"key": "roles.view_detailed_analytics", "value": "View Detailed Analytics"}, {"key": "roles.team_management", "value": "Team Management"}, {"key": "roles.view_team", "value": "View Team"}, {"key": "roles.manage_team", "value": "Manage Team"}, {"key": "roles.settings", "value": "Settings"}, {"key": "roles.view_settings", "value": "View Settings"}, {"key": "roles.manage_settings", "value": "Manage Settings"}, {"key": "roles.calendar", "value": "Calendar"}, {"key": "roles.view_calendar", "value": "View Calendar"}, {"key": "roles.manage_calendar", "value": "Manage Calendar"}, {"key": "roles.page_management", "value": "Page Management"}, {"key": "roles.view_pages", "value": "View Pages"}, {"key": "roles.manage_pages", "value": "Manage Pages"}, {"key": "whatsapp_twilio.validation_error", "value": "Validation Error"}, {"key": "whatsapp_twilio.required_fields", "value": "Account SID, Auth Token, and Conversation Service SID are required for validation."}, {"key": "whatsapp_twilio.credentials_valid", "value": "Credentials Valid"}, {"key": "whatsapp_twilio.validation_success", "value": "Successfully validated Twilio service: {{serviceName}}"}, {"key": "whatsapp_twilio.invalid_credentials", "value": "Invalid credentials"}, {"key": "whatsapp_twilio.validation_failed", "value": "Validation Failed"}, {"key": "whatsapp_twilio.validation_failed_desc", "value": "Failed to validate <PERSON><PERSON><PERSON> credentials."}, {"key": "whatsapp_twilio.connection_created", "value": "Twilio WhatsApp connection created successfully!"}, {"key": "whatsapp_twilio.connection_failed", "value": "Failed to create connection"}, {"key": "whatsapp_twilio.connection_error", "value": "Connection Error"}, {"key": "whatsapp_twilio.connection_error_desc", "value": "Failed to connect to Twilio WhatsApp"}, {"key": "whatsapp_twilio.connect_title", "value": "Connect T<PERSON>lio <PERSON>"}, {"key": "whatsapp_twilio.connection_name", "value": "Connection Name"}, {"key": "whatsapp_twilio.connection_name_placeholder", "value": "<PERSON> <PERSON><PERSON><PERSON>"}, {"key": "whatsapp_twilio.connection_name_help", "value": "A friendly name for this connection"}, {"key": "whatsapp_twilio.account_sid", "value": "Account SID"}, {"key": "whatsapp_twilio.account_sid_placeholder", "value": "ACxxxxx..."}, {"key": "whatsapp_twilio.account_sid_help", "value": "Your <PERSON><PERSON><PERSON> Account SID from the console"}, {"key": "whatsapp_twilio.auth_token", "value": "<PERSON><PERSON>"}, {"key": "whatsapp_twilio.auth_token_placeholder", "value": "Your auth token..."}, {"key": "whatsapp_twilio.auth_token_help", "value": "Your <PERSON><PERSON><PERSON> (keep this secure)"}, {"key": "whatsapp_twilio.conversation_service_sid", "value": "Conversation Service SID"}, {"key": "whatsapp_twilio.conversation_service_sid_placeholder", "value": "ISxxxxx..."}, {"key": "whatsapp_twilio.conversation_service_sid_help", "value": "Your Twilio Conversations Service SID"}, {"key": "whatsapp_twilio.whatsapp_number", "value": "WhatsApp Number"}, {"key": "whatsapp_twilio.whatsapp_number_placeholder", "value": "whatsapp:+***********"}, {"key": "whatsapp_twilio.whatsapp_number_help", "value": "Your Twilio WhatsApp number (sandbox or approved number)"}, {"key": "whatsapp_twilio.setup_title", "value": "<PERSON><PERSON><PERSON> Setup"}, {"key": "whatsapp_twilio.setup_steps", "value": "1. Create a Twilio account and Conversations Service\\n2. Enable WhatsApp for your service\\n3. Configure webhook endpoints\\n4. Test the connection"}, {"key": "whatsapp_twilio.validating", "value": "Validating..."}, {"key": "whatsapp_twilio.test", "value": "Test"}, {"key": "whatsapp_twilio.connecting", "value": "Connecting..."}, {"key": "whatsapp_twilio.connect", "value": "Connect"}, {"key": "whatsapp_business.validation_error", "value": "Validation Error"}, {"key": "whatsapp_business.required_fields", "value": "Access Token and Phone Number ID are required for validation."}, {"key": "whatsapp_business.credentials_valid", "value": "Credentials Valid"}, {"key": "whatsapp_business.validation_success", "value": "Successfully validated phone number: {{phoneNumber}}"}, {"key": "whatsapp_business.invalid_credentials", "value": "Invalid credentials"}, {"key": "whatsapp_business.validation_failed", "value": "Validation Failed"}, {"key": "whatsapp_business.validation_failed_desc", "value": "Failed to validate WhatsApp Business API credentials."}, {"key": "whatsapp_business.test_error", "value": "Test Error"}, {"key": "whatsapp_business.webhook_required", "value": "Webhook URL and verify token are required for testing."}, {"key": "whatsapp_business.webhook_test_successful", "value": "Webhook Test Successful!"}, {"key": "whatsapp_business.webhook_test_success_desc", "value": "Your webhook configuration is working correctly. You can now configure it in Meta Developer Console."}, {"key": "whatsapp_business.webhook_test_failed", "value": "Webhook Test Failed"}, {"key": "whatsapp_business.webhook_test_failed_desc", "value": "Please check your webhook configuration and try again."}, {"key": "whatsapp_business.test_failed", "value": "Test Failed"}, {"key": "whatsapp_business.network_error", "value": "Network error. Please check your connection and try again."}, {"key": "whatsapp_business.phone_required", "value": "Please enter a phone number to test the template message."}, {"key": "whatsapp_business.save_first", "value": "Please save the connection first before testing."}, {"key": "whatsapp_business.temp_connection_failed", "value": "Failed to create temporary connection for testing"}, {"key": "whatsapp_business.template_test_successful", "value": "Template Test Successful"}, {"key": "whatsapp_business.template_test_success_desc", "value": "Template message sent successfully to {{phone}}. Message ID: {{messageId}}"}, {"key": "whatsapp_business.template_test_failed", "value": "Template Test Failed"}, {"key": "whatsapp_business.template_test_failed_desc", "value": "Failed to send template message."}, {"key": "whatsapp_business.connection_created", "value": "WhatsApp Business API Connected"}, {"key": "whatsapp_business.connection_created_desc", "value": "Your WhatsApp Business API account has been connected successfully!"}, {"key": "whatsapp_business.connection_error", "value": "Connection Error"}, {"key": "whatsapp_business.connection_error_desc", "value": "Failed to connect to WhatsApp Business API"}, {"key": "whatsapp_business.connection_failed", "value": "Failed to create WhatsApp Business API connection"}, {"key": "whatsapp_business.account_name", "value": "Account Name"}, {"key": "whatsapp_business.account_name_placeholder", "value": "e.g. My Business"}, {"key": "whatsapp_business.account_name_help", "value": "A name to identify this connection"}, {"key": "whatsapp_business.phone_number_id", "value": "Phone Number ID"}, {"key": "whatsapp_business.phone_number_id_help", "value": "From Meta for Developers dashboard"}, {"key": "whatsapp_business.business_account_id", "value": "Business Account ID"}, {"key": "whatsapp_business.access_token", "value": "Access Token"}, {"key": "whatsapp_business.access_token_placeholder", "value": "EAAxxxxx..."}, {"key": "whatsapp_business.access_token_help", "value": "Long-lived or permanent access token from Meta for Developers"}, {"key": "whatsapp_business.app_id", "value": "App ID"}, {"key": "whatsapp_business.app_secret", "value": "App Secret"}, {"key": "whatsapp_business.webhook_url", "value": "Webhook URL"}, {"key": "whatsapp_business.webhook_url_help", "value": "Enter your webhook URL or use the auto-generated one. Copy this URL and paste it in your Meta for Developers dashboard under Webhooks configuration."}, {"key": "whatsapp_business.webhook_verify_token", "value": "Webhook Verify Token"}, {"key": "whatsapp_business.webhook_verify_token_placeholder", "value": "default_verify_token"}, {"key": "whatsapp_business.webhook_verify_token_help", "value": "This token must match the WHATSAPP_WEBHOOK_VERIFY_TOKEN environment variable on your server. Copy this token and paste it in the \"Verify token\" field in your Meta for Developers webhook configuration."}, {"key": "whatsapp_business.connection_specific_token", "value": "✅ Connection-Specific Token: This verify token will be stored with your WhatsApp Business API connection:"}, {"key": "whatsapp_business.token_instructions", "value": "Enter this exact token in your Meta Developer Console webhook configuration. Each WhatsApp Business API connection can have its own unique verify token."}, {"key": "whatsapp_business.test_webhook_config", "value": "Test Webhook Configuration"}, {"key": "whatsapp_business.test_webhook_desc", "value": "Test your webhook configuration before submitting to Meta"}, {"key": "whatsapp_business.test_webhook_help", "value": "This will verify that your webhook URL is accessible and responds correctly to Meta's verification requests."}, {"key": "whatsapp_business.test_template_message", "value": "Test Template Message (Optional)"}, {"key": "whatsapp_business.test_template_placeholder", "value": "+1234567890"}, {"key": "whatsapp_business.test_template_help", "value": "Send a test \"hello_world\" template message to verify your connection is working. Enter a phone number with country code (e.g., +1234567890)."}, {"key": "whatsapp_business.development_mode_note", "value": "Note: If your WhatsApp Business API is in development mode, you can only send messages to phone numbers that are added to the \"allowed list\" in your Meta for Developers dashboard."}, {"key": "whatsapp_business.learn_more", "value": "Learn how to add phone numbers →"}, {"key": "whatsapp_business.copy", "value": "Copy"}, {"key": "whatsapp_business.testing", "value": "Testing..."}, {"key": "whatsapp_business.test_webhook", "value": "Test Webhook"}, {"key": "whatsapp_business.test", "value": "Test"}, {"key": "whatsapp_business.validating", "value": "Validating..."}, {"key": "whatsapp_business.connecting", "value": "Connecting..."}, {"key": "whatsapp_business.connect", "value": "Connect"}, {"key": "ai_usage.limit_exceeded", "value": "Limit Exceeded"}, {"key": "ai_usage.warning", "value": "Warning"}, {"key": "ai_usage.company_credentials", "value": "Company"}, {"key": "ai_usage.system_credentials", "value": "System"}, {"key": "ai_usage.total_requests", "value": "Total Requests"}, {"key": "ai_usage.total_tokens", "value": "Total Tokens"}, {"key": "ai_usage.total_cost", "value": "Total Cost"}, {"key": "ai_usage.tokens", "value": "Tokens"}, {"key": "ai_usage.cost", "value": "Cost"}, {"key": "ai_usage.requests", "value": "requests"}, {"key": "ai_usage.usage_percentage", "value": "{{percentage}}% of total usage"}, {"key": "ai_usage.tokens_count", "value": "{{count}} tokens"}, {"key": "ai_usage.company_credentials_label", "value": "Company Credentials"}, {"key": "ai_usage.system_credentials_label", "value": "System Credentials"}, {"key": "header.connection_active", "value": "Real-time connection active"}, {"key": "header.connected", "value": "Connected"}, {"key": "header.connection_lost", "value": "Real-time connection lost"}, {"key": "header.disconnected", "value": "Disconnected"}, {"key": "admin.languages.edit_language", "value": "Edit Language"}, {"key": "admin.languages.edit_language_desc", "value": "Update the language settings."}, {"key": "admin.languages.update_failed", "value": "Failed to update language"}, {"key": "admin.languages.language_updated", "value": "Language updated"}, {"key": "admin.languages.language_updated_desc", "value": "The language has been updated successfully."}, {"key": "admin.languages.updating", "value": "Updating..."}, {"key": "admin.languages.update_language", "value": "Update Language"}, {"key": "admin.namespaces.create_namespace", "value": "Create Namespace"}, {"key": "admin.namespaces.add_namespace", "value": "Add Namespace"}, {"key": "admin.namespaces.add_namespace_desc", "value": "Create a new namespace for organizing translations."}, {"key": "admin.namespaces.name_label", "value": "Name"}, {"key": "admin.namespaces.name_placeholder", "value": "common"}, {"key": "admin.namespaces.description_label", "value": "Description"}, {"key": "admin.namespaces.description_placeholder", "value": "Common translations used throughout the application"}, {"key": "admin.namespaces.create_failed", "value": "Failed to create namespace"}, {"key": "admin.namespaces.namespace_created", "value": "Namespace created"}, {"key": "admin.namespaces.namespace_created_desc", "value": "The namespace has been created successfully."}, {"key": "admin.namespaces.creating", "value": "Creating..."}, {"key": "admin.translations.export_array", "value": "Export Array"}, {"key": "admin.translations.export_nested", "value": "Export Nested"}, {"key": "admin.translations.import_translations", "value": "Import Translations"}, {"key": "admin.translations.import_translations_desc", "value": "Import translations from a JSON file. Supports both array format and nested format."}, {"key": "admin.translations.select_file_language", "value": "Please select a language and a file"}, {"key": "admin.translations.import_failed", "value": "Failed to import translations"}, {"key": "admin.translations.translations_imported", "value": "Translations imported"}, {"key": "admin.translations.translations_imported_desc", "value": "The translations have been imported successfully."}, {"key": "admin.translations.language_label", "value": "Language"}, {"key": "admin.translations.select_language", "value": "Select language"}, {"key": "admin.translations.file_label", "value": "File"}, {"key": "admin.analytics.dashboard_title", "value": "Analytics Dashboard"}, {"key": "admin.analytics.time_range", "value": "Time Range:"}, {"key": "admin.analytics.select_time_range", "value": "Select time range"}, {"key": "admin.analytics.last_7_days", "value": "Last 7 Days"}, {"key": "admin.analytics.last_30_days", "value": "Last 30 Days"}, {"key": "admin.analytics.last_90_days", "value": "Last 90 Days"}, {"key": "admin.analytics.last_year", "value": "Last Year"}, {"key": "admin.analytics.all_time", "value": "All Time"}, {"key": "admin.analytics.error_loading", "value": "Error Loading Analytics"}, {"key": "admin.analytics.failed_load_data", "value": "Failed to load analytics data"}, {"key": "admin.analytics.retry", "value": "Retry"}, {"key": "admin.analytics.total_users", "value": "Total Users"}, {"key": "admin.analytics.users_last_period", "value": "+{{count}} in the last period"}, {"key": "admin.analytics.companies", "value": "Companies"}, {"key": "admin.analytics.active_companies", "value": "{{count}} active companies"}, {"key": "admin.analytics.messages", "value": "Messages"}, {"key": "admin.analytics.across_conversations", "value": "Across {{count}} conversations"}, {"key": "admin.analytics.contacts", "value": "Contacts"}, {"key": "admin.analytics.in_contact_database", "value": "In contact database"}, {"key": "admin.analytics.user_growth", "value": "User Growth"}, {"key": "admin.analytics.user_growth_chart", "value": "User growth chart visualization"}, {"key": "admin.analytics.channel_distribution", "value": "Channel Distribution"}, {"key": "admin.analytics.channel_distribution_chart", "value": "Channel distribution chart visualization"}, {"key": "admin.analytics.company_conversations", "value": "Company Conversations"}, {"key": "admin.analytics.company_conversations_chart", "value": "Company conversation chart visualization"}, {"key": "admin.analytics.active_users", "value": "Active Users"}, {"key": "admin.analytics.active_users_chart", "value": "Active users chart visualization"}, {"key": "flow_builder.translation_node.hide", "value": "<PERSON>de"}, {"key": "flow_builder.translation_node.edit", "value": "Edit"}, {"key": "flow_builder.translation_node.openai_translation", "value": "OpenAI Translation"}, {"key": "flow_builder.translation_node.translation_enabled", "value": "Translation enabled"}, {"key": "flow_builder.translation_node.translation_disabled", "value": "Translation disabled"}, {"key": "flow_builder.translation_node.target_label", "value": "Target:"}, {"key": "flow_builder.translation_node.mode_label", "value": "Mode:"}, {"key": "flow_builder.translation_node.auto_detect", "value": "Auto-detect"}, {"key": "flow_builder.translation_node.configuration", "value": "Translation Configuration"}, {"key": "flow_builder.translation_node.enable_translation", "value": "Enable Translation"}, {"key": "flow_builder.translation_node.enable_desc", "value": "Automatically translate messages using OpenAI"}, {"key": "flow_builder.translation_node.openai_api_key", "value": "OpenAI API Key"}, {"key": "flow_builder.translation_node.api_key_placeholder", "value": "Enter your OpenAI API key"}, {"key": "flow_builder.translation_node.get_api_key", "value": "Get your API key here"}, {"key": "flow_builder.translation_node.target_language", "value": "Target Language"}, {"key": "flow_builder.translation_node.select_language", "value": "Select language..."}, {"key": "flow_builder.translation_node.translation_mode", "value": "Translation Mode"}, {"key": "flow_builder.translation_node.select_mode", "value": "Select mode..."}, {"key": "flow_builder.translation_node.auto_language_detection", "value": "Auto Language Detection"}, {"key": "flow_builder.translation_node.auto_detect_desc", "value": "Only translate if source ≠ target language"}, {"key": "flow_builder.translation_node.separate_message", "value": "Separate Message"}, {"key": "flow_builder.translation_node.separate_desc", "value": "Send translation as a separate follow-up message"}, {"key": "flow_builder.translation_node.append_original", "value": "Append to Original"}, {"key": "flow_builder.translation_node.append_desc", "value": "Add translation to the end of the original message"}, {"key": "flow_builder.translation_node.replace_original", "value": "Replace Original"}, {"key": "flow_builder.translation_node.replace_desc", "value": "Replace the original message with the translation"}, {"key": "flow_builder.typebot_node.start_conversation", "value": "Start Conversation"}, {"key": "flow_builder.typebot_node.start_conversation_desc", "value": "Initialize new chat session"}, {"key": "flow_builder.typebot_node.send_message", "value": "Send Message"}, {"key": "flow_builder.typebot_node.send_message_desc", "value": "Send user message to bot"}, {"key": "flow_builder.typebot_node.get_response", "value": "Get Response"}, {"key": "flow_builder.typebot_node.get_response_desc", "value": "Retrieve bot response"}, {"key": "flow_builder.typebot_node.manage_session", "value": "Manage Session"}, {"key": "flow_builder.typebot_node.manage_session_desc", "value": "Create/update/close session"}, {"key": "flow_builder.typebot_node.webhook_event", "value": "Webhook Event"}, {"key": "flow_builder.typebot_node.webhook_event_desc", "value": "Handle incoming webhook"}, {"key": "flow_builder.typebot_node.get_analytics", "value": "Get Analytics"}, {"key": "flow_builder.typebot_node.get_analytics_desc", "value": "Retrieve conversation analytics"}, {"key": "flow_builder.typebot_node.create_session", "value": "Create Session"}, {"key": "flow_builder.typebot_node.update_session", "value": "Update Session"}, {"key": "flow_builder.typebot_node.close_session", "value": "Close Session"}, {"key": "flow_builder.typebot_node.get_status", "value": "Get Status"}, {"key": "flow_builder.typebot_node.text_input", "value": "Text Input"}, {"key": "flow_builder.typebot_node.number_input", "value": "Number Input"}, {"key": "flow_builder.typebot_node.email_input", "value": "Email Input"}, {"key": "flow_builder.typebot_node.url_input", "value": "URL Input"}, {"key": "flow_builder.typebot_node.date_input", "value": "Date Input"}, {"key": "flow_builder.typebot_node.phone_input", "value": "Phone Input"}, {"key": "flow_builder.typebot_node.multiple_choice", "value": "Multiple Choice"}, {"key": "flow_builder.typebot_node.file_upload", "value": "File Upload"}, {"key": "flow_builder.typebot_node.start_new_chat", "value": "Start New Chat"}, {"key": "flow_builder.typebot_node.send_user_message", "value": "Send User Message"}, {"key": "flow_builder.typebot_node.get_bot_response", "value": "Get Bot Response"}, {"key": "flow_builder.typebot_node.close_conversation", "value": "Close Conversation"}, {"key": "flow_builder.typebot_node.typebot_integration", "value": "Typebot Integration"}, {"key": "flow_builder.typebot_node.no_bot_selected", "value": "No bot selected"}, {"key": "flow_builder.typebot_node.bot_label", "value": "Bot:"}, {"key": "flow_builder.typebot_node.config_label", "value": "config"}, {"key": "flow_builder.typebot_node.configs_label", "value": "configs"}, {"key": "flow_builder.typebot_node.mapping_label", "value": "mapping"}, {"key": "flow_builder.typebot_node.mappings_label", "value": "mappings"}, {"key": "flow_builder.typebot_node.api_connected", "value": "API Connected"}, {"key": "flow_builder.typebot_node.timeout_label", "value": "Timeout:"}, {"key": "flow_builder.typebot_node.quick_templates", "value": "Quick Templates"}, {"key": "flow_builder.typebot_node.choose_operation", "value": "Choose a Typebot operation..."}, {"key": "flow_builder.typebot_node.api_configuration", "value": "Typebot API Configuration"}, {"key": "flow_builder.typebot_node.api_token", "value": "API Token"}, {"key": "flow_builder.typebot_node.workspace_id", "value": "Workspace ID (Optional)"}, {"key": "flow_builder.typebot_node.typebot_id", "value": "Typebot ID"}, {"key": "flow_builder.typebot_node.session_timeout", "value": "Session Timeout (seconds)"}, {"key": "flow_builder.typebot_node.test_connection", "value": "Test"}, {"key": "flow_builder.typebot_node.test_connection_title", "value": "Test connection to your Typebot"}, {"key": "flow_builder.typebot_node.testing", "value": "Testing..."}, {"key": "flow_builder.typebot_node.typebot_operation", "value": "Typebot Operation"}, {"key": "flow_builder.typebot_node.select_operation", "value": "Select operation"}, {"key": "flow_builder.typebot_node.operation_configuration", "value": "Operation Configuration"}, {"key": "flow_builder.typebot_node.enabled", "value": "Enabled"}, {"key": "flow_builder.typebot_node.disabled", "value": "Disabled"}, {"key": "flow_builder.typebot_node.variable_syntax", "value": "Use {{variable}} syntax for dynamic values"}, {"key": "flow_builder.typebot_node.response_variable_mapping", "value": "Response Variable Mapping"}, {"key": "flow_builder.typebot_node.add_mapping", "value": "Add"}, {"key": "flow_builder.typebot_node.mapping_description", "value": "Map Typebot response fields to flow variables for use in subsequent nodes"}, {"key": "flow_builder.main.flow_name", "value": "Flow name"}, {"key": "flow_builder.main.auto_arrange", "value": "Auto-Arrange"}, {"key": "flow_builder.main.arranging", "value": "Arranging..."}, {"key": "flow_builder.main.auto_arrange_tooltip", "value": "Automatically organize all nodes in a clean hierarchical layout"}, {"key": "flow_builder.main.auto_arrange_shortcut", "value": "Shortcut: Ctrl+Shift+A"}, {"key": "flow_builder.main.undo_arrange", "value": "Undo"}, {"key": "flow_builder.main.undo_arrange_tooltip", "value": "Restore nodes to their previous positions"}, {"key": "flow_builder.main.undo_arrange_shortcut", "value": "Shortcut: Ctrl+Z"}, {"key": "flow_builder.main.node_selection", "value": "Node Selection"}, {"key": "flow_builder.main.nodes_auto_arranged", "value": "Nodes Auto-Arranged"}, {"key": "flow_builder.main.nodes_arranged_desc", "value": "{{count}} nodes organized across {{levels}} levels with proper spacing. No overlaps guaranteed!"}, {"key": "flow_builder.main.auto_arrange_undone", "value": "Auto-<PERSON><PERSON><PERSON>"}, {"key": "flow_builder.main.nodes_restored", "value": "Nodes have been restored to their previous positions."}, {"key": "flow_builder.node_types.whatsapp_message", "value": "WhatsApp Message"}, {"key": "flow_builder.node_types.text_message", "value": "Text Message"}, {"key": "flow_builder.node_types.quick_reply_options", "value": "Quick Reply Options"}, {"key": "flow_builder.node_types.follow_up_message", "value": "Follow-up Message"}, {"key": "flow_builder.node_types.image_message", "value": "Image Message"}, {"key": "flow_builder.node_types.video_message", "value": "Video Message"}, {"key": "flow_builder.node_types.audio_message", "value": "Audio Message"}, {"key": "flow_builder.node_types.document_message", "value": "Document Message"}, {"key": "flow_builder.node_types.condition", "value": "Condition"}, {"key": "flow_builder.node_types.wait", "value": "Wait"}, {"key": "flow_builder.node_types.ai_assistant", "value": "AI Assistant"}, {"key": "flow_builder.node_types.translation", "value": "Translation"}, {"key": "flow_builder.node_types.pipeline", "value": "Pipeline"}, {"key": "flow_builder.node_types.agent_handoff", "value": "Agent <PERSON><PERSON>"}, {"key": "flow_builder.node_types.n8n", "value": "n8n"}, {"key": "flow_builder.node_types.make_com", "value": "Make.com"}, {"key": "flow_builder.node_types.http_request", "value": "HTTP Request"}, {"key": "flow_builder.node_types.google_sheets", "value": "Google Sheets"}, {"key": "flow_builder.node_types.webhook", "value": "Webhook"}, {"key": "flow_builder.node_types.shopify", "value": "Shopify"}, {"key": "flow_builder.node_types.woocommerce", "value": "WooCommerce"}, {"key": "flow_builder.node_types.typebot", "value": "Typebot"}, {"key": "flow_builder.node_types.flowise", "value": "Flowise"}, {"key": "flow_builder.node_types.documind_pdf_chat", "value": "Documind PDF Chat"}, {"key": "flow_builder.node_types.chat_pdf_ai", "value": "Chat PDF AI"}, {"key": "flow_builder.sections.triggers", "value": "Triggers"}, {"key": "flow_builder.sections.messages", "value": "Messages"}, {"key": "flow_builder.sections.flow_control", "value": "Flow Control"}, {"key": "flow_builder.sections.integrations", "value": "Integrations"}, {"key": "flow_builder.condition_types.message_contains", "value": "Message Contains"}, {"key": "flow_builder.condition_types.exact_match", "value": "Exact Match"}, {"key": "flow_builder.condition_types.regex_match", "value": "Regex Match"}, {"key": "flow_builder.condition_types.message_starts_with", "value": "Message Starts With"}, {"key": "flow_builder.condition_types.message_ends_with", "value": "Message Ends With"}, {"key": "flow_builder.condition_types.has_media", "value": "Has Media"}, {"key": "flow_builder.condition_types.media_type_is", "value": "Media Type Is"}, {"key": "flow_builder.condition_types.time_condition", "value": "Time Condition"}, {"key": "flow_builder.condition_types.contact_attribute", "value": "Contact Attribute"}, {"key": "flow_builder.condition_types.custom_expression", "value": "Custom Expression"}, {"key": "flow_builder.media_types.image", "value": "image"}, {"key": "flow_builder.media_types.video", "value": "video"}, {"key": "flow_builder.media_types.audio", "value": "audio"}, {"key": "flow_builder.media_types.document", "value": "document"}, {"key": "flow_builder.media_types.sticker", "value": "sticker"}, {"key": "flow_builder.file_types.pdf_document", "value": "PDF Document"}, {"key": "flow_builder.file_types.word_document", "value": "Word Document"}, {"key": "flow_builder.file_types.excel_spreadsheet", "value": "Excel Spreadsheet"}, {"key": "flow_builder.file_types.powerpoint_presentation", "value": "PowerPoint Presentation"}, {"key": "flow_builder.file_types.text_file", "value": "Text File"}, {"key": "flow_builder.file_types.archive_file", "value": "Archive File"}, {"key": "flow_builder.file_types.image_file", "value": "Image File"}, {"key": "flow_builder.file_types.document", "value": "Document"}, {"key": "flow_builder.singleton_errors.typebot_exists", "value": "Only one Typebot node allowed per flow"}, {"key": "flow_builder.singleton_errors.flowise_exists", "value": "Only one Flowise node allowed per flow"}, {"key": "campaigns.dashboard_description", "value": "Manage and monitor your mass messaging campaigns"}, {"key": "campaigns.live_updates", "value": "Live updates"}, {"key": "campaigns.polling_mode", "value": "Polling mode"}, {"key": "campaigns.refresh", "value": "Refresh"}, {"key": "campaigns.total_campaigns", "value": "Total Campaigns"}, {"key": "campaigns.active_campaigns", "value": "Active Campaigns"}, {"key": "campaigns.total_recipients", "value": "Total Recipients"}, {"key": "campaigns.messages_delivered", "value": "Messages Delivered"}, {"key": "campaigns.delivery_rate", "value": "Delivery Rate"}, {"key": "campaigns.status.draft", "value": "Draft"}, {"key": "campaigns.status.scheduled", "value": "Scheduled"}, {"key": "campaigns.status.running", "value": "Running"}, {"key": "campaigns.status.paused", "value": "Paused"}, {"key": "campaigns.status.completed", "value": "Completed"}, {"key": "campaigns.status.cancelled", "value": "Cancelled"}, {"key": "campaigns.status.failed", "value": "Failed"}, {"key": "campaigns.start", "value": "Start"}, {"key": "campaigns.pause", "value": "Pause"}, {"key": "campaigns.resume", "value": "Resume"}, {"key": "campaigns.details", "value": "Details"}, {"key": "campaigns.recipients", "value": "Recipients"}, {"key": "campaigns.progress", "value": "Progress"}, {"key": "campaigns.delivered", "value": "Delivered"}, {"key": "campaigns.failed", "value": "Failed"}, {"key": "campaigns.processed", "value": "processed"}, {"key": "campaigns.total", "value": "total"}, {"key": "campaigns.delete_campaign", "value": "Delete Campaign"}, {"key": "campaigns.delete_confirmation", "value": "Are you sure you want to delete the campaign \"{{name}}\"? This action cannot be undone and all campaign data will be permanently removed."}, {"key": "campaigns.completed", "value": "Campaign Completed"}, {"key": "campaigns.finished_processing", "value": "Campaign \"{{name}}\" has finished processing."}, {"key": "campaigns.fetch_failed", "value": "Failed to fetch campaigns"}, {"key": "campaigns.stats_fetch_failed", "value": "Failed to fetch campaign statistics"}, {"key": "campaigns.action_failed", "value": "Failed to {{action}} campaign"}, {"key": "campaigns.deleted_successfully", "value": "Campaign \"{{name}}\" deleted successfully"}, {"key": "campaigns.delete_failed", "value": "Failed to delete campaign"}, {"key": "campaigns.stats_recalculated", "value": "Stats Recalculated"}, {"key": "campaigns.stats_updated", "value": "Campaign statistics have been updated."}, {"key": "campaigns.stats_recalc_failed", "value": "Failed to recalculate stats: {{error}}"}, {"key": "campaigns.network_error", "value": "Network error: {{error}}"}, {"key": "settings.whatsapp.typing_indicators", "value": "Typing Indicators"}, {"key": "settings.whatsapp.typing_indicators_desc", "value": "Configure human-like typing indicators that appear before sending messages"}, {"key": "settings.whatsapp.enable_typing", "value": "Enable Typing Indicators"}, {"key": "settings.whatsapp.enable_typing_desc", "value": "Show \"Typing...\" and \"Recording...\" indicators before sending messages"}, {"key": "settings.whatsapp.typing_speed", "value": "Typing Speed (WPM)"}, {"key": "settings.whatsapp.randomness_factor", "value": "Randomness Factor"}, {"key": "settings.whatsapp.consistent", "value": "Consistent"}, {"key": "settings.whatsapp.random", "value": "Random"}, {"key": "settings.whatsapp.min_delay", "value": "Minimum Delay (seconds)"}, {"key": "settings.whatsapp.min_delay_desc", "value": "Minimum time to show typing indicator (0.5-10 seconds)"}, {"key": "settings.whatsapp.max_delay", "value": "Maximum Delay (seconds)"}, {"key": "settings.whatsapp.max_delay_desc", "value": "Maximum time to show typing indicator (1-30 seconds)"}, {"key": "settings.whatsapp.recording_min_delay", "value": "Recording <PERSON> (seconds)"}, {"key": "settings.whatsapp.recording_min_delay_desc", "value": "Minimum time to show recording indicator (1-10 seconds)"}, {"key": "settings.whatsapp.recording_max_delay", "value": "Recording <PERSON> (seconds)"}, {"key": "settings.whatsapp.recording_max_delay_desc", "value": "Maximum time to show recording indicator (2-15 seconds)"}, {"key": "settings.whatsapp.message_splitting", "value": "Message Splitting"}, {"key": "settings.whatsapp.message_splitting_desc", "value": "Automatically break long messages into smaller, more natural chunks"}, {"key": "settings.whatsapp.enable_splitting", "value": "Enable Message Splitting"}, {"key": "settings.whatsapp.enable_splitting_desc", "value": "Break long bot responses into multiple messages for better readability"}, {"key": "settings.whatsapp.max_message_length", "value": "Maximum Message Length"}, {"key": "settings.whatsapp.split_method", "value": "Split Method"}, {"key": "settings.whatsapp.by_sentences", "value": "By Sentences"}, {"key": "settings.whatsapp.by_paragraphs", "value": "By Paragraphs"}, {"key": "settings.whatsapp.by_characters", "value": "By Characters"}, {"key": "settings.whatsapp.delay_between_chunks", "value": "Delay Between Message Chunks (seconds)"}, {"key": "settings.whatsapp.delay_between_chunks_desc", "value": "Time between split message chunks (0.5-10 seconds)"}, {"key": "settings.whatsapp.random_delay_factor", "value": "Random Delay Factor"}, {"key": "settings.whatsapp.min_chunk_size", "value": "Minimum Chunk Size"}, {"key": "settings.whatsapp.min_chunk_size_desc", "value": "Minimum characters per chunk to avoid very short messages"}, {"key": "settings.whatsapp.preserve_formatting", "value": "Preserve Formatting"}, {"key": "settings.whatsapp.preserve_formatting_desc", "value": "Maintain markdown and text formatting when splitting messages"}, {"key": "settings.whatsapp.smart_boundary", "value": "Smart Boundary Detection"}, {"key": "settings.whatsapp.smart_boundary_desc", "value": "Intelligently split at sentence and clause boundaries"}, {"key": "settings.whatsapp.prioritize_sentences", "value": "Prioritize Sentence Boundaries"}, {"key": "settings.whatsapp.prioritize_sentences_desc", "value": "Prefer splitting at sentence endings over character limits"}, {"key": "settings.whatsapp.preview", "value": "Preview"}, {"key": "settings.whatsapp.preview_desc", "value": "See how your settings will affect message delivery"}, {"key": "settings.whatsapp.sample_message", "value": "Sample Message"}, {"key": "settings.whatsapp.sample_message_placeholder", "value": "Enter a sample message to see how it will be split and timed..."}, {"key": "settings.whatsapp.message_preview", "value": "Message Preview"}, {"key": "settings.whatsapp.typing_time", "value": "~{{time}}s typing"}, {"key": "settings.whatsapp.messages_count", "value": "{{count}} messages"}, {"key": "settings.whatsapp.message_number", "value": "Message {{current}} of {{total}}"}, {"key": "settings.whatsapp.characters_count", "value": "{{count}} characters"}, {"key": "settings.whatsapp.typing_delay", "value": "Typing delay: ~{{time}}s"}, {"key": "settings.whatsapp.reset_defaults", "value": "Reset to Defaults"}, {"key": "settings.whatsapp.reload_config", "value": "Reload Config"}, {"key": "settings.whatsapp.save_settings", "value": "Save Settings"}, {"key": "settings.whatsapp.settings_saved", "value": "Settings Saved"}, {"key": "settings.whatsapp.settings_saved_desc", "value": "WhatsApp behavior settings have been updated successfully."}, {"key": "settings.whatsapp.save_failed", "value": "Failed to save WhatsApp behavior settings. Please try again."}, {"key": "contacts.edit.contact_id_missing", "value": "Contact ID is missing"}, {"key": "contacts.edit.update_failed", "value": "Failed to update contact"}, {"key": "contacts.edit.success_title", "value": "Contact updated"}, {"key": "contacts.edit.success_description", "value": "The contact has been successfully updated."}, {"key": "contacts.edit.error_title", "value": "Update failed"}, {"key": "contacts.edit.title", "value": "Edit Contact"}, {"key": "contacts.edit.description", "value": "Make changes to the contact information below."}, {"key": "contacts.edit.name_label", "value": "Name"}, {"key": "contacts.edit.email_label", "value": "Email"}, {"key": "contacts.edit.phone_label", "value": "Phone"}, {"key": "contacts.edit.company_label", "value": "Company"}, {"key": "contacts.edit.channel_label", "value": "Channel"}, {"key": "contacts.edit.select_channel_placeholder", "value": "Select channel"}, {"key": "contacts.edit.channel.whatsapp_official", "value": "WhatsApp Official"}, {"key": "contacts.edit.channel.whatsapp_unofficial", "value": "WhatsApp Unofficial"}, {"key": "contacts.edit.channel.messenger", "value": "Facebook Messenger"}, {"key": "contacts.edit.channel.instagram", "value": "Instagram"}, {"key": "contacts.edit.channel_identifier_label", "value": "Channel Identifier"}, {"key": "contacts.edit.channel_identifier_placeholder", "value": "Phone number or ID"}, {"key": "contacts.edit.tags_label", "value": "Tags (comma separated)"}, {"key": "contacts.edit.tags_placeholder", "value": "lead, customer, etc."}, {"key": "contacts.edit.notes_label", "value": "Notes"}, {"key": "contacts.edit.saving", "value": "Saving..."}, {"key": "contacts.edit.save_changes", "value": "Save Changes"}, {"key": "contacts.delete_contact", "value": "Delete Contact"}, {"key": "contacts.delete_warning", "value": "This will permanently delete this contact and all associated conversations, messages, and notes. This action cannot be undone."}, {"key": "contacts.message", "value": "Message"}, {"key": "contacts.bulk_actions.selected_count", "value": "{{count}} contacts selected"}, {"key": "contacts.bulk_actions.clear_selection", "value": "Clear Selection"}, {"key": "contacts.bulk_actions.deleting", "value": "Deleting..."}, {"key": "contacts.bulk_actions.delete_selected", "value": "Delete Selected"}, {"key": "contacts.bulk_actions.select_all", "value": "Select all contacts"}, {"key": "contacts.bulk_actions.select_contact", "value": "Select contact {{name}}"}, {"key": "contacts.bulk_delete.title", "value": "Delete {{count}} Contacts"}, {"key": "contacts.bulk_delete.warning", "value": "This will permanently delete these contacts and all associated conversations, messages, and notes. This action cannot be undone."}, {"key": "contacts.bulk_delete.deleting", "value": "Deleting..."}, {"key": "contacts.bulk_delete.confirm", "value": "Delete {{count}} Contacts"}, {"key": "contacts.add.avatar_upload", "value": "Upload contact photo"}, {"key": "contacts.add.avatar_optional", "value": "Optional - JPG, PNG up to 5MB"}, {"key": "contacts.add.choose_photo", "value": "<PERSON>ose Photo"}, {"key": "contacts.add.name_label", "value": "Name"}, {"key": "contacts.add.name_placeholder", "value": "Enter contact name"}, {"key": "contacts.add.email_label", "value": "Email"}, {"key": "contacts.add.email_placeholder", "value": "Enter email address"}, {"key": "contacts.add.phone_label", "value": "Phone"}, {"key": "contacts.add.phone_placeholder", "value": "+1234567890"}, {"key": "contacts.add.company_label", "value": "Company"}, {"key": "contacts.add.company_placeholder", "value": "Enter company name"}, {"key": "contacts.add.channel_label", "value": "Channel"}, {"key": "contacts.add.select_channel_placeholder", "value": "Select channel"}, {"key": "contacts.add.channel.whatsapp_official", "value": "WhatsApp Official"}, {"key": "contacts.add.channel.whatsapp_unofficial", "value": "WhatsApp Unofficial"}, {"key": "contacts.add.channel.messenger", "value": "Facebook Messenger"}, {"key": "contacts.add.channel.instagram", "value": "Instagram"}, {"key": "contacts.add.channel_identifier_label", "value": "Channel Identifier"}, {"key": "contacts.add.channel_identifier_placeholder", "value": "Phone number or ID"}, {"key": "contacts.add.tags_label", "value": "Tags"}, {"key": "contacts.add.tags_placeholder", "value": "Type tags separated by commas..."}, {"key": "contacts.add.notes_label", "value": "Notes"}, {"key": "contacts.add.notes_placeholder", "value": "Additional notes about this contact..."}, {"key": "contacts.add.creating", "value": "Creating..."}, {"key": "contacts.add.create_button", "value": "Create Contact"}, {"key": "contacts.add.name_required", "value": "Contact name is required"}, {"key": "contacts.add.duplicate_phone", "value": "A contact with this phone number already exists: {{name}}"}, {"key": "contacts.import.title", "value": "Import Contacts from CSV"}, {"key": "contacts.import.description", "value": "Upload a CSV file to import multiple contacts at once. Download the template to see the required format."}, {"key": "contacts.import.download_template", "value": "Download Template"}, {"key": "contacts.import.file_label", "value": "CSV File"}, {"key": "contacts.import.file_help", "value": "Maximum file size: 10MB. Only CSV files are supported."}, {"key": "contacts.import.preview_label", "value": "Preview (first 5 rows)"}, {"key": "contacts.import.duplicate_handling_label", "value": "Duplicate Handling"}, {"key": "contacts.import.duplicate.skip", "value": "Skip duplicates"}, {"key": "contacts.import.duplicate.update", "value": "Update existing"}, {"key": "contacts.import.duplicate.create", "value": "Create new"}, {"key": "contacts.import.duplicate_help", "value": "How to handle contacts with duplicate email addresses"}, {"key": "contacts.import.importing", "value": "Importing..."}, {"key": "contacts.import.results_label", "value": "Import Results"}, {"key": "contacts.import.successful", "value": "Successfully imported: {{count}}"}, {"key": "contacts.import.failed", "value": "Failed to import: {{count}}"}, {"key": "contacts.import.errors", "value": "Errors:"}, {"key": "contacts.import.more_errors", "value": "And {{count}} more errors..."}, {"key": "contacts.import.import_button", "value": "Import Contacts"}, {"key": "contacts.import.no_file_selected", "value": "Please select a CSV file to import"}, {"key": "auth.signin_company_title", "value": "Sign in to {{companyName}}"}, {"key": "auth.signin_company_description", "value": "Access your company workspace on {{appName}}"}, {"key": "auth.signin_title", "value": "Access like a shadow"}, {"key": "auth.signin_description", "value": "Unite your customer conversations across all channels in one powerful CRM."}, {"key": "auth.get_started", "value": "<PERSON><PERSON>"}, {"key": "auth.new_to_platform", "value": "New to the platform?"}, {"key": "auth.or_sign_in_with", "value": "Or sign in with"}, {"key": "auth.forgot_password_title", "value": "Forgot your password?"}, {"key": "auth.forgot_password_description", "value": "Enter your email address and we'll send you a link to reset your password."}, {"key": "auth.email_address", "value": "Email address"}, {"key": "auth.email_address_placeholder", "value": "Enter your email address"}, {"key": "auth.send_reset_link", "value": "Send reset link"}, {"key": "auth.back_to_login", "value": "Back to login"}, {"key": "auth.check_email_title", "value": "Check your email"}, {"key": "auth.check_email_description", "value": "We've sent a password reset link to your email address."}, {"key": "auth.reset_email_sent_message", "value": "If an account with that email exists, you'll receive a password reset link shortly."}, {"key": "auth.didnt_receive_email", "value": "Didn't receive the email?"}, {"key": "auth.resend_email", "value": "Resend email"}, {"key": "auth.reset_email_sent", "value": "Reset email sent"}, {"key": "auth.failed_send_reset", "value": "Failed to send reset email"}, {"key": "auth.enter_email_error", "value": "Please enter your email address"}, {"key": "registration.error.register_failed", "value": "Failed to register company"}, {"key": "registration.success_title", "value": "Registration successful!"}, {"key": "registration.approval_required", "value": "Your company registration has been submitted for approval. You will receive an email once approved."}, {"key": "registration.success_desc", "value": "Your company has been registered successfully. You can now log in."}, {"key": "registration.failed_title", "value": "Registration failed"}, {"key": "registration.registering", "value": "Registering..."}, {"key": "registration.register_button", "value": "Register Company"}, {"key": "admin.forgot_password_title", "value": "Admin Password Reset"}, {"key": "admin.forgot_password_description", "value": "Enter your admin email address and we'll send you a secure reset link."}, {"key": "admin.email_address", "value": "Admin Email Address"}, {"key": "admin.email_placeholder", "value": "Enter your admin email address"}, {"key": "admin.send_reset_link", "value": "Send admin reset link"}, {"key": "admin.back_to_login", "value": "Back to admin login"}, {"key": "admin.reset_email_sent_message", "value": "For security reasons, admin password reset links are only sent to verified admin email addresses."}, {"key": "inbox.individual", "value": "Individual"}, {"key": "inbox.groups", "value": "Groups"}, {"key": "inbox.conversations", "value": "Conversations"}, {"key": "inbox.filtered_by_channel", "value": "Filtered by channel"}, {"key": "inbox.clear_channel_filter", "value": "Clear channel filter"}, {"key": "inbox.loading_more_conversations", "value": "Loading more conversations..."}, {"key": "inbox.load_more_conversations", "value": "Load More Conversations"}, {"key": "inbox.all_conversations_loaded", "value": "All conversations loaded"}, {"key": "messages.input.type_message", "value": "Type a message..."}, {"key": "messages.input.send_message", "value": "Send message"}, {"key": "message_bubble.contact_avatar", "value": "Contact avatar"}, {"key": "message_bubble.download_file_failed", "value": "Failed to download file to device"}, {"key": "message_bubble.delete_failed", "value": "Failed to delete message"}, {"key": "message_bubble.message_deleted", "value": "Message deleted"}, {"key": "message_bubble.delete_success", "value": "Message has been deleted successfully"}, {"key": "message_bubble.delete_failed_title", "value": "Delete failed"}, {"key": "new_conversation.channel_unofficial", "value": "Unofficial"}, {"key": "new_conversation.channel_official", "value": "Business API"}, {"key": "new_conversation.name_required", "value": "Contact name is required"}, {"key": "new_conversation.phone_required", "value": "Phone number is required"}, {"key": "new_conversation.phone_invalid", "value": "Please enter a valid phone number with at least 10 digits"}, {"key": "new_conversation.connection_required", "value": "Please select a WhatsApp connection"}, {"key": "new_conversation.no_connections", "value": "No active WhatsApp connections found. Please connect WhatsApp in Settings first."}, {"key": "new_conversation.create_failed", "value": "Failed to create conversation"}, {"key": "new_conversation.success_message", "value": "WhatsApp conversation initiated successfully."}, {"key": "new_conversation.create_error", "value": "Failed to create conversation. Please try again."}, {"key": "new_conversation.no_connection_selected", "value": "No Connection Selected"}, {"key": "new_conversation.select_connection", "value": "Please select a WhatsApp connection."}, {"key": "new_conversation.title", "value": "Start New WhatsApp Conversation"}, {"key": "new_conversation.description", "value": "Enter contact details to initiate a new WhatsApp conversation."}, {"key": "new_conversation.contact_name_required", "value": "Contact Name *"}, {"key": "new_conversation.enter_contact_name", "value": "Enter contact's full name"}, {"key": "new_conversation.phone_number_required", "value": "Phone Number *"}, {"key": "new_conversation.enter_phone_number", "value": "Enter phone number (e.g., +1234567890)"}, {"key": "new_conversation.include_country_code", "value": "Include country code for international numbers"}, {"key": "new_conversation.whatsapp_connection_required", "value": "WhatsApp Connection *"}, {"key": "new_conversation.loading_connections", "value": "Loading connections..."}, {"key": "new_conversation.select_whatsapp_connection", "value": "Select WhatsApp connection"}, {"key": "new_conversation.no_connections_available", "value": "No active WhatsApp connections available"}, {"key": "new_conversation.initial_message_optional", "value": "Initial Message (Optional)"}, {"key": "new_conversation.enter_initial_message", "value": "Enter an optional first message to send..."}, {"key": "new_conversation.initial_message_help", "value": "This message will be sent immediately after creating the conversation"}, {"key": "new_conversation.creating", "value": "Creating..."}, {"key": "new_conversation.start_conversation", "value": "Start Conversation"}, {"key": "messages.input.recording_too_short", "value": "Recording is too short or empty"}, {"key": "messages.input.voice_message_sent", "value": "Voice message sent"}, {"key": "messages.input.voice_message_failed", "value": "Failed to send voice message"}, {"key": "message_input.reply_failed", "value": "Failed to send reply"}, {"key": "messages.input.recording_permission_denied", "value": "Microphone permission denied"}, {"key": "messages.input.recording_not_supported", "value": "Voice recording not supported in this browser"}, {"key": "messages.input.recording_error", "value": "Error accessing microphone"}, {"key": "messages.input.start_recording", "value": "Start recording"}, {"key": "messages.input.stop_recording", "value": "Stop recording"}, {"key": "messages.input.cancel_recording", "value": "Cancel recording"}, {"key": "messages.input.send_recording", "value": "Send recording"}, {"key": "messages.input.recording_duration", "value": "Recording: {{duration}}"}, {"key": "messages.input.emoji_picker", "value": "Open emoji picker"}, {"key": "messages.input.attach_media", "value": "Attach media"}, {"key": "messages.input.quick_replies", "value": "Quick replies"}, {"key": "messages.input.cancel_reply", "value": "<PERSON><PERSON> reply"}, {"key": "messages.input.replying_to", "value": "Replying to {{name}}"}, {"key": "settings.google_calendar_disconnected", "value": "Google Calendar Disconnected"}, {"key": "settings.google_calendar_disconnect_success", "value": "Your Google Calendar account has been disconnected successfully."}, {"key": "settings.google_calendar_disconnect_error", "value": "Failed to disconnect Google Calendar: {{error}}"}, {"key": "settings.google_calendar_credentials_updated", "value": "Google Calendar Credentials Updated"}, {"key": "settings.google_calendar_credentials_success", "value": "Your Google OAuth credentials have been updated successfully."}, {"key": "settings.google_calendar_credentials_error", "value": "Failed to update Google Calendar credentials: {{error}}"}, {"key": "settings.account_updated", "value": "Account Updated"}, {"key": "settings.account_updated_success", "value": "Your account settings have been saved successfully"}, {"key": "settings.api_key_empty_error", "value": "API key cannot be empty"}, {"key": "settings.api_key_saved", "value": "API Key Saved"}, {"key": "settings.api_key_updated", "value": "Your API key has been updated"}, {"key": "settings.subscription_updated", "value": "Subscription Updated"}, {"key": "settings.subscription_updated_success", "value": "Your subscription has been updated successfully"}, {"key": "settings.whatsapp_connected", "value": "WhatsApp Connected"}, {"key": "settings.whatsapp_connected_success", "value": "Your WhatsApp account has been connected successfully!"}, {"key": "settings.connection_error", "value": "Connection Error"}, {"key": "settings.tabs.channel_connections", "value": "Channel Connections"}, {"key": "settings.tabs.channels", "value": "Channels"}, {"key": "settings.tabs.inbox_settings", "value": "Inbox Settings"}, {"key": "settings.tabs.inbox", "value": "Inbox"}, {"key": "settings.tabs.whatsapp_behavior", "value": "WhatsApp Behavior"}, {"key": "settings.tabs.whatsapp", "value": "WhatsApp"}, {"key": "settings.tabs.billing", "value": "Billing"}, {"key": "settings.tabs.team_members", "value": "Team Members"}, {"key": "settings.tabs.team", "value": "Team"}, {"key": "settings.tabs.api_access", "value": "API Access"}, {"key": "settings.tabs.api", "value": "API"}, {"key": "settings.tabs.ai_credentials", "value": "AI Credentials"}, {"key": "settings.tabs.ai_keys", "value": "AI Keys"}, {"key": "settings.tabs.ai_usage", "value": "AI Usage"}, {"key": "settings.tabs.usage", "value": "Usage"}, {"key": "settings.tabs.platform", "value": "Platform"}, {"key": "settings.channel_connections.title", "value": "Channel Connections"}, {"key": "settings.channel_connections.description", "value": "Connect and manage your communication channels"}, {"key": "settings.billing.title", "value": "Billing & Subscription"}, {"key": "settings.billing.description", "value": "Manage your subscription plan and payment methods"}, {"key": "settings.team.title", "value": "Team Members"}, {"key": "settings.team.description", "value": "Manage team members and their permissions"}, {"key": "settings.platform.title", "value": "Platform Configuration"}, {"key": "settings.platform.description", "value": "Configure platform-wide integrations and partner API settings"}, {"key": "flow_builder.http_request", "value": "HTTP Request"}, {"key": "flow_builder.http_auth", "value": "<PERSON><PERSON>"}, {"key": "flow_builder.http_header", "value": "header"}, {"key": "flow_builder.http_body_configured", "value": "Body configured"}, {"key": "flow_builder.http_retry", "value": "Retry"}, {"key": "flow_builder.http_mapping", "value": "mapping"}, {"key": "flow_builder.http_quick_templates", "value": "Quick Templates"}, {"key": "flow_builder.http_choose_template", "value": "Choose a template..."}, {"key": "flow_builder.http_method", "value": "HTTP Method"}, {"key": "flow_builder.http_select_method", "value": "Select method"}, {"key": "flow_builder.http_request_url", "value": "Request URL"}, {"key": "flow_builder.webhook", "value": "Webhook"}, {"key": "flow_builder.webhook_no_url", "value": "No URL configured"}, {"key": "flow_builder.webhook_timeout_error", "value": "Request timed out after {{timeout}} seconds"}, {"key": "flow_builder.webhook_unknown_error", "value": "Unknown error occurred"}, {"key": "flow_builder.ai_elevenlabs_api_key", "value": "ElevenLabs API Key"}, {"key": "flow_builder.ai_elevenlabs_api_key_placeholder", "value": "Enter ElevenLabs API key..."}, {"key": "flow_builder.ai_elevenlabs_required", "value": "Required for ElevenLabs TTS"}, {"key": "flow_builder.ai_elevenlabs_model", "value": "Model"}, {"key": "flow_builder.ai_elevenlabs_select_model", "value": "Select model..."}, {"key": "flow_builder.ai_elevenlabs_stability", "value": "Stability ({{value}})"}, {"key": "flow_builder.ai_elevenlabs_similarity", "value": "Similarity ({{value}})"}, {"key": "flow_builder.ai_elevenlabs_speaker_boost", "value": "Speaker <PERSON><PERSON>"}, {"key": "flow_builder.ai_voice_response_mode_placeholder", "value": "Select mode..."}, {"key": "flow_builder.ai_share_document_name", "value": "Share Document"}, {"key": "flow_builder.ai_share_document_desc", "value": "Share a document or file with the user when they request it"}, {"key": "flow_builder.ai_book_appointment_name", "value": "Book Appointment"}, {"key": "flow_builder.ai_book_appointment_desc", "value": "Book a new appointment in Google Calendar"}, {"key": "flow_builder.ai_function_book_appointment", "value": "Create a new calendar event/appointment in Google Calendar. Use this when the user wants to schedule a meeting or appointment."}, {"key": "flow_builder.ai_function_param_title", "value": "Title/summary of the appointment"}, {"key": "flow_builder.ai_function_param_description", "value": "Detailed description of the appointment"}, {"key": "flow_builder.ai_function_param_start_datetime", "value": "Start date and time in ISO format (YYYY-MM-DDTHH:MM:SS)"}, {"key": "flow_builder.ai_function_param_end_datetime", "value": "End date and time in ISO format (YYYY-MM-DDTHH:MM:SS)"}, {"key": "flow_builder.ai_function_param_attendee_emails", "value": "Email addresses of attendees (optional)"}, {"key": "flow_builder.ai_function_param_location", "value": "Location of the appointment (optional)"}, {"key": "flow_builder.ai_function_param_document_type", "value": "Type of document requested (brochure, manual, catalog, etc.)"}, {"key": "flow_builder.ai_function_param_user_request", "value": "The user's original request for the document"}, {"key": "flow_builder.ai_task_active", "value": "Active"}, {"key": "flow_builder.ai_task_inactive", "value": "Inactive"}, {"key": "flow_builder.ai_task_name_label", "value": "Task Name"}, {"key": "flow_builder.ai_task_name_placeholder", "value": "e.g., Share Product Brochure"}, {"key": "flow_builder.ai_function_name_label", "value": "Function Name"}, {"key": "flow_builder.ai_function_name_placeholder", "value": "e.g., share_document"}, {"key": "flow_builder.ai_task_description_label", "value": "Task Description"}, {"key": "flow_builder.ai_task_description_placeholder", "value": "Describe what this task does and when it should be triggered"}, {"key": "flow_builder.ai_function_description_label", "value": "AI Function Description"}, {"key": "flow_builder.ai_function_description_placeholder", "value": "Detailed instructions for the AI model about when to call this function. Be specific about user intent requirements."}, {"key": "flow_builder.ai_function_description_tip", "value": "💡 Tip: Use phrases like \"ONLY call when user explicitly requests...\" to prevent false triggers"}, {"key": "flow_builder.ai_output_handle_label", "value": "Output Handle ID"}, {"key": "flow_builder.ai_output_handle_placeholder", "value": "e.g., task_brochure"}, {"key": "flow_builder.ai_add_task", "value": "Add Task"}, {"key": "flow_builder.ai_no_tasks_configured", "value": "No tasks configured"}, {"key": "flow_builder.ai_no_tasks_help", "value": "Add tasks to enable AI function calling and flow routing"}, {"key": "flow_builder.ai_tasks_tip", "value": "💡 Tip: Each active task creates an output handle for flow routing. Use specific descriptions to prevent false triggers."}, {"key": "flow_builder.ai_task_execution_disabled_help", "value": "Enable task execution to allow AI function calling and advanced flow routing"}, {"key": "flow_builder.ai_google_calendar_integration", "value": "Google Calendar Integration"}, {"key": "flow_builder.ai_checking_connection", "value": "Checking connection..."}, {"key": "flow_builder.ai_google_calendar_connected", "value": "Google Calendar Connected"}, {"key": "flow_builder.ai_switch_account", "value": "Switch Account"}, {"key": "flow_builder.ai_switching", "value": "Switching..."}, {"key": "flow_builder.ai_disconnect", "value": "Disconnect"}, {"key": "flow_builder.ai_google_calendar_not_connected", "value": "Google Calendar Not Connected"}, {"key": "flow_builder.ai_connect_google_calendar", "value": "Connect Google Calendar"}, {"key": "flow_builder.ai_connecting", "value": "Connecting..."}, {"key": "flow_builder.ai_calendar_features_available", "value": "The AI can now: book appointments, check availability, list events, update events, and cancel events."}, {"key": "flow_builder.ai_calendar_system_prompt_note", "value": "Note: Calendar behavior is controlled by the main system prompt above."}, {"key": "flow_builder.ai_business_hours_start", "value": "Business Hours Start"}, {"key": "flow_builder.ai_business_hours_end", "value": "Business Hours End"}, {"key": "flow_builder.ai_appointment_duration", "value": "Appointment Duration (minutes)"}, {"key": "flow_builder.ai_timezone", "value": "Timezone"}, {"key": "flow_builder.ai_timezone_placeholder", "value": "Select timezone..."}, {"key": "flow_builder.ai_knowledge_base", "value": "Knowledge Base"}, {"key": "flow_builder.ai_rag_enhancement", "value": "RAG Enhancement"}, {"key": "flow_builder.ai_knowledge_base_disabled_help", "value": "Enable knowledge base to enhance AI responses with document-based context using RAG (Retrieval-Augmented Generation)"}, {"key": "flow_builder.ai_google_calendar_disabled_help", "value": "Enable Google Calendar integration to allow AI to manage appointments and check availability"}, {"key": "flow_builder.ai_tts_provider", "value": "TTS Provider"}, {"key": "flow_builder.ai_tts_provider_placeholder", "value": "Select TTS provider..."}, {"key": "flow_builder.ai_tts_provider_help", "value": "Choose your text-to-speech provider"}, {"key": "flow_builder.ai_voice_selection", "value": "Voice Selection"}, {"key": "flow_builder.ai_voice_selection_placeholder", "value": "Select voice..."}, {"key": "flow_builder.ai_voice_selection_help", "value": "Choose the voice for AI audio responses"}, {"key": "flow_builder.ai_custom_voice_id", "value": "Custom Voice ID"}, {"key": "flow_builder.ai_custom_voice_id_placeholder", "value": "Paste your ElevenLabs voice ID here..."}, {"key": "flow_builder.ai_custom_voice_id_help", "value": "Enter a custom voice ID from your ElevenLabs account (e.g., \"pNInz6obpgDQGcFmaJgB\")"}, {"key": "flow_builder.ai_voice_id_warning", "value": "⚠️ Voice ID seems short. ElevenLabs voice IDs are typically 20+ characters long."}, {"key": "flow_builder.ai_voice_id_tip", "value": "💡 Tip: You can find voice IDs in your ElevenLabs dashboard under \"Voices\" → Click on a voice → Copy the Voice ID"}, {"key": "flow_builder.ai_voice_processing_elevenlabs", "value": "Speech-to-Text uses OpenAI Whisper, Text-to-Speech uses ElevenLabs API"}, {"key": "flow_builder.ai_voice_processing_openai", "value": "Both Speech-to-Text and Text-to-Speech use OpenAI APIs"}, {"key": "flow_builder.ai_voice_processing_label", "value": "Voice Processing:"}, {"key": "flow_builder.ai_audio_limits", "value": "Audio Processing Limits"}, {"key": "flow_builder.ai_max_audio_duration", "value": "Maximum Audio Duration (seconds)"}, {"key": "flow_builder.ai_max_audio_duration_help", "value": "Audio messages longer than this limit will not be transcribed or generate TTS responses to save API costs"}, {"key": "flow_builder.ai_max_duration_exceeded", "value": "Maximum allowed duration is 30 seconds"}, {"key": "flow_builder.ai_min_duration_error", "value": "Minimum duration is 1 second"}, {"key": "flow_builder.ai_cost_optimization_tip", "value": "💰 Cost Optimization: Limiting audio duration prevents expensive API calls for long voice messages. Users will receive a text response asking them to send shorter messages."}, {"key": "flow_builder.ai_task_execution", "value": "Task Execution"}, {"key": "flow_builder.ai_voice_processing_openai", "value": "Speech-to-Text and Text-to-Speech both use OpenAI APIs"}, {"key": "flow_builder.ai_default_system_prompt", "value": "You are a helpful assistant. Answer user questions concisely and accurately. Only perform specific actions when the user explicitly requests them."}, {"key": "flow_builder.ai_voice_only_mode", "value": "Voice-to-Voice Only"}, {"key": "flow_builder.ai_voice_only_description", "value": "Only generate voice responses when user sends a voice message"}, {"key": "flow_builder.ai_share_document_task_description", "value": "When user requests a document, brochure, or file to be shared"}, {"key": "flow_builder.ai_function_label", "value": "Function:"}, {"key": "flow_builder.ai_tts_openai_name", "value": "OpenAI"}, {"key": "flow_builder.ai_tts_openai_description", "value": "OpenAI TTS with Whisper STT"}, {"key": "flow_builder.ai_tts_elevenlabs_name", "value": "ElevenLabs"}, {"key": "flow_builder.ai_tts_elevenlabs_description", "value": "ElevenLabs TTS with OpenAI Whisper STT"}, {"key": "flow_builder.ai_voice_mode_always", "value": "Always"}, {"key": "flow_builder.ai_voice_mode_always_description", "value": "Generate voice responses for all messages (text and voice)"}, {"key": "flow_builder.ai_voice_mode_voice_only", "value": "Voice-to-Voice Only"}, {"key": "flow_builder.ai_voice_mode_never", "value": "Never"}, {"key": "flow_builder.ai_voice_mode_never_description", "value": "Disable voice responses (text only)"}, {"key": "flow_builder.ai_summary_history", "value": "History:"}, {"key": "flow_builder.ai_summary_tasks", "value": "Tasks:"}, {"key": "flow_builder.ai_summary_tts", "value": "TTS:"}, {"key": "flow_builder.ai_summary_audio", "value": "Audio:"}, {"key": "flow_builder.ai_summary_audio_max", "value": "{{duration}}s max"}, {"key": "flow_builder.ai_history_help", "value": "Previous messages to include for context"}, {"key": "flow_builder.ai_tts_help", "value": "Convert AI responses to voice messages"}, {"key": "flow_builder.n8n_name", "value": "n8n"}, {"key": "flow_builder.n8n_description", "value": "Execute n8n chat AI workflow with multimedia support"}, {"key": "flow_builder.n8n_tooltip", "value": "Execute an n8n workflow with AI chat capabilities. Supports text, images, videos, audio, and documents. Perfect for AI agents, chatbots, and conversational workflows."}, {"key": "flow_builder.n8n_delete_node", "value": "Delete node"}, {"key": "flow_builder.n8n_workflow_integration", "value": "n8n Workflow Automation Integration"}, {"key": "flow_builder.n8n_field_required", "value": "This field is required"}, {"key": "flow_builder.n8n_invalid_url", "value": "Please enter a valid URL (e.g., https://your-n8n-instance.com)"}, {"key": "flow_builder.n8n_timeout_range", "value": "Timeout must be between 1 and 300 seconds"}, {"key": "flow_builder.n8n_instance_url_required", "value": "Please enter your n8n instance URL"}, {"key": "flow_builder.n8n_invalid_url_format", "value": "Invalid URL format. Please enter a valid URL (e.g., https://your-instance.app.n8n.cloud)"}, {"key": "flow_builder.n8n_webhook_or_workflow_required", "value": "Either webhook URL or (workflow name + API key) is required to execute the workflow"}, {"key": "flow_builder.n8n_test_connection_failed", "value": "Failed to test connection. Please check your configuration and try again."}, {"key": "flow_builder.n8n_list_workflows_required", "value": "Please enter instance URL and API key to list workflows"}, {"key": "flow_builder.n8n_no_workflows_found", "value": "No workflows found in your n8n instance"}, {"key": "flow_builder.n8n_list_workflows_failed", "value": "Failed to list workflows. Please check your configuration and try again."}, {"key": "flow_builder.n8n_workflow_selected", "value": "✓ Selected workflow: \"{{name}}\" (ID: {{id}})"}, {"key": "flow_builder.n8n_copy_failed", "value": "Failed to copy text: "}, {"key": "flow_builder.n8n_configuration_completeness", "value": "Configuration completeness: {{progress}}%"}, {"key": "flow_builder.n8n_complete_required_fields", "value": "Complete required fields to reach 70%"}, {"key": "flow_builder.n8n_configuration_ready", "value": "Configuration ready!"}, {"key": "flow_builder.n8n_hide", "value": "<PERSON>de"}, {"key": "flow_builder.n8n_edit", "value": "Edit"}, {"key": "flow_builder.n8n_hide_configuration_panel", "value": "Hide configuration panel"}, {"key": "flow_builder.n8n_show_configuration_panel", "value": "Show configuration panel"}, {"key": "flow_builder.n8n_configured", "value": "Configured"}, {"key": "flow_builder.n8n_not_configured", "value": "Not configured"}, {"key": "flow_builder.n8n_connection_configured", "value": "n8n connection is properly configured"}, {"key": "flow_builder.n8n_configure_connection", "value": "Please configure n8n instance URL and workflow details"}, {"key": "flow_builder.n8n_workflow_label", "value": "Workflow:"}, {"key": "flow_builder.n8n_target_workflow", "value": "Target workflow: {{name}}"}, {"key": "flow_builder.n8n_multimedia_support", "value": "Multimedia support enabled"}, {"key": "flow_builder.n8n_supports", "value": "Supports: {{types}}"}, {"key": "flow_builder.n8n_max_size", "value": "Max size: {{size}}MB"}, {"key": "flow_builder.n8n_api_configuration", "value": "n8n API Configuration"}, {"key": "flow_builder.n8n_configure_connection_help", "value": "Configure connection to your n8n instance"}, {"key": "flow_builder.n8n_required_fields_marked", "value": "Required fields are marked with *"}, {"key": "flow_builder.n8n_instance_url_label", "value": "Instance URL *"}, {"key": "flow_builder.n8n_instance_url_help_title", "value": "Your n8n instance URL"}, {"key": "flow_builder.n8n_instance_url_examples", "value": "Examples:"}, {"key": "flow_builder.n8n_instance_url_example1", "value": "• https://your-n8n.example.com"}, {"key": "flow_builder.n8n_instance_url_example2", "value": "• https://n8n.yourcompany.com"}, {"key": "flow_builder.n8n_instance_url_example3", "value": "• http://localhost:5678 (for local)"}, {"key": "flow_builder.n8n_instance_url_placeholder", "value": "https://your-n8n-instance.com"}, {"key": "flow_builder.n8n_copied", "value": "Copied!"}, {"key": "flow_builder.n8n_copy_url_clipboard", "value": "Copy URL to clipboard"}, {"key": "flow_builder.n8n_api_key_label", "value": "API Key"}, {"key": "flow_builder.n8n_optional", "value": "Optional"}, {"key": "flow_builder.n8n_api_key_help_title", "value": "n8n API Key (optional)"}, {"key": "flow_builder.n8n_api_key_help_find", "value": "How to find your API key:"}, {"key": "flow_builder.n8n_api_key_step1", "value": "1. Go to n8n Settings → API"}, {"key": "flow_builder.n8n_api_key_step2", "value": "2. Create a new API key"}, {"key": "flow_builder.n8n_api_key_step3", "value": "3. Copy and paste it here"}, {"key": "flow_builder.n8n_api_key_required_private", "value": "Required for private instances"}, {"key": "flow_builder.n8n_api_key_placeholder", "value": "n8n_api_key_..."}, {"key": "flow_builder.n8n_workflow_id_name_label", "value": "Workflow ID/Name *"}, {"key": "flow_builder.n8n_workflow_identifier_help_title", "value": "Target workflow identifier"}, {"key": "flow_builder.n8n_workflow_identifier_help_use", "value": "You can use either:"}, {"key": "flow_builder.n8n_workflow_identifier_id", "value": "• Workflow ID (numeric)"}, {"key": "flow_builder.n8n_workflow_identifier_name", "value": "• Workflow name (text)"}, {"key": "flow_builder.n8n_workflow_identifier_find", "value": "Find this in your n8n workflows list"}, {"key": "flow_builder.n8n_workflow_placeholder", "value": "my-workflow or 123"}, {"key": "flow_builder.n8n_chat_webhook_url_label", "value": "Chat Webhook URL"}, {"key": "flow_builder.n8n_webhook_help_title", "value": "Direct URL to your n8n chat trigger webhook"}, {"key": "flow_builder.n8n_webhook_help_use", "value": "Use this if automatic webhook detection fails"}, {"key": "flow_builder.n8n_webhook_help_example", "value": "Example: https://n8n.com/webhook/your-webhook-id/chat"}, {"key": "flow_builder.n8n_webhook_help_priority", "value": "Takes priority over automatic detection"}, {"key": "flow_builder.n8n_webhook_placeholder", "value": "https://n8n.com/webhook/your-webhook-id/chat"}, {"key": "flow_builder.n8n_webhook_direct_url", "value": "✓ Direct webhook URL will be used"}, {"key": "flow_builder.n8n_timeout_label", "value": "Timeout (seconds)"}, {"key": "flow_builder.n8n_timeout_help_title", "value": "Request timeout"}, {"key": "flow_builder.n8n_timeout_help_description", "value": "Maximum time to wait for n8n response"}, {"key": "flow_builder.n8n_timeout_help_range", "value": "Range: 1-300 seconds"}, {"key": "flow_builder.n8n_timeout_help_default", "value": "Default: 30 seconds"}, {"key": "flow_builder.n8n_executing", "value": "Executing..."}, {"key": "flow_builder.n8n_execute_workflow", "value": "Execute Workflow"}, {"key": "flow_builder.n8n_execute_workflow_help", "value": "Execute your n8n workflow"}, {"key": "flow_builder.n8n_enter_url_to_enable", "value": "Enter your n8n instance URL to enable execution"}, {"key": "flow_builder.n8n_execute_via_webhook", "value": "Execute workflow via webhook URL"}, {"key": "flow_builder.n8n_execute_via_api", "value": "Execute workflow via n8n API"}, {"key": "flow_builder.n8n_enter_workflow_api_key", "value": "Enter workflow name and API key to execute via API"}, {"key": "flow_builder.n8n_list_workflows_help", "value": "List workflows to find correct ID/name"}, {"key": "flow_builder.n8n_open_api_docs", "value": "Open n8n API documentation"}, {"key": "flow_builder.n8n_media_configuration", "value": "Media Configuration"}, {"key": "flow_builder.n8n_multimedia_support_help_title", "value": "Multimedia Message Support"}, {"key": "flow_builder.n8n_multimedia_support_help_description", "value": "Configure how your n8n workflow handles images, videos, audio, and documents from WhatsApp users."}, {"key": "flow_builder.n8n_input_support_title", "value": "📥 Input Support:"}, {"key": "flow_builder.n8n_input_support_receive", "value": "• Receive media files with URLs and metadata"}, {"key": "flow_builder.n8n_input_support_automatic", "value": "• Automatic file type detection and validation"}, {"key": "flow_builder.n8n_input_support_enhanced", "value": "• Enhanced message payload with media info"}, {"key": "flow_builder.n8n_output_support_title", "value": "📤 Output Support:"}, {"key": "flow_builder.n8n_output_support_return", "value": "• Return media URLs in n8n response"}, {"key": "flow_builder.n8n_output_support_automatic", "value": "• Automatic media message generation"}, {"key": "flow_builder.n8n_enable_media_support", "value": "Enable Media Support"}, {"key": "flow_builder.n8n_enable_media_support_help", "value": "Allow n8n workflow to receive and process multimedia messages"}, {"key": "flow_builder.n8n_supported_media_types", "value": "Supported Media Types"}, {"key": "flow_builder.n8n_input_support_validation", "value": "• File type validation and size limits"}, {"key": "flow_builder.n8n_output_support_multiple", "value": "• Support for multiple media attachments"}, {"key": "flow_builder.n8n_output_support_delivery", "value": "• Automatic WhatsApp media delivery"}, {"key": "flow_builder.n8n_max_file_size_label", "value": "<PERSON> (MB)"}, {"key": "flow_builder.n8n_media_processing_mode_label", "value": "Media Processing Mode"}, {"key": "flow_builder.n8n_url_only", "value": "URL Only"}, {"key": "flow_builder.n8n_url_only_description", "value": "Send media URL to n8n workflow"}, {"key": "flow_builder.n8n_url_metadata", "value": "URL + Metadata"}, {"key": "flow_builder.n8n_url_metadata_description", "value": "Include file size, type, and metadata"}, {"key": "flow_builder.n8n_include_file_metadata", "value": "Include File Metadata"}, {"key": "flow_builder.n8n_include_file_metadata_help", "value": "Include MIME type, file size, and original filename"}, {"key": "flow_builder.n8n_integration_examples", "value": "📋 Integration Examples"}, {"key": "flow_builder.n8n_input_payload_structure", "value": "Input Payload Structure:"}, {"key": "flow_builder.n8n_response_format", "value": "Response Format:"}, {"key": "flow_builder.n8n_select_workflow", "value": "Select Workflow"}, {"key": "flow_builder.n8n_workflow_id_name", "value": "ID: {{id}} - Name: {{name}}"}, {"key": "flow_builder.n8n_workflow_active", "value": "Active: {{status}}"}, {"key": "flow_builder.n8n_yes", "value": "Yes"}, {"key": "flow_builder.n8n_no", "value": "No"}, {"key": "flow_builder.n8n_no_workflows_found_title", "value": "No workflows found"}, {"key": "flow_builder.n8n_create_workflows_first", "value": "Create workflows in your n8n instance first"}, {"key": "flow_builder.n8n_click_workflow_select", "value": "Click on a workflow to select it and populate the Workflow ID/Name field."}, {"key": "flow_builder.n8n_ensure_instance_running", "value": "Make sure your n8n instance is running and contains workflows."}, {"key": "flow_builder.n8n_connection_successful", "value": "Connection Successful"}, {"key": "flow_builder.n8n_connection_failed", "value": "Connection Failed"}, {"key": "flow_builder.trigger_default_reset_message", "value": "<PERSON><PERSON> has been reactivated. Starting fresh conversation..."}, {"key": "flow_builder.trigger_delete_node", "value": "Delete node"}, {"key": "flow_builder.trigger_channel_whatsapp_unofficial", "value": "WhatsApp (Unofficial)"}, {"key": "flow_builder.trigger_channel_whatsapp_official", "value": "<PERSON><PERSON><PERSON><PERSON> (Official)"}, {"key": "flow_builder.trigger_channel_whatsapp_twilio", "value": "WhatsApp (Twilio)"}, {"key": "flow_builder.trigger_channel_whatsapp_360dialog", "value": "WhatsApp (360Dialog)"}, {"key": "flow_builder.trigger_channel_messenger", "value": "Facebook Messenger"}, {"key": "flow_builder.trigger_channel_instagram", "value": "Instagram"}, {"key": "flow_builder.trigger_channel_email", "value": "Email"}, {"key": "flow_builder.trigger_channel_telegram", "value": "Telegram"}, {"key": "flow_builder.trigger_more_keywords", "value": "+{{count}} more"}, {"key": "flow_builder.trigger_condition_type", "value": "Condition Type"}, {"key": "flow_builder.trigger_word_phrase", "value": "Word or Phrase"}, {"key": "flow_builder.trigger_exact_text", "value": "Exact Text"}, {"key": "flow_builder.trigger_multiple_keywords", "value": "Multiple Keywords"}, {"key": "flow_builder.trigger_pattern", "value": "Pattern"}, {"key": "flow_builder.trigger_keywords_help", "value": "Enter keywords separated by commas. The trigger will activate when any of these keywords is detected in a message."}, {"key": "flow_builder.trigger_keywords_label", "value": "Keywords:"}, {"key": "flow_builder.trigger_keywords_required", "value": "At least one keyword is required"}, {"key": "flow_builder.trigger_case_sensitive", "value": "Case sensitive matching"}, {"key": "flow_builder.trigger_enable", "value": "Enable"}, {"key": "flow_builder.trigger_minutes", "value": "Minutes"}, {"key": "flow_builder.trigger_hours", "value": "Hours"}, {"key": "flow_builder.trigger_days", "value": "Days"}, {"key": "flow_builder.trigger_condition_contains", "value": "contains"}, {"key": "flow_builder.trigger_condition_exactly_matches", "value": "exactly matches"}, {"key": "flow_builder.trigger_condition_contains_any", "value": "contains any of"}, {"key": "flow_builder.trigger_condition_matches_pattern", "value": "matches pattern"}, {"key": "flow_builder.trigger_condition_has_media", "value": "has media attachment"}, {"key": "flow_builder.trigger_placeholder_contains", "value": "help, support, etc."}, {"key": "flow_builder.trigger_placeholder_exact", "value": "Hello world"}, {"key": "flow_builder.trigger_placeholder_keywords", "value": "Enter keywords separated by commas (e.g., help, support, agent)"}, {"key": "flow_builder.trigger_placeholder_regex", "value": "\\b\\w+\\b"}, {"key": "flow_builder.flowise_delete_node", "value": "Delete node"}, {"key": "flow_builder.flowise_integration", "value": "Flowise Integration"}, {"key": "flow_builder.flowise_no_chatflow_selected", "value": "No chatflow selected"}, {"key": "flow_builder.flowise_flow_label", "value": "Flow:"}, {"key": "flow_builder.flowise_config_single", "value": "{{count}} config"}, {"key": "flow_builder.flowise_config_plural", "value": "{{count}} configs"}, {"key": "flow_builder.flowise_mapping_single", "value": "{{count}} mapping"}, {"key": "flow_builder.flowise_mapping_plural", "value": "{{count}} mappings"}, {"key": "flow_builder.flowise_api_connected", "value": "API Connected"}, {"key": "flow_builder.flowise_timeout_label", "value": "Timeout: {{timeout}}s"}, {"key": "flow_builder.flowise_quick_templates", "value": "Quick Templates"}, {"key": "flow_builder.flowise_choose_operation", "value": "Choose a Flowise operation..."}, {"key": "flow_builder.flowise_template_start_ai_chat", "value": "Start AI Chat"}, {"key": "flow_builder.flowise_template_send_query", "value": "Send AI Query"}, {"key": "flow_builder.flowise_template_get_response", "value": "Get AI Response"}, {"key": "flow_builder.flowise_template_stream_chat", "value": "Stream AI Chat"}, {"key": "flow_builder.make_delete_node", "value": "Delete node"}, {"key": "flow_builder.make_integration", "value": "Make.com Workflow Automation Integration"}, {"key": "flow_builder.make_configuration_completeness", "value": "Configuration completeness: {{progress}}%"}, {"key": "flow_builder.make_complete_required_fields", "value": "Complete required fields to reach 70%"}, {"key": "flow_builder.make_configuration_ready", "value": "Configuration ready!"}, {"key": "flow_builder.make_name", "value": "Make.com"}, {"key": "flow_builder.make_description", "value": "Execute Make.com scenario with multimedia support"}, {"key": "flow_builder.make_tooltip", "value": "Execute a Make.com scenario with AI chat capabilities. Supports text, images, videos, audio, and documents. Perfect for AI agents, chatbots, and conversational workflows."}, {"key": "flow_builder.make_copy_failed", "value": "Failed to copy text: "}, {"key": "flow_builder.make_field_required", "value": "This field is required"}, {"key": "flow_builder.make_invalid_url", "value": "Please enter a valid URL (e.g., https://hook.make.com/...)"}, {"key": "flow_builder.make_timeout_range", "value": "Timeout must be between 1 and 300 seconds"}, {"key": "flow_builder.make_api_token_required_test", "value": "API token is required for testing"}, {"key": "flow_builder.make_test_connection_failed", "value": "Failed to test connection. Please check your network and try again."}, {"key": "flow_builder.make_api_token_required_list", "value": "API token is required for listing scenarios"}, {"key": "flow_builder.make_list_scenarios_failed", "value": "Failed to fetch scenarios"}, {"key": "flow_builder.make_list_scenarios_network_error", "value": "Failed to fetch scenarios. Please check your network and try again."}, {"key": "flow_builder.make_hide", "value": "<PERSON>de"}, {"key": "flow_builder.make_edit", "value": "Edit"}, {"key": "flow_builder.make_hide_configuration_panel", "value": "Hide configuration panel"}, {"key": "flow_builder.make_show_configuration_panel", "value": "Show configuration panel"}, {"key": "flow_builder.make_configured", "value": "Configured"}, {"key": "flow_builder.make_not_configured", "value": "Not configured"}, {"key": "flow_builder.make_connection_configured", "value": "Make.com connection is properly configured"}, {"key": "flow_builder.make_configure_connection", "value": "Please configure Make.com API token and scenario details"}, {"key": "flow_builder.make_scenario_label", "value": "Scenario:"}, {"key": "flow_builder.make_target_scenario", "value": "Target scenario: {{name}}"}, {"key": "flow_builder.make_webhook_connected", "value": "Webhook Connected"}, {"key": "flow_builder.make_webhook_url_configured", "value": "Webhook URL configured"}, {"key": "flow_builder.make_timeout_label", "value": "Timeout: {{timeout}}s"}, {"key": "flow_builder.make_request_timeout", "value": "Request timeout: {{timeout}} seconds"}, {"key": "flow_builder.make_max_wait_time", "value": "Maximum time to wait for Make.com response"}, {"key": "flow_builder.make_multimedia_support", "value": "📎 Media Support"}, {"key": "flow_builder.make_multimedia_enabled", "value": "Multimedia support enabled"}, {"key": "flow_builder.make_supports_types", "value": "Supports: {{types}}"}, {"key": "flow_builder.make_max_size", "value": "Max size: {{size}}MB"}, {"key": "flow_builder.make_api_configuration", "value": "Make.com API Configuration"}, {"key": "flow_builder.make_configure_connection_help", "value": "Configure connection to your Make.com account"}, {"key": "flow_builder.make_required_fields_marked", "value": "Required fields are marked with *"}, {"key": "flow_builder.make_api_token_label", "value": "API Token *"}, {"key": "flow_builder.make_api_token_help_title", "value": "Your Make.com API token"}, {"key": "flow_builder.make_api_token_help_description", "value": "Get this from your Make.com account settings"}, {"key": "flow_builder.make_api_token_placeholder", "value": "Enter your Make.com API token"}, {"key": "flow_builder.make_copied", "value": "Copied!"}, {"key": "flow_builder.make_copy_token_clipboard", "value": "Copy token to clipboard"}, {"key": "flow_builder.make_team_id_label", "value": "Team ID"}, {"key": "flow_builder.make_team_id_help", "value": "Your Make.com team ID for team-scoped scenarios"}, {"key": "flow_builder.make_team_id_placeholder", "value": "Enter team ID (optional)"}, {"key": "flow_builder.make_organization_id_label", "value": "Organization ID"}, {"key": "flow_builder.make_organization_id_help", "value": "Your Make.com organization ID for org-scoped scenarios"}, {"key": "flow_builder.make_organization_id_placeholder", "value": "Enter organization ID (optional)"}, {"key": "flow_builder.make_testing", "value": "Testing..."}, {"key": "flow_builder.make_test_connection", "value": "Test Connection"}, {"key": "flow_builder.make_loading", "value": "Loading..."}, {"key": "flow_builder.make_list_scenarios", "value": "List Scenarios"}, {"key": "flow_builder.webhook_delete_node", "value": "Delete node"}, {"key": "flow_builder.webhook_integration", "value": "Webhook Integration"}, {"key": "flow_builder.http_delete_node", "value": "Delete node"}, {"key": "flow_builder.http_integration", "value": "HTTP Request"}, {"key": "flow_builder.auth_none", "value": "None"}, {"key": "flow_builder.auth_bearer_token", "value": "<PERSON><PERSON>"}, {"key": "flow_builder.auth_basic_auth", "value": "Basic Auth"}, {"key": "flow_builder.auth_api_key", "value": "API Key"}, {"key": "flow_builder.template_get_user_data", "value": "Get User Data"}, {"key": "flow_builder.template_post_form_data", "value": "Post Form Data"}, {"key": "flow_builder.template_update_record", "value": "Update Record"}, {"key": "flow_builder.media_images", "value": "Images"}, {"key": "flow_builder.media_videos", "value": "Videos"}, {"key": "flow_builder.media_audio", "value": "Audio"}, {"key": "flow_builder.media_documents", "value": "Documents"}, {"key": "flow_builder.media_images_desc", "value": "JPEG, PNG, WebP images"}, {"key": "flow_builder.media_videos_desc", "value": "MP4, 3GPP videos"}, {"key": "flow_builder.media_audio_desc", "value": "MP3, AAC, OGG audio files"}, {"key": "flow_builder.media_documents_desc", "value": "PDF, DOC, DOCX files"}, {"key": "flow_builder.wait_node_title", "value": "Wait"}, {"key": "flow_builder.wait_no_date_selected", "value": "No date selected"}, {"key": "flow_builder.wait_for_duration", "value": "Wait for {{value}} {{unit}}"}, {"key": "flow_builder.wait_schedule_not_set", "value": "Schedule not set"}, {"key": "flow_builder.wait_scheduled_for", "value": "Scheduled for {{date}} at {{time}} ({{timezone}})"}, {"key": "flow_builder.wait_duration_mode", "value": "Duration"}, {"key": "flow_builder.wait_schedule_mode", "value": "Schedule"}, {"key": "flow_builder.wait_time_value", "value": "Time Value"}, {"key": "flow_builder.wait_time_unit", "value": "Time Unit"}, {"key": "flow_builder.wait_seconds", "value": "Seconds"}, {"key": "flow_builder.wait_minutes", "value": "Minutes"}, {"key": "flow_builder.wait_hours", "value": "Hours"}, {"key": "flow_builder.wait_days", "value": "Days"}, {"key": "flow_builder.wait_date", "value": "Date"}, {"key": "flow_builder.wait_time", "value": "Time"}, {"key": "flow_builder.wait_timezone", "value": "Timezone"}, {"key": "flow_builder.quick_reply_default_message", "value": "Please select an option to continue:"}, {"key": "flow_builder.quick_reply_default_option1", "value": "I have a question about my order."}, {"key": "flow_builder.quick_reply_default_option2", "value": "I have a question about a product."}, {"key": "flow_builder.quick_reply_default_option3", "value": "I have another question."}, {"key": "flow_builder.quick_reply_invalid_response", "value": "I didn't understand your selection. Please choose one of the available options:"}, {"key": "flow_builder.quick_reply_new_option", "value": "New option"}, {"key": "flow_builder.quick_reply_node_title", "value": "Quick Reply"}, {"key": "flow_builder.quick_reply_message_label", "value": "Message"}, {"key": "flow_builder.quick_reply_options_label", "value": "Options"}, {"key": "flow_builder.quick_reply_add_option", "value": "Add Option"}, {"key": "flow_builder.quick_reply_invalid_response_label", "value": "Invalid Response Message"}, {"key": "flow_builder.audio_upload_default_caption", "value": "Listen to this audio!"}, {"key": "flow_builder.audio_upload_duplicate_node", "value": "Duplicate node"}, {"key": "flow_builder.audio_upload_delete_node", "value": "Delete node"}, {"key": "flow_builder.audio_upload_node_title", "value": "Audio Message"}, {"key": "flow_builder.audio_upload_upload_complete", "value": "Upload complete"}, {"key": "flow_builder.audio_upload_upload_success", "value": "Audio uploaded successfully"}, {"key": "flow_builder.audio_upload_upload_failed", "value": "Upload failed"}, {"key": "flow_builder.audio_upload_upload_error", "value": "There was an error uploading your audio"}, {"key": "flow_builder.audio_upload_upload_label", "value": "Upload Audio:"}, {"key": "flow_builder.audio_upload_caption_label", "value": "Caption:"}, {"key": "flow_builder.audio_upload_caption_placeholder", "value": "Enter caption"}, {"key": "flow_builder.audio_upload_browser_not_supported", "value": "Your browser does not support the audio element."}, {"key": "flow_builder.audio_upload_audio_label", "value": "Audio"}, {"key": "flow_builder.audio_upload_no_audio_selected", "value": "No audio selected"}, {"key": "flow_builder.image_upload_default_caption", "value": "Check out this image!"}, {"key": "flow_builder.image_upload_duplicate_node", "value": "Duplicate node"}, {"key": "flow_builder.image_upload_delete_node", "value": "Delete node"}, {"key": "flow_builder.image_upload_node_title", "value": "Image Message"}, {"key": "flow_builder.image_upload_upload_complete", "value": "Upload complete"}, {"key": "flow_builder.image_upload_upload_success", "value": "Image uploaded successfully"}, {"key": "flow_builder.image_upload_upload_failed", "value": "Upload failed"}, {"key": "flow_builder.image_upload_upload_error", "value": "There was an error uploading your image"}, {"key": "flow_builder.image_upload_upload_label", "value": "Upload Image:"}, {"key": "flow_builder.image_upload_caption_label", "value": "Caption:"}, {"key": "flow_builder.image_upload_caption_placeholder", "value": "Enter caption"}, {"key": "flow_builder.image_upload_preview_alt", "value": "Preview"}, {"key": "flow_builder.image_upload_image_label", "value": "Image"}, {"key": "flow_builder.image_upload_no_image_selected", "value": "No image selected"}, {"key": "flow_builder.video_upload_default_caption", "value": "Check out this video!"}, {"key": "flow_builder.video_upload_duplicate_node", "value": "Duplicate node"}, {"key": "flow_builder.video_upload_delete_node", "value": "Delete node"}, {"key": "flow_builder.video_upload_node_title", "value": "Video Message"}, {"key": "flow_builder.video_upload_upload_complete", "value": "Upload complete"}, {"key": "flow_builder.video_upload_upload_success", "value": "Video uploaded successfully"}, {"key": "flow_builder.video_upload_upload_failed", "value": "Upload failed"}, {"key": "flow_builder.video_upload_upload_error", "value": "There was an error uploading your video"}, {"key": "flow_builder.video_upload_upload_label", "value": "Upload Video:"}, {"key": "flow_builder.video_upload_caption_label", "value": "Caption:"}, {"key": "flow_builder.video_upload_caption_placeholder", "value": "Enter caption"}, {"key": "flow_builder.video_upload_browser_not_supported", "value": "Your browser does not support the video element."}, {"key": "flow_builder.video_upload_video_label", "value": "Video"}, {"key": "flow_builder.video_upload_no_video_selected", "value": "No video selected"}, {"key": "flow_builder.document_upload_default_caption", "value": "Here's a document for you"}, {"key": "flow_builder.document_upload_duplicate_node", "value": "Duplicate node"}, {"key": "flow_builder.document_upload_delete_node", "value": "Delete node"}, {"key": "flow_builder.document_upload_node_title", "value": "Document Message"}, {"key": "flow_builder.document_upload_upload_complete", "value": "Upload complete"}, {"key": "flow_builder.document_upload_upload_success", "value": "Document uploaded successfully"}, {"key": "flow_builder.document_upload_upload_failed", "value": "Upload failed"}, {"key": "flow_builder.document_upload_upload_error", "value": "There was an error uploading your document"}, {"key": "flow_builder.document_upload_upload_label", "value": "Upload Document:"}, {"key": "flow_builder.document_upload_caption_label", "value": "Caption:"}, {"key": "flow_builder.document_upload_caption_placeholder", "value": "Enter caption"}, {"key": "flow_builder.document_upload_document_label", "value": "Document"}, {"key": "flow_builder.document_upload_no_document_selected", "value": "No document selected"}, {"key": "flow_builder.ai_assistant_node_title", "value": "AI Assistant"}, {"key": "flow_builder.ai_assistant_duplicate_node", "value": "Duplicate node"}, {"key": "flow_builder.ai_assistant_delete_node", "value": "Delete node"}, {"key": "flow_builder.ai_task_active", "value": "Active"}, {"key": "flow_builder.ai_task_inactive", "value": "Inactive"}, {"key": "flow_builder.ai_task_name_label", "value": "Task Name"}, {"key": "flow_builder.ai_task_name_placeholder", "value": "e.g., Share Product Brochure"}, {"key": "flow_builder.ai_function_name_label", "value": "Function Name"}, {"key": "flow_builder.ai_function_name_placeholder", "value": "e.g., share_document"}, {"key": "flow_builder.ai_task_description_label", "value": "Task Description"}, {"key": "flow_builder.ai_task_description_placeholder", "value": "Describe what this task does and when it should be triggered"}, {"key": "flow_builder.shopify_orders", "value": "Orders"}, {"key": "flow_builder.shopify_products", "value": "Products"}, {"key": "flow_builder.shopify_customers", "value": "Customers"}, {"key": "flow_builder.shopify_inventory", "value": "Inventory"}, {"key": "flow_builder.shopify_fulfillments", "value": "Fulfillments"}, {"key": "flow_builder.shopify_webhooks", "value": "Webhooks"}, {"key": "flow_builder.shopify_product_variants", "value": "Product Variants"}, {"key": "flow_builder.shopify_collections", "value": "Collections"}, {"key": "flow_builder.shopify_get_fetch_data", "value": "Get (Fetch Data)"}, {"key": "flow_builder.shopify_create_new", "value": "Create New"}, {"key": "flow_builder.shopify_update_existing", "value": "Update Existing"}, {"key": "flow_builder.shopify_delete", "value": "Delete"}, {"key": "flow_builder.shopify_get_recent_orders", "value": "Get Recent Orders"}, {"key": "flow_builder.shopify_update_product_stock", "value": "Update Product Stock"}, {"key": "flow_builder.shopify_create_customer", "value": "Create Customer"}, {"key": "flow_builder.shopify_create_order_fulfillment", "value": "Create Order Fulfillment"}, {"key": "flow_builder.shopify_node_title", "value": "Shopify"}, {"key": "flow_builder.shopify_duplicate_node", "value": "Duplicate node"}, {"key": "flow_builder.shopify_delete_node", "value": "Delete node"}, {"key": "flow_builder.shopify_connection_failed", "value": "Connection failed"}, {"key": "flow_builder.shopify_hide", "value": "<PERSON>de"}, {"key": "flow_builder.shopify_edit", "value": "Edit"}, {"key": "flow_builder.google_sheets_append_row", "value": "Append Row"}, {"key": "flow_builder.google_sheets_append_row_description", "value": "Add new data row to sheet"}, {"key": "flow_builder.google_sheets_append_row_tooltip", "value": "Add a new row of data to the end of your Google Sheet. Perfect for lead capture, form submissions, and data collection from WhatsApp conversations."}, {"key": "flow_builder.google_sheets_read_rows", "value": "Read Rows"}, {"key": "flow_builder.google_sheets_read_rows_description", "value": "Fetch data with optional filters"}, {"key": "flow_builder.google_sheets_read_rows_tooltip", "value": "Read data from your Google Sheet with optional filtering and row range selection. Use this to lookup existing data or retrieve information."}, {"key": "flow_builder.google_sheets_update_row", "value": "Update Row"}, {"key": "flow_builder.google_sheets_update_row_description", "value": "Modify existing row data"}, {"key": "flow_builder.google_sheets_update_row_tooltip", "value": "Update existing rows in your Google Sheet by row number or by matching column values. Great for updating order status, user information, or any existing records."}, {"key": "flow_builder.google_sheets_get_sheet_info", "value": "Get Sheet Info"}, {"key": "flow_builder.google_sheets_get_sheet_info_description", "value": "Retrieve sheet metadata and headers"}, {"key": "flow_builder.google_sheets_get_sheet_info_tooltip", "value": "Get information about your Google Sheet including column headers, sheet names, and metadata. Useful for dynamic configuration and validation."}, {"key": "flow_builder.google_sheets_lead_capture_form", "value": "Lead Capture Form"}, {"key": "flow_builder.typebot_create_session", "value": "Create Session"}, {"key": "flow_builder.typebot_update_session", "value": "Update Session"}, {"key": "flow_builder.typebot_close_session", "value": "Close Session"}, {"key": "flow_builder.typebot_get_status", "value": "Get Status"}, {"key": "flow_builder.typebot_text_input", "value": "Text Input"}, {"key": "flow_builder.typebot_number_input", "value": "Number Input"}, {"key": "flow_builder.typebot_email_input", "value": "Email Input"}, {"key": "flow_builder.typebot_url_input", "value": "URL Input"}, {"key": "flow_builder.typebot_date_input", "value": "Date Input"}, {"key": "flow_builder.typebot_phone_input", "value": "Phone Input"}, {"key": "flow_builder.typebot_multiple_choice", "value": "Multiple Choice"}, {"key": "flow_builder.typebot_file_upload", "value": "File Upload"}, {"key": "flow_builder.typebot_start_new_chat", "value": "Start New Chat"}, {"key": "flow_builder.typebot_send_user_message", "value": "Send User Message"}, {"key": "flow_builder.typebot_get_bot_response", "value": "Get Bot Response"}, {"key": "flow_builder.typebot_close_conversation", "value": "Close Conversation"}, {"key": "flow_builder.bot_disable_5_minutes", "value": "5 minutes"}, {"key": "flow_builder.bot_disable_15_minutes", "value": "15 minutes"}, {"key": "flow_builder.bot_disable_30_minutes", "value": "30 minutes"}, {"key": "flow_builder.bot_disable_1_hour", "value": "1 hour"}, {"key": "flow_builder.bot_disable_2_hours", "value": "2 hours"}, {"key": "flow_builder.bot_disable_4_hours", "value": "4 hours"}, {"key": "flow_builder.bot_disable_8_hours", "value": "8 hours"}, {"key": "flow_builder.bot_disable_24_hours", "value": "24 hours"}, {"key": "flow_builder.bot_disable_manual", "value": "Until manually re-enabled"}, {"key": "flow_builder.bot_disable_custom", "value": "Custom duration"}, {"key": "flow_builder.bot_disable_node_title", "value": "Agent <PERSON><PERSON>"}, {"key": "flow_builder.bot_disable_duplicate_node", "value": "Duplicate node"}, {"key": "flow_builder.bot_disable_delete_node", "value": "Delete node"}, {"key": "flow_builder.bot_disable_disable_bot", "value": "Disable Bo<PERSON>"}, {"key": "flow_builder.bot_disable_auto_assign", "value": "Auto-assign to available agent"}, {"key": "flow_builder.bot_disable_default_handoff_message", "value": "A customer is requesting human assistance."}, {"key": "flow_builder.bot_reset_bot_only", "value": "Re-enable bot responses only"}, {"key": "flow_builder.bot_reset_bot_only_description", "value": "Only remove bot disable flag, keep all context and history"}, {"key": "flow_builder.bot_reset_bot_and_context", "value": "Reset bot + clear conversation context"}, {"key": "flow_builder.bot_reset_bot_and_context_description", "value": "Re-enable bot and clear flow variables/context"}, {"key": "flow_builder.bot_reset_full_reset", "value": "Full reset (bot + context + history)"}, {"key": "flow_builder.bot_reset_full_reset_description", "value": "Complete reset including conversation history"}, {"key": "flow_builder.bot_reset_node_title", "value": "<PERSON><PERSON>"}, {"key": "flow_builder.bot_reset_duplicate_node", "value": "Duplicate node"}, {"key": "flow_builder.bot_reset_delete_node", "value": "Delete node"}, {"key": "flow_builder.bot_reset_default_confirmation", "value": "Bot assistance has been re-enabled. How can I help you?"}, {"key": "flow_builder.ai_provider_openai", "value": "OpenAI"}, {"key": "flow_builder.ai_provider_anthropic", "value": "Anthropic"}, {"key": "flow_builder.ai_provider_gemini", "value": "Google Gemini"}, {"key": "flow_builder.ai_provider_deepseek", "value": "DeepSeek"}, {"key": "flow_builder.ai_provider_xai", "value": "xAI"}, {"key": "flow_builder.ai_model_gpt4o_latest", "value": "GPT-4o (Latest)"}, {"key": "flow_builder.ai_model_gpt41_nano", "value": "GPT-4.1 Nano"}, {"key": "flow_builder.ai_model_gpt41_mini", "value": "GPT-4.1 Mini"}, {"key": "flow_builder.ai_model_gpt4_turbo", "value": "GPT-4 Turbo"}, {"key": "flow_builder.ai_model_gpt35_turbo", "value": "GPT-3.5 Turbo"}, {"key": "flow_builder.ai_model_claude37_sonnet", "value": "Claude 3.7 Son<PERSON> (Latest)"}, {"key": "flow_builder.ai_model_claude3_opus", "value": "Claude 3 Opus"}, {"key": "flow_builder.ai_model_claude3_sonnet", "value": "Claude 3 Sonnet"}, {"key": "flow_builder.ai_model_claude3_haiku", "value": "Claude 3 Haiku"}, {"key": "flow_builder.ai_assistant_node_title", "value": "AI Assistant"}, {"key": "flow_builder.ai_assistant_duplicate_node", "value": "Duplicate node"}, {"key": "flow_builder.ai_assistant_delete_node", "value": "Delete node"}, {"key": "flow_builder.documind_ask_question", "value": "Ask Question"}, {"key": "flow_builder.documind_ask_question_description", "value": "Ask a question about documents in the selected folder"}, {"key": "flow_builder.documind_analyze_documents", "value": "Analyze Documents"}, {"key": "flow_builder.documind_analyze_documents_description", "value": "Analyze all documents in the folder and provide insights"}, {"key": "flow_builder.documind_search_content", "value": "Search Content"}, {"key": "flow_builder.documind_search_content_description", "value": "Search for specific content across documents"}, {"key": "flow_builder.documind_node_title", "value": "Documind AI"}, {"key": "flow_builder.documind_duplicate_node", "value": "Duplicate node"}, {"key": "flow_builder.documind_delete_node", "value": "Delete node"}, {"key": "flow_builder.documind_conversation_history", "value": "Conversation History"}, {"key": "flow_builder.documind_enable_history", "value": "Include conversation history"}, {"key": "flow_builder.documind_history_limit", "value": "Message Limit"}, {"key": "flow_builder.documind_history_help", "value": "Previous messages to include for context"}, {"key": "flow_builder.chatpdf_ask_question", "value": "Ask Question"}, {"key": "flow_builder.chatpdf_ask_question_description", "value": "Ask a question about the selected PDF document"}, {"key": "flow_builder.chatpdf_summarize", "value": "Summarize"}, {"key": "flow_builder.chatpdf_summarize_description", "value": "Get a comprehensive summary of the PDF document"}, {"key": "flow_builder.chatpdf_analyze_content", "value": "Analyze Content"}, {"key": "flow_builder.chatpdf_analyze_content_description", "value": "Analyze and extract insights from the PDF content"}, {"key": "flow_builder.chatpdf_gpt4o_description", "value": "Latest GPT-4 model (2 credits per message)"}, {"key": "flow_builder.chatpdf_gpt4_turbo_description", "value": "GPT-4 Turbo model (4 credits per message)"}, {"key": "flow_builder.chatpdf_node_title", "value": "ChatPDF"}, {"key": "flow_builder.chatpdf_duplicate_node", "value": "Duplicate node"}, {"key": "flow_builder.chatpdf_delete_node", "value": "Delete node"}, {"key": "flow_builder.pipeline_contact_id", "value": "Contact ID"}, {"key": "flow_builder.pipeline_contact_id_description", "value": "Unique identifier for the contact"}, {"key": "flow_builder.pipeline_contact_phone", "value": "Contact Phone"}, {"key": "flow_builder.pipeline_contact_phone_description", "value": "Phone number of the contact"}, {"key": "flow_builder.pipeline_contact_name", "value": "Contact Name"}, {"key": "flow_builder.pipeline_contact_name_description", "value": "Full name of the contact"}, {"key": "flow_builder.pipeline_node_title", "value": "Update Pipeline Stage"}, {"key": "flow_builder.pipeline_duplicate_node", "value": "Duplicate node"}, {"key": "flow_builder.pipeline_delete_node", "value": "Delete node"}, {"key": "flow_builder.followup_default_text_template", "value": "Thank you for your interest! How can we help you further?"}, {"key": "flow_builder.woocommerce_orders", "value": "Orders"}, {"key": "flow_builder.woocommerce_products", "value": "Products"}, {"key": "flow_builder.woocommerce_customers", "value": "Customers"}, {"key": "flow_builder.woocommerce_coupons", "value": "Coupons"}, {"key": "flow_builder.woocommerce_product_categories", "value": "Product Categories"}, {"key": "flow_builder.woocommerce_product_variations", "value": "Product Variations"}, {"key": "flow_builder.woocommerce_reports", "value": "Reports"}, {"key": "flow_builder.woocommerce_payment_gateways", "value": "Payment Gateways"}, {"key": "flow_builder.woocommerce_shipping_zones", "value": "Shipping Zones"}, {"key": "flow_builder.woocommerce_tax_rates", "value": "Tax Rates"}, {"key": "flow_builder.woocommerce_get_recent_orders", "value": "Get Recent Orders"}, {"key": "flow_builder.woocommerce_update_product_stock", "value": "Update Product Stock"}, {"key": "flow_builder.woocommerce_create_customer", "value": "Create Customer"}, {"key": "flow_builder.woocommerce_create_discount_coupon", "value": "Create Discount Coupon"}, {"key": "flow_builder.woocommerce_mark_order_completed", "value": "Mark Order as Completed"}, {"key": "flow_builder.woocommerce_node_title", "value": "WooCommerce"}, {"key": "calendar.no_available_slots", "value": "No available time slots found for the selected date and duration"}, {"key": "calendar.checking", "value": "Checking..."}, {"key": "calendar.refresh_availability", "value": "Refresh Availability"}, {"key": "calendar.category.work", "value": "Work"}, {"key": "calendar.category.personal", "value": "Personal"}, {"key": "calendar.category.family", "value": "Family"}, {"key": "calendar.category.health", "value": "Health"}, {"key": "calendar.category.projects", "value": "Projects"}, {"key": "calendar.category.default", "value": "<PERSON><PERSON><PERSON>"}, {"key": "calendar.week_of", "value": "Week of"}, {"key": "campaigns.unknown_error", "value": "Unknown error occurred"}, {"key": "settings.inbox.history_sync_enabled", "value": "History Sync Enabled"}, {"key": "settings.inbox.history_sync_disabled", "value": "History Sync Disabled"}, {"key": "settings.inbox.history_sync_enabled_desc", "value": "WhatsApp message history will be synced on next connection"}, {"key": "settings.inbox.history_sync_disabled_desc", "value": "History sync has been disabled for this connection"}, {"key": "settings.inbox.update_history_sync_failed", "value": "Failed to update history sync setting"}, {"key": "groups.create_conversation_failed", "value": "Failed to create conversation"}, {"key": "admin.settings.no_logo_file_selected", "value": "No logo file selected"}, {"key": "admin.settings.failed_upload_logo", "value": "Failed to upload logo"}, {"key": "admin.settings.logo_uploaded", "value": "Logo uploaded"}, {"key": "admin.settings.logo_uploaded_desc", "value": "The logo has been uploaded successfully."}, {"key": "admin.settings.error_uploading_logo", "value": "Error uploading logo"}, {"key": "admin.settings.no_favicon_file_selected", "value": "No favicon file selected"}, {"key": "admin.settings.failed_upload_favicon", "value": "Failed to upload favicon"}, {"key": "admin.settings.favicon_uploaded", "value": "<PERSON><PERSON><PERSON> uploaded"}, {"key": "admin.settings.favicon_uploaded_desc", "value": "The favicon has been uploaded successfully."}, {"key": "admin.settings.error_uploading_favicon", "value": "Error uploading favicon"}, {"key": "admin.settings.app_name_required", "value": "Application name is required"}, {"key": "admin.settings.failed_save_branding", "value": "Failed to save branding settings"}, {"key": "admin.settings.branding_saved", "value": "Branding settings saved"}, {"key": "admin.settings.branding_saved_desc", "value": "The branding settings have been saved successfully."}, {"key": "admin.settings.error_saving_branding", "value": "Error saving branding settings"}, {"key": "admin.settings.description", "value": "Configure system settings, payment gateways, and application preferences"}, {"key": "admin.settings.logo", "value": "Logo"}, {"key": "admin.settings.logo_description", "value": "Upload your company logo (recommended size: 200x50px)"}, {"key": "admin.settings.logo_preview_alt", "value": "Logo Preview"}, {"key": "admin.settings.choose_logo", "value": "<PERSON><PERSON>"}, {"key": "admin.settings.upload", "value": "Upload"}, {"key": "admin.settings.favicon", "value": "Favicon"}, {"key": "admin.settings.favicon_description", "value": "Upload your favicon (recommended size: 32x32px)"}, {"key": "admin.settings.embed_code_copied", "value": "Embed code copied"}, {"key": "admin.settings.embed_code_copied_desc", "value": "The embed code has been copied to your clipboard."}, {"key": "admin.settings.copy_failed", "value": "Co<PERSON> failed"}, {"key": "admin.settings.copy_failed_desc", "value": "Failed to copy embed code to clipboard. Please copy manually."}, {"key": "admin.settings.failed_save_stripe", "value": "Failed to save Stripe settings"}, {"key": "admin.settings.stripe_saved", "value": "Stripe settings saved"}, {"key": "admin.settings.stripe_saved_desc", "value": "The Stripe settings have been saved successfully."}, {"key": "admin.settings.error_saving_stripe", "value": "Error saving Stripe settings"}, {"key": "admin.settings.stripe_connection_successful", "value": "Stripe connection successful"}, {"key": "admin.settings.connected_to_stripe_account", "value": "Connected to Stripe account: {{email}}"}, {"key": "admin.settings.error_connecting_stripe", "value": "Error connecting to Stripe"}, {"key": "admin.settings.failed_test_stripe", "value": "Failed to test Stripe connection"}, {"key": "admin.settings.test_email_required", "value": "Test email address is required"}, {"key": "admin.settings.smtp_connection_successful", "value": "SMTP connection successful"}, {"key": "admin.settings.smtp_test_passed", "value": "SMTP connection test passed"}, {"key": "admin.settings.error_testing_smtp", "value": "Error testing SMTP connection"}, {"key": "admin.settings.failed_test_smtp", "value": "Failed to test SMTP connection"}, {"key": "admin.settings.default_plan_required", "value": "Default plan is required when registration is enabled"}, {"key": "admin.settings.registration_saved", "value": "Registration settings saved"}, {"key": "admin.settings.registration_saved_desc", "value": "The registration settings have been saved successfully."}, {"key": "admin.settings.error_saving_registration", "value": "Error saving registration settings"}, {"key": "admin.settings.failed_save_registration", "value": "Failed to save registration settings"}, {"key": "admin.settings.company_name_placeholder", "value": "Company Name"}, {"key": "admin.settings.test_connection", "value": "Test Connection"}, {"key": "admin.settings.configure", "value": "Configure"}, {"key": "admin.settings.configure_google_oauth", "value": "Configure Google OAuth for social login"}, {"key": "admin.settings.configure_facebook_oauth", "value": "Configure Facebook OAuth for social login"}, {"key": "admin.settings.configure_apple_oauth", "value": "Configure Apple OAuth for social login"}, {"key": "admin.settings.configure_stripe_gateway", "value": "Configure Stripe payment gateway"}, {"key": "admin.settings.configure_paypal_gateway", "value": "Configure PayPal payment gateway"}, {"key": "admin.settings.configure_smtp_settings", "value": "Configure SMTP settings for sending system emails, notifications, and password resets"}, {"key": "admin.settings.configure_general_settings", "value": "Configure general application settings"}, {"key": "admin.settings.enable_company_registration", "value": "Enable Company Registration"}, {"key": "admin.settings.registration_description", "value": "When enabled, new companies can register for accounts. When disabled, the registration page will show a message that registration is currently unavailable."}, {"key": "admin.settings.approval_description", "value": "When enabled, new company registrations will require super admin approval before they can access the platform."}, {"key": "admin.settings.frontend_website", "value": "Frontend Website"}, {"key": "admin.settings.frontend_website_description", "value": "Enable or disable the public landing page at /landing"}, {"key": "admin.settings.website_disabled", "value": "Website Disabled"}, {"key": "admin.settings.website_disabled_description", "value": "The public landing page is currently disabled. Visitors to /landing will see a \"not found\" page."}, {"key": "inbox.cannot_send_media", "value": "Cannot send media, not connected to server"}, {"key": "inbox.not_connected_server", "value": "Not connected to server"}, {"key": "inbox.conversation_not_found", "value": "Conversation not found"}, {"key": "inbox.network_error", "value": "Network error"}, {"key": "inbox.failed_send_media", "value": "Failed to send media message"}, {"key": "inbox.server_error", "value": "Server error ({{status}}): {{statusText}}"}, {"key": "inbox.send_message_failed", "value": "Failed to send message"}, {"key": "flow_builder.duplicate_node", "value": "Duplicate node"}, {"key": "flow_builder.delete_node", "value": "Delete node"}, {"key": "flow_builder.send_message", "value": "Send Message"}, {"key": "flow_builder.default_message", "value": "Hello! How can I help you?"}, {"key": "flow_builder.var_contact_name", "value": "<PERSON>'s name"}, {"key": "flow_builder.var_contact_phone", "value": "Contact's phone number"}, {"key": "flow_builder.var_message_content", "value": "Received message content"}, {"key": "flow_builder.var_date_today", "value": "Current date"}, {"key": "flow_builder.var_time_now", "value": "Current time"}, {"key": "flow_builder.var_availability", "value": "Google Calendar availability data from previous node"}, {"key": "flow_builder.type_message_placeholder", "value": "Type your message here..."}, {"key": "flow_builder.insert_variable", "value": "Insert Variable:"}, {"key": "flow_builder.variables_help", "value": "Variables will be replaced with actual values when message is sent."}, {"key": "flow_builder.condition", "value": "Condition"}, {"key": "flow_builder.condition_type", "value": "Condition Type:"}, {"key": "flow_builder.advanced", "value": "Advanced"}, {"key": "flow_builder.enter_custom_condition", "value": "Enter custom condition"}, {"key": "flow_builder.condition_examples", "value": "Examples: Contains('help'), IsMedia(), ExactMatch('hello')"}, {"key": "flow_builder.enter_text_value", "value": "Enter text value"}, {"key": "flow_builder.case_sensitive", "value": "Case sensitive"}, {"key": "flow_builder.has_media_desc", "value": "Checks if the message has any attached media."}, {"key": "flow_builder.media_type_desc", "value": "Checks if the message contains media of the selected type."}, {"key": "flow_builder.before", "value": "Before"}, {"key": "flow_builder.after", "value": "After"}, {"key": "flow_builder.between", "value": "Between"}, {"key": "flow_builder.time_format_between", "value": "Use format: HH:MM,HH:MM (24h)"}, {"key": "flow_builder.time_format_single", "value": "Use format: HH:MM (24h)"}, {"key": "flow_builder.name", "value": "Name"}, {"key": "flow_builder.phone", "value": "Phone"}, {"key": "flow_builder.email", "value": "Email"}, {"key": "flow_builder.tags", "value": "Tags"}, {"key": "flow_builder.attribute_value", "value": "Attribute value"}, {"key": "flow_builder.yes", "value": "Yes"}, {"key": "flow_builder.no", "value": "No"}, {"key": "flow_builder.input", "value": "Input"}, {"key": "flow_builder.collect_user_response", "value": "Collect user response"}, {"key": "flow_builder.enter_input_prompt", "value": "Enter input prompt"}, {"key": "flow_builder.action", "value": "Action"}, {"key": "flow_builder.perform_api_call", "value": "Perform API call"}, {"key": "flow_builder.enter_action", "value": "Enter action"}, {"key": "flow_builder.action_examples", "value": "Examples: CreateTicket(), TagContact('VIP'), AddToGroup('leads')"}, {"key": "flow_builder.search_nodes", "value": "Search nodes..."}, {"key": "flow_builder.add_node", "value": "Add Node"}, {"key": "flow_builder.no_nodes_found", "value": "No nodes found"}, {"key": "flow_builder.try_different_search", "value": "Try a different search term"}, {"key": "flow_builder.checking", "value": "Checking..."}, {"key": "flow_builder.connected", "value": "Connected"}, {"key": "flow_builder.connect", "value": "Connect"}, {"key": "flow_builder.connect_google_calendar_first", "value": "Connect to Google Calendar first"}, {"key": "flow_builder.name_required", "value": "Name required"}, {"key": "flow_builder.provide_flow_name", "value": "Please provide a name for your flow"}, {"key": "flow_builder.error_loading_flow", "value": "Error loading flow"}, {"key": "flow_builder.could_not_parse_flow_data", "value": "Could not parse flow data"}, {"key": "flow_builder.flow_created", "value": "Flow created"}, {"key": "flow_builder.flow_created_successfully", "value": "Your flow has been created successfully."}, {"key": "flow_builder.error_creating_flow", "value": "Error creating flow"}, {"key": "flow_builder.something_went_wrong", "value": "Something went wrong"}, {"key": "flow_builder.flow_updated", "value": "Flow updated"}, {"key": "flow_builder.flow_updated_successfully", "value": "Your flow has been updated successfully."}, {"key": "flow_builder.error_updating_flow", "value": "Error updating flow"}, {"key": "flow_builder.node_deleted", "value": "Node deleted"}, {"key": "flow_builder.node_connections_removed", "value": "Node and its connections have been removed."}, {"key": "flow_builder.node_duplicated", "value": "Node duplicated"}, {"key": "flow_builder.node_copy_created", "value": "A copy of the node has been created."}, {"key": "flow_builder.flow_name", "value": "Flow name"}, {"key": "flow_builder.node_selection", "value": "Node Selection"}, {"key": "flow_builder.active", "value": "Active"}, {"key": "flow_builder.draft", "value": "Draft"}, {"key": "flow_builder.creating_new_flow", "value": "Creating New Flow"}, {"key": "flow_builder.current_flow_status", "value": "Current flow status"}, {"key": "flow_builder.send_image", "value": "Send Image"}, {"key": "flow_builder.image_label", "value": "Image:"}, {"key": "flow_builder.or_enter_image_url", "value": "Or enter image URL:"}, {"key": "flow_builder.enter_image_url", "value": "Enter image URL or path"}, {"key": "flow_builder.caption_optional", "value": "Caption (optional):"}, {"key": "flow_builder.add_caption_image", "value": "Add a caption to your image..."}, {"key": "flow_builder.insert_variable_caption", "value": "Insert Variable in Caption:"}, {"key": "flow_builder.no_image_provided", "value": "No image provided"}, {"key": "flow_builder.caption_label", "value": "Caption:"}, {"key": "flow_builder.send_video", "value": "Send Video"}, {"key": "flow_builder.video_url_label", "value": "Video URL:"}, {"key": "flow_builder.enter_video_url", "value": "Enter video URL or path"}, {"key": "flow_builder.add_caption_video", "value": "Add a caption to your video..."}, {"key": "flow_builder.no_url_provided", "value": "No URL provided"}, {"key": "flow_builder.send_audio", "value": "Send Audio"}, {"key": "flow_builder.audio_url_label", "value": "Audio URL:"}, {"key": "flow_builder.enter_audio_url", "value": "Enter audio URL or path"}, {"key": "flow_builder.add_caption_audio", "value": "Add a caption to your audio..."}, {"key": "flow_builder.send_document", "value": "Send Document"}, {"key": "flow_builder.document_url_label", "value": "Document URL:"}, {"key": "flow_builder.enter_document_url", "value": "Enter document URL or path"}, {"key": "flow_builder.file_name_optional", "value": "File Name (optional):"}, {"key": "flow_builder.enter_file_name", "value": "Enter file name (e.g. report.pdf)"}, {"key": "flow_builder.add_caption_document", "value": "Add a caption to your document..."}, {"key": "flow_builder.file_name_label", "value": "File Name:"}, {"key": "flow_builder.message_received", "value": "Message Received"}, {"key": "flow_builder.when", "value": "When"}, {"key": "flow_builder.message_lowercase", "value": "message"}, {"key": "flow_builder.that", "value": "that"}, {"key": "flow_builder.any_message", "value": "Any Message"}, {"key": "flow_builder.contains_word", "value": "Contains Word"}, {"key": "flow_builder.exact_match", "value": "Exact Match"}, {"key": "flow_builder.regex_pattern", "value": "Regex Pattern"}, {"key": "flow_builder.has_media", "value": "Has Media"}, {"key": "flow_builder.changes_saved_automatically", "value": "Changes are saved automatically when you save the flow."}, {"key": "flow_builder.default_video_caption", "value": "Watch this video!"}, {"key": "flow_builder.default_audio_caption", "value": "Listen to this audio!"}, {"key": "flow_builder.default_document_caption", "value": "Check out this document!"}, {"key": "flow_builder.default_image_caption", "value": "Check out this image!"}, {"key": "flow_builder.default_document_caption_full", "value": "Here is the document you requested."}, {"key": "analytics.title", "value": "Analytics"}, {"key": "analytics.select_period", "value": "Select time period"}, {"key": "analytics.period.today", "value": "Today"}, {"key": "analytics.period.yesterday", "value": "Yesterday"}, {"key": "analytics.period.7days", "value": "Last 7 days"}, {"key": "analytics.period.30days", "value": "Last 30 days"}, {"key": "analytics.period.90days", "value": "Last 90 days"}, {"key": "analytics.period.custom", "value": "Custom Range"}, {"key": "analytics.pick_date", "value": "Pick a date"}, {"key": "analytics.export", "value": "Export"}, {"key": "analytics.vs_last_period", "value": "vs last period"}, {"key": "analytics.cards.total_conversations", "value": "Total Conversations"}, {"key": "analytics.cards.total_contacts", "value": "Total Contacts"}, {"key": "analytics.cards.total_messages", "value": "Total Messages"}, {"key": "analytics.cards.response_rate", "value": "Response Rate"}, {"key": "analytics.charts.conversations_by_channel", "value": "Conversations by Channel"}, {"key": "analytics.charts.avg_response_time", "value": "Average Response Time"}, {"key": "analytics.charts.channel_distribution", "value": "Channel Distribution"}, {"key": "analytics.charts.bot_performance", "value": "Bot <PERSON>"}, {"key": "analytics.response_time", "value": "Response Time"}, {"key": "analytics.channels.whatsapp_official", "value": "WhatsApp Official"}, {"key": "analytics.channels.whatsapp_unofficial", "value": "WhatsApp Unofficial"}, {"key": "analytics.channels.messenger", "value": "<PERSON>"}, {"key": "analytics.channels.instagram", "value": "Instagram"}, {"key": "analytics.tabs.overview", "value": "Overview"}, {"key": "analytics.tabs.top_intents", "value": "Top Intents"}, {"key": "analytics.tabs.flow_performance", "value": "Flow Performance"}, {"key": "analytics.bot.handling_title", "value": "<PERSON><PERSON>"}, {"key": "analytics.bot.handling_description", "value": "65% of all conversations were successfully handled by the AI assistant without human intervention."}, {"key": "analytics.bot.escalation_title", "value": "Human Escalation"}, {"key": "analytics.bot.escalation_description", "value": "35% of conversations required escalation to a human agent."}, {"key": "analytics.bot.escalation_reasons_title", "value": "Top Reasons for Escalation"}, {"key": "analytics.bot.reason_1", "value": "Complex product questions"}, {"key": "analytics.bot.reason_2", "value": "Pricing negotiations"}, {"key": "analytics.bot.reason_3", "value": "Technical support issues"}, {"key": "analytics.intents.product_info", "value": "Product Information"}, {"key": "analytics.intents.pricing", "value": "Pricing Inquiries"}, {"key": "analytics.intents.scheduling", "value": "Appointment Scheduling"}, {"key": "analytics.intents.support", "value": "Support Requests"}, {"key": "analytics.intents.other", "value": "Other"}, {"key": "analytics.flows.welcome", "value": "Welcome Flow"}, {"key": "analytics.flows.product_info", "value": "Product Information"}, {"key": "analytics.flows.support_ticket", "value": "Support Ticket"}, {"key": "analytics.flows.completion_rate", "value": "{{rate}}% completion"}, {"key": "analytics.flows.conversations_count", "value": "{{count}} conversations"}, {"key": "analytics.export.success", "value": "Export successful"}, {"key": "analytics.export.success_description", "value": "Analytics data has been exported successfully."}, {"key": "analytics.export.error", "value": "Export failed"}, {"key": "analytics.export.error_description", "value": "Failed to export analytics data."}, {"key": "groups.unnamed_group", "value": "Unnamed Group"}, {"key": "groups.participants", "value": "participants"}, {"key": "groups.group_info", "value": "Group Info"}, {"key": "groups.close_group_info", "value": "Close group info"}, {"key": "groups.description", "value": "Description"}, {"key": "groups.group_id", "value": "Group ID"}, {"key": "groups.created_date", "value": "Created"}, {"key": "groups.admin", "value": "Admin"}, {"key": "groups.super_admin", "value": "Group Admin"}, {"key": "groups.show_more", "value": "Show More"}, {"key": "groups.show_less", "value": "Show Less"}, {"key": "groups.unknown_participant", "value": "Unknown Participant"}, {"key": "groups.avatar.refresh_tooltip", "value": "Refresh group profile picture"}, {"key": "groups.participants.title", "value": "Group Participants"}, {"key": "groups.participants.view_all", "value": "View all participants"}, {"key": "groups.participants.export_participants", "value": "Export Participants"}, {"key": "groups.participants.export_csv", "value": "Export CSV"}, {"key": "groups.participants.search_placeholder", "value": "Search participants..."}, {"key": "groups.participants.showing_count", "value": "Showing {{count}} of {{total}} participants"}, {"key": "groups.participants.loading", "value": "Loading participants..."}, {"key": "groups.participants.error", "value": "Failed to load participants"}, {"key": "groups.participants.no_results", "value": "No participants found matching your search"}, {"key": "groups.participants.no_participants", "value": "No participants found"}, {"key": "groups.participants.member", "value": "Member"}, {"key": "groups.participants.admin", "value": "Admin"}, {"key": "groups.participants.super_admin", "value": "Super Admin"}, {"key": "groups.participants.joined", "value": "Joined"}, {"key": "groups.participants.export_success", "value": "Export successful"}, {"key": "groups.participants.export_success_desc", "value": "Participants list has been exported to CSV"}, {"key": "groups.participants.export_error", "value": "Failed to export participants list"}, {"key": "groups.participants.unknown_participant", "value": "Unknown Participant"}, {"key": "groups.participants.sync_participants", "value": "Sync Participants"}, {"key": "groups.participants.sync_success", "value": "Sync successful"}, {"key": "groups.participants.sync_success_desc", "value": "Participants have been synced from WhatsApp. Names will appear as participants send messages."}, {"key": "groups.participants.sync_error", "value": "Failed to sync participants"}, {"key": "groups.participants.name_info", "value": "Participant names appear when they send messages. Phone numbers are shown for participants who haven't been active yet."}, {"key": "common.unknown", "value": "Unknown"}, {"key": "admin.settings.platform", "value": "Platform"}, {"key": "admin.settings.partnerapi", "value": "Partner API"}, {"key": "admin.settings.title", "value": "Settings"}, {"key": "admin.settings.branding", "value": "Branding"}, {"key": "admin.settings.branding_description", "value": "Customize the appearance of your application"}, {"key": "admin.settings.payment_gateways", "value": "Payment Gateways"}, {"key": "admin.settings.email", "value": "<PERSON><PERSON>s"}, {"key": "admin.settings.general", "value": "General"}, {"key": "admin.settings.registration", "value": "Registration"}, {"key": "admin.settings.backup", "value": "Database Backup"}, {"key": "admin.settings.app_name", "value": "Application Name"}, {"key": "admin.settings.primary_color", "value": "Primary Color"}, {"key": "admin.settings.secondary_color", "value": "Secondary Color"}, {"key": "admin.settings.save_branding", "value": "Save Branding Settings"}, {"key": "admin.settings.platform.title", "value": "Platform Configuration"}, {"key": "admin.settings.platform.description", "value": "Configure platform-wide partner API integrations and Tech Provider credentials"}, {"key": "admin.settings.platform.360dialog.title", "value": "360Dialog Partner API"}, {"key": "admin.settings.platform.360dialog.description", "value": "Configure 360Dialog Partner credentials for company onboarding"}, {"key": "admin.settings.platform.meta.title", "value": "Meta WhatsApp Business API"}, {"key": "admin.settings.platform.meta.description", "value": "Configure Meta Tech Provider credentials for embedded signup"}, {"key": "admin.settings.platform.meta.benefits.embedded_signup", "value": "Tech Provider embedded signup integration"}, {"key": "admin.settings.platform.meta.benefits.streamlined_onboarding", "value": "Streamlined WhatsApp Business account onboarding"}, {"key": "admin.settings.platform.meta.benefits.automatic_provisioning", "value": "Automatic phone number provisioning"}, {"key": "admin.settings.platform.configure_button", "value": "Configure"}, {"key": "admin.settings.platform.coming_soon", "value": "Coming Soon"}, {"key": "admin.settings.platform.additional_partners.title", "value": "Additional Partner APIs"}, {"key": "admin.settings.platform.additional_partners.description", "value": "Future partner integrations will appear here"}, {"key": "meta.partner.config.title", "value": "Meta WhatsApp Business API Partner Configuration"}, {"key": "meta.partner.config.loading", "value": "Loading configuration..."}, {"key": "meta.partner.config.tech_provider_credentials", "value": "Tech Provider Credentials"}, {"key": "meta.partner.config.tech_provider_description", "value": "Configure your Meta Tech Provider credentials for embedded signup"}, {"key": "meta.partner.config.app_id", "value": "App ID"}, {"key": "meta.partner.config.app_id_placeholder", "value": "Your Meta App ID"}, {"key": "meta.partner.config.app_secret", "value": "App Secret"}, {"key": "meta.partner.config.app_secret_placeholder", "value": "Your Meta App Secret"}, {"key": "meta.partner.config.business_manager_id", "value": "Business Manager ID"}, {"key": "meta.partner.config.business_manager_id_placeholder", "value": "Your Business Manager ID"}, {"key": "meta.partner.config.webhook_verify_token", "value": "Webhook Verify Token"}, {"key": "meta.partner.config.webhook_verify_token_placeholder", "value": "Webhook verification token"}, {"key": "meta.partner.config.access_token", "value": "System User Access Token"}, {"key": "meta.partner.config.access_token_placeholder", "value": "System user access token"}, {"key": "meta.partner.config.webhook_url", "value": "Webhook URL"}, {"key": "meta.partner.config.webhook_url_placeholder", "value": "https://yourdomain.com/api/webhooks/meta-whatsapp"}, {"key": "meta.partner.config.company_profile", "value": "Company Profile"}, {"key": "meta.partner.config.company_profile_description", "value": "This information will be shown to companies during onboarding"}, {"key": "meta.partner.config.company_name", "value": "Company Name"}, {"key": "meta.partner.config.company_name_placeholder", "value": "Your company name"}, {"key": "meta.partner.config.logo_url", "value": "Logo URL"}, {"key": "meta.partner.config.logo_url_placeholder", "value": "https://example.com/logo.png"}, {"key": "meta.partner.config.test_configuration", "value": "Test Configuration"}, {"key": "meta.partner.config.save_configuration", "value": "Save Configuration"}, {"key": "meta.partner.config.update_configuration", "value": "Update Configuration"}, {"key": "meta.partner.config.validation.required_fields", "value": "App ID, App Secret, and Business Manager ID are required"}, {"key": "meta.partner.config.validation.success", "value": "Meta Partner API credentials are valid!"}, {"key": "meta.partner.config.validation.failed", "value": "Invalid Meta Partner API credentials"}, {"key": "meta.partner.config.validation.error", "value": "Failed to validate configuration"}, {"key": "meta.partner.config.save.success", "value": "Meta Partner API configuration updated successfully"}, {"key": "meta.partner.config.save.error", "value": "Failed to save configuration"}, {"key": "meta.partner.config.load.error", "value": "Failed to load existing configuration"}, {"key": "meta.whatsapp.onboarding.title", "value": "Meta WhatsApp Business API - Easy Setup"}, {"key": "meta.whatsapp.onboarding.description", "value": "Connect your WhatsApp Business account in just a few clicks using our integrated onboarding flow."}, {"key": "meta.whatsapp.onboarding.connection_name", "value": "Connection Name"}, {"key": "meta.whatsapp.onboarding.connection_name_placeholder", "value": "e.g., Main WhatsApp Business"}, {"key": "meta.whatsapp.onboarding.connection_name_help", "value": "Give this connection a memorable name"}, {"key": "meta.whatsapp.onboarding.what_happens_next", "value": "What happens next:"}, {"key": "meta.whatsapp.onboarding.step_connect_business", "value": "Connect your Meta Business account"}, {"key": "meta.whatsapp.onboarding.step_select_account", "value": "Select your WhatsApp Business account"}, {"key": "meta.whatsapp.onboarding.step_choose_numbers", "value": "Choose phone numbers to integrate"}, {"key": "meta.whatsapp.onboarding.step_automatic_config", "value": "Automatic configuration and setup"}, {"key": "meta.whatsapp.onboarding.sdk_initializing", "value": "Initializing Facebook SDK..."}, {"key": "meta.whatsapp.onboarding.start_easy_setup", "value": "Start Easy Setup"}, {"key": "meta.whatsapp.onboarding.processing", "value": "Processing..."}, {"key": "meta.whatsapp.onboarding.cancel", "value": "Cancel"}, {"key": "meta.whatsapp.onboarding.checking_config", "value": "Checking configuration..."}, {"key": "meta.whatsapp.onboarding.config_required.title", "value": "Configuration Required"}, {"key": "meta.whatsapp.onboarding.config_required.description", "value": "Meta WhatsApp Business API Partner integration is not configured. Please contact your system administrator to set up the Partner API credentials."}, {"key": "meta.whatsapp.onboarding.config_required.close", "value": "Close"}, {"key": "meta.whatsapp.onboarding.success", "value": "WhatsApp Business account connected successfully. {{count}} phone number(s) configured."}, {"key": "meta.whatsapp.onboarding.error.connection_name_required", "value": "Please enter a connection name"}, {"key": "meta.whatsapp.onboarding.error.sdk_not_initialized", "value": "Facebook SDK not initialized"}, {"key": "meta.whatsapp.onboarding.error.launch_signup", "value": "Failed to launch WhatsApp signup"}, {"key": "meta.whatsapp.onboarding.error.signup_failed", "value": "Failed to process WhatsApp Business account signup"}, {"key": "meta.whatsapp.onboarding.error.process_signup", "value": "Failed to process signup"}, {"key": "meta.whatsapp.onboarding.error.sdk_init", "value": "Failed to initialize Facebook SDK"}, {"key": "channels.whatsapp.meta.easy_setup", "value": "Easy Setup"}, {"key": "channels.whatsapp.meta.title", "value": "WhatsApp Business API (Meta)"}, {"key": "channels.whatsapp.meta.description", "value": "Official Meta WhatsApp Business API"}, {"key": "channels.whatsapp.meta.embedded.title", "value": "WhatsApp Business Embedded"}, {"key": "channels.whatsapp.meta.embedded.description", "value": "Quick setup with embedded signup"}, {"key": "channels.whatsapp.meta.connection_info", "value": "Official WhatsApp Business API (Meta)"}, {"key": "channels.whatsapp.meta.connection_description", "value": "This connection uses the official WhatsApp Business API from Meta. It provides reliable messaging with advanced features and compliance."}, {"key": "common.success", "value": "Success"}, {"key": "common.error", "value": "Error"}, {"key": "common.validation_failed", "value": "Validation Failed"}, {"key": "common.signup_error", "value": "Signup Error"}, {"key": "common.sdk_error", "value": "SDK Error"}, {"key": "common.configuration_error", "value": "Configuration Error"}, {"key": "partner.config.success.created", "value": "Partner API configuration created successfully"}, {"key": "partner.config.success.updated", "value": "Partner API configuration updated successfully"}, {"key": "partner.config.error.load_failed", "value": "Failed to load existing configuration"}, {"key": "partner.config.error.save_failed", "value": "Failed to save configuration"}, {"key": "partner.config.error.validation_failed", "value": "Failed to validate configuration"}, {"key": "api.access.title", "value": "API Access"}, {"key": "api.access.description", "value": "Manage API keys and programmatic access to your channels"}, {"key": "api.access.create_key", "value": "Create API Key"}, {"key": "api.access.no_keys.title", "value": "No API Keys"}, {"key": "api.access.no_keys.description", "value": "Create your first API key to start sending messages programmatically"}, {"key": "api.access.tabs.keys", "value": "API Keys"}, {"key": "api.access.tabs.usage", "value": "Usage Statistics"}, {"key": "api.access.tabs.docs", "value": "Documentation"}, {"key": "api.access.key.active", "value": "Active"}, {"key": "api.access.key.inactive", "value": "Inactive"}, {"key": "api.access.key.activate", "value": "Activate"}, {"key": "api.access.key.deactivate", "value": "Deactivate"}, {"key": "api.access.key.delete", "value": "Delete"}, {"key": "api.access.key.created", "value": "Created"}, {"key": "api.access.key.last_used", "value": "Last Used"}, {"key": "api.access.key.never_used", "value": "Never"}, {"key": "api.access.key.rate_limits", "value": "Rate Limits"}, {"key": "api.access.key.permissions", "value": "Permissions"}, {"key": "api.access.create.title", "value": "Create New API Key"}, {"key": "api.access.create.description", "value": "Create a new API key to access the messaging API programmatically."}, {"key": "api.access.create.name_label", "value": "API Key Name"}, {"key": "api.access.create.name_placeholder", "value": "e.g., Production Bot, Marketing Automation"}, {"key": "api.access.create.creating", "value": "Creating..."}, {"key": "api.access.create.cancel", "value": "Cancel"}, {"key": "api.access.show_key.title", "value": "API Key Created"}, {"key": "api.access.show_key.description", "value": "Your API key has been created. Copy it now as it won't be shown again."}, {"key": "api.access.show_key.warning", "value": "Store this API key securely. You won't be able to see it again."}, {"key": "api.access.show_key.saved", "value": "I've Saved the Key"}, {"key": "api.access.usage.total_requests", "value": "Total Requests"}, {"key": "api.access.usage.successful", "value": "Successful"}, {"key": "api.access.usage.failed", "value": "Failed"}, {"key": "api.access.usage.avg_duration", "value": "Avg Duration"}, {"key": "api.access.usage.data_transfer", "value": "Data Transfer"}, {"key": "api.access.docs.title", "value": "API Documentation"}, {"key": "api.access.docs.description", "value": "Learn how to integrate with our messaging API"}, {"key": "api.access.docs.base_url", "value": "Base URL"}, {"key": "api.access.docs.endpoints", "value": "Available Endpoints"}, {"key": "api.access.docs.authentication", "value": "Authentication"}, {"key": "api.access.docs.auth_description", "value": "Include your API key in the Authorization header:"}, {"key": "api.access.error.name_required", "value": "Please enter a name for the API key"}, {"key": "api.access.error.create_failed", "value": "Failed to create API key"}, {"key": "api.access.error.delete_failed", "value": "Failed to delete API key"}, {"key": "api.access.error.update_failed", "value": "Failed to update API key"}, {"key": "api.access.error.load_failed", "value": "Failed to load API keys"}, {"key": "api.access.success.created", "value": "API key created successfully"}, {"key": "api.access.success.deleted", "value": "API key deleted successfully"}, {"key": "api.access.success.activated", "value": "API key activated successfully"}, {"key": "api.access.success.deactivated", "value": "API key deactivated successfully"}, {"key": "api.access.success.copied", "value": "API key copied to clipboard"}, {"key": "landing.nav.features", "value": "Features"}, {"key": "landing.nav.pricing", "value": "Pricing"}, {"key": "landing.nav.about", "value": "About"}, {"key": "landing.nav.contact", "value": "Contact"}, {"key": "landing.nav.sign_in", "value": "Sign In"}, {"key": "landing.nav.get_started", "value": "Get Started"}, {"key": "landing.hero.title", "value": "Ready to transform your customer communication?"}, {"key": "landing.hero.subtitle", "value": "Join thousands of businesses using PowerChat to streamline their customer interactions and boost satisfaction rates."}, {"key": "landing.hero.start_free_trial", "value": "Start Free Trial"}, {"key": "landing.hero.sign_in", "value": "Sign In"}, {"key": "landing.trust.enterprise_security", "value": "Enterprise Security"}, {"key": "landing.trust.uptime", "value": "99.9% Uptime"}, {"key": "landing.trust.soc2_compliant", "value": "SOC 2 Compliant"}, {"key": "landing.pricing.title", "value": "Simple, transparent pricing"}, {"key": "landing.pricing.subtitle", "value": "Choose the perfect plan for your business. Start free, upgrade when you need more."}, {"key": "landing.pricing.loading", "value": "Loading pricing plans..."}, {"key": "landing.pricing.error", "value": "Failed to load pricing plans"}, {"key": "landing.pricing.retry", "value": "Retry"}, {"key": "landing.pricing.most_popular", "value": "Most Popular"}, {"key": "landing.pricing.per_month", "value": "per month"}, {"key": "landing.pricing.forever", "value": "forever"}, {"key": "landing.pricing.free", "value": "Free"}, {"key": "landing.pricing.get_started_free", "value": "Get Started Free"}, {"key": "landing.pricing.start_trial", "value": "Start {{days}}-Day Free Trial"}, {"key": "landing.pricing.get_started", "value": "Get Started"}, {"key": "landing.pricing.users", "value": "Up to {{count}} users"}, {"key": "landing.pricing.contacts", "value": "{{count}} contacts"}, {"key": "landing.pricing.channels", "value": "{{count}} channels"}, {"key": "landing.pricing.flows", "value": "{{count}} flows"}, {"key": "landing.cta.title", "value": "Ready to transform your customer communication?"}, {"key": "landing.cta.subtitle", "value": "Join thousands of businesses using PowerChat to streamline their customer interactions and boost satisfaction rates."}, {"key": "landing.cta.start_free_trial", "value": "Start Free Trial"}, {"key": "landing.cta.sign_in", "value": "Sign In"}, {"key": "zoho_calendar.oauth_not_configured", "value": "Zoho Calendar OAuth not configured in admin settings"}, {"key": "zoho_calendar.oauth_not_properly_configured", "value": "Zoho Calendar OAuth not properly configured or disabled"}, {"key": "zoho_calendar.no_credentials_available", "value": "No credentials available for auth URL generation"}, {"key": "zoho_calendar.auth_code_not_provided", "value": "Authorization code not provided"}, {"key": "zoho_calendar.invalid_state_parameter", "value": "Invalid state parameter"}, {"key": "zoho_calendar.auth_failed", "value": "Failed to authenticate with <PERSON><PERSON><PERSON>"}, {"key": "zoho_calendar.invalid_client_id", "value": "Invalid Zoho Client ID. Please check your Zoho Developer Console configuration."}, {"key": "zoho_calendar.invalid_request", "value": "Invalid OAuth request. Please check your Zoho application settings."}, {"key": "zoho_calendar.access_denied", "value": "Access denied. Please ensure you have the required permissions."}, {"key": "zoho_calendar.client_not_available", "value": "Zoho Calendar client not available"}, {"key": "zoho_calendar.primary_calendar_not_found", "value": "Primary calendar not found"}, {"key": "zoho_calendar.no_calendars_found", "value": "No calendars found for user"}, {"key": "zoho_calendar.no_suitable_calendar_found", "value": "No suitable calendar found"}, {"key": "zoho_calendar.calendar_no_id_field", "value": "Calendar found but no ID field"}, {"key": "zoho_calendar.start_end_times_required", "value": "Start and end times are required"}, {"key": "zoho_calendar.all_endpoints_failed", "value": "All API endpoints failed"}, {"key": "zoho_calendar.failed_create_event", "value": "Failed to create calendar event"}, {"key": "zoho_calendar.failed_list_events", "value": "Failed to list calendar events"}, {"key": "zoho_calendar.failed_fetch_current_event", "value": "Failed to fetch current event for update"}, {"key": "zoho_calendar.event_not_found_update", "value": "Event not found for update"}, {"key": "zoho_calendar.failed_update_event", "value": "Failed to update calendar event"}, {"key": "zoho_calendar.failed_delete_event", "value": "Failed to delete calendar event"}, {"key": "zoho_calendar.not_connected", "value": "Not connected to Zoho Calendar"}, {"key": "zoho_calendar.connection_failed", "value": "Connection to Zoho Calendar failed"}, {"key": "zoho_calendar.connected", "value": "Connected to Zoho Calendar"}, {"key": "zoho_calendar.connected_token_refreshed", "value": "Connected to Zoho Calendar (token refreshed)"}, {"key": "zoho_calendar.error_checking_connection", "value": "Error checking Zoho Calendar connection"}, {"key": "zoho_calendar.date_range_required", "value": "Date or date range is required"}, {"key": "zoho_calendar.failed_get_availability", "value": "Failed to get available time slots"}, {"key": "calendar.create_events_with", "value": "Create Events With:"}, {"key": "calendar.google_calendar", "value": "Google Calendar"}, {"key": "calendar.zoho_calendar", "value": "Zoho Calendar"}, {"key": "calendar.view", "value": "View"}, {"key": "calendar.all_calendars", "value": "All Calendars"}, {"key": "calendar.google", "value": "Google"}, {"key": "calendar.zoho", "value": "<PERSON><PERSON><PERSON>"}, {"key": "calendar.personal_teams", "value": "Personal, Teams"}, {"key": "calendar.enter_email_address", "value": "Enter email address"}, {"key": "calendar.creating", "value": "Creating..."}, {"key": "calendar.create_appointment", "value": "Create Appointment"}, {"key": "calendar.edit_appointment", "value": "Edit Appointment"}, {"key": "calendar.from", "value": "from"}, {"key": "calendar.update_appointment_details", "value": "Update the details of your appointment."}, {"key": "calendar.color.blue", "value": "Blue"}, {"key": "calendar.color.green", "value": "Green"}, {"key": "calendar.color.purple", "value": "Purple"}, {"key": "calendar.color.red", "value": "Red"}, {"key": "calendar.color.yellow", "value": "Yellow"}, {"key": "calendar.color.orange", "value": "Orange"}, {"key": "calendar.color.turquoise", "value": "Turquoise"}, {"key": "calendar.color.gray", "value": "<PERSON>"}, {"key": "calendar.updating", "value": "Updating..."}, {"key": "calendar.update_appointment", "value": "Update Appointment"}, {"key": "calendar.schedule_new_appointment", "value": "Schedule New Appointment"}, {"key": "calendar.create_new_event", "value": "Create a new event on your calendar."}, {"key": "calendar.categories", "value": "Categories"}, {"key": "calendar.sun", "value": "Sun"}, {"key": "calendar.mon", "value": "Mon"}, {"key": "calendar.tue", "value": "<PERSON><PERSON>"}, {"key": "calendar.wed", "value": "Wed"}, {"key": "calendar.thu", "value": "<PERSON>hu"}, {"key": "calendar.fri", "value": "<PERSON><PERSON>"}, {"key": "calendar.sat", "value": "Sat"}, {"key": "calendar.more", "value": "more"}, {"key": "calendar.week_of", "value": "Week of"}, {"key": "calendar.check_availability", "value": "Check Availability"}, {"key": "calendar.schedule_appointment", "value": "Schedule Appointment"}, {"key": "calendar.no_available_slots", "value": "No available time slots found for the selected date and duration"}, {"key": "calendar.checking", "value": "Checking..."}, {"key": "calendar.refresh_availability", "value": "Refresh Availability"}, {"key": "calendar.schedule.work", "value": "Work"}, {"key": "calendar.schedule.personal", "value": "Personal"}, {"key": "calendar.schedule.family", "value": "Family"}, {"key": "calendar.schedule.health", "value": "Health"}, {"key": "calendar.schedule.projects", "value": "Projects"}, {"key": "calendar.canceling", "value": "Canceling..."}, {"key": "calendar.yes_cancel_appointment", "value": "Yes, <PERSON><PERSON> Appointment"}, {"key": "calendar.name", "value": "Name"}, {"key": "calendar.enter_schedule_name", "value": "Enter schedule name"}, {"key": "calendar.color", "value": "Color"}, {"key": "calendar.select_color", "value": "Select color"}, {"key": "calendar.my_schedules", "value": "My Schedules"}, {"key": "calendar.duration", "value": "Duration"}, {"key": "calendar.duration.15_minutes", "value": "15 minutes"}, {"key": "calendar.duration.30_minutes", "value": "30 minutes"}, {"key": "calendar.duration.60_minutes", "value": "60 minutes"}, {"key": "calendar.duration.90_minutes", "value": "90 minutes"}, {"key": "settings.updated", "value": "Settings Updated"}, {"key": "settings.update_failed", "value": "Failed to update settings"}, {"key": "settings.browser_notifications_enabled", "value": "Browser notifications are now enabled"}, {"key": "settings.browser_notifications_disabled", "value": "Browser notifications are now disabled"}, {"key": "settings.notifications_not_supported", "value": "Notifications Not Supported"}, {"key": "settings.notifications_not_supported_desc", "value": "Your browser does not support notifications."}, {"key": "settings.notification_permission_denied", "value": "Permission Denied"}, {"key": "settings.notification_permission_denied_desc", "value": "Please enable notifications in your browser settings to receive alerts."}, {"key": "settings.inbox.browser_notifications", "value": "Browser Notifications"}, {"key": "settings.inbox.browser_notifications_description", "value": "Receive desktop notifications when new messages arrive (only when the page is not in focus)"}, {"key": "settings.inbox.notifications_blocked", "value": "Notifications are blocked. Please enable them in your browser settings."}, {"key": "settings.inbox.notifications_permission_needed", "value": "Permission will be requested when you enable notifications."}, {"key": "settings.inbox.browser_notifications_enabled_info", "value": "You will receive desktop notifications for new messages when the page is not in focus. You can test notifications using the button below."}, {"key": "settings.inbox.test_notification", "value": "Test Notification"}, {"key": "notifications.conversation_opened", "value": "Conversation Opened"}, {"key": "notifications.navigated_to_conversation", "value": "Navigated to the conversation from notification"}, {"key": "backup.title", "value": "Inbox Backup & Restore"}, {"key": "backup.description", "value": "Create backups of your inbox data and restore from previous backups. Media files are excluded to keep backup sizes manageable."}, {"key": "backup.create_backup", "value": "Create Backup"}, {"key": "backup.history_title", "value": "Backup History"}, {"key": "backup.history_description", "value": "View and manage your backup files"}, {"key": "backup.no_backups", "value": "No backups found"}, {"key": "backup.create_first_backup", "value": "Create your first backup to get started"}, {"key": "backup.contacts", "value": "contacts"}, {"key": "backup.conversations", "value": "conversations"}, {"key": "backup.messages", "value": "messages"}, {"key": "backup.created", "value": "Created"}, {"key": "backup.completed", "value": "Completed"}, {"key": "backup.download", "value": "Download"}, {"key": "backup.delete", "value": "Delete"}, {"key": "backup.scheduled", "value": "Scheduled"}, {"key": "backup.status.pending", "value": "Pending"}, {"key": "backup.status.in_progress", "value": "In Progress"}, {"key": "backup.status.completed", "value": "Completed"}, {"key": "backup.status.failed", "value": "Failed"}, {"key": "backup.status.cancelled", "value": "Cancelled"}, {"key": "backup.create_backup_title", "value": "Create New Backup"}, {"key": "backup.create_backup_desc", "value": "Configure your backup settings. Media files will be excluded to keep the backup size manageable."}, {"key": "backup.name", "value": "Backup Name"}, {"key": "backup.name_placeholder", "value": "Enter backup name"}, {"key": "backup.description_optional", "value": "Description (Optional)"}, {"key": "backup.description_placeholder", "value": "Enter backup description"}, {"key": "backup.include_data", "value": "Include Data"}, {"key": "backup.include_contacts", "value": "Contacts"}, {"key": "backup.include_conversations", "value": "Conversations"}, {"key": "backup.include_messages", "value": "Messages (Text Only)"}, {"key": "backup.date_range", "value": "Date Range (Optional)"}, {"key": "backup.start_date", "value": "Start Date"}, {"key": "backup.end_date", "value": "End Date"}, {"key": "backup.create", "value": "Create Backup"}, {"key": "backup.create_success_title", "value": "Backup Started"}, {"key": "backup.create_success_desc", "value": "Your backup has been queued and will be processed shortly."}, {"key": "backup.create_error_title", "value": "Backup Failed"}, {"key": "backup.validation_error", "value": "Validation Error"}, {"key": "backup.name_required", "value": "Backup name is required"}, {"key": "backup.download_error_title", "value": "Download Error"}, {"key": "backup.download_not_ready", "value": "Backup is not ready for download"}, {"key": "backup.download_success_title", "value": "Download Started"}, {"key": "backup.download_success_desc", "value": "Your backup file is being downloaded."}, {"key": "backup.download_error_desc", "value": "Failed to download backup file"}, {"key": "backup.delete_confirm_title", "value": "Delete Backup"}, {"key": "backup.delete_confirm_desc", "value": "Are you sure you want to delete this backup? This action cannot be undone."}, {"key": "backup.delete_success_title", "value": "Backup Deleted"}, {"key": "backup.delete_success_desc", "value": "The backup has been deleted successfully."}, {"key": "backup.delete_error_title", "value": "Delete Failed"}, {"key": "restore.title", "value": "Restore from Backup"}, {"key": "restore.description", "value": "Restore your inbox data from a previous backup or upload a backup file."}, {"key": "restore.start_restore", "value": "Start Restore"}, {"key": "restore.history_title", "value": "Restore History"}, {"key": "restore.history_description", "value": "View your previous restore operations"}, {"key": "restore.no_restores", "value": "No restore operations found"}, {"key": "restore.start_first_restore", "value": "Start your first restore to get started"}, {"key": "restore.full_restore", "value": "Full Restore"}, {"key": "restore.selective_restore", "value": "Selective Restore"}, {"key": "restore.conflict_resolution", "value": "Conflict Resolution"}, {"key": "restore.items_restored", "value": "Restored"}, {"key": "restore.items_skipped", "value": "Skipped"}, {"key": "restore.items_errored", "value": "Errors"}, {"key": "restore.started", "value": "Started"}, {"key": "restore.completed", "value": "Completed"}, {"key": "restore.status.pending", "value": "Pending"}, {"key": "restore.status.in_progress", "value": "In Progress"}, {"key": "restore.status.completed", "value": "Completed"}, {"key": "restore.status.failed", "value": "Failed"}, {"key": "restore.status.cancelled", "value": "Cancelled"}, {"key": "restore.configure_title", "value": "Configure <PERSON>"}, {"key": "restore.configure_desc", "value": "Select a backup source and configure restore options."}, {"key": "restore.backup_source", "value": "Backup Source"}, {"key": "restore.existing_backup", "value": "Existing Backup"}, {"key": "restore.select_backup", "value": "Select a backup"}, {"key": "restore.or", "value": "OR"}, {"key": "restore.upload_backup", "value": "Upload Backup File"}, {"key": "restore.choose_file", "value": "Choose backup file"}, {"key": "restore.restore_type", "value": "Restore Type"}, {"key": "restore.merge", "value": "Merge (Recommended)"}, {"key": "restore.overwrite", "value": "Overwrite"}, {"key": "restore.skip", "value": "<PERSON><PERSON> Existing"}, {"key": "restore.merge_desc", "value": "Combine existing data with backup data"}, {"key": "restore.overwrite_desc", "value": "Replace existing data with backup data"}, {"key": "restore.skip_desc", "value": "Keep existing data, skip backup data"}, {"key": "restore.select_data", "value": "Select Data to Restore"}, {"key": "restore.restore_contacts", "value": "Contacts"}, {"key": "restore.restore_conversations", "value": "Conversations"}, {"key": "restore.restore_messages", "value": "Messages"}, {"key": "restore.date_range", "value": "Date Range (Optional)"}, {"key": "restore.start_date", "value": "Start Date"}, {"key": "restore.end_date", "value": "End Date"}, {"key": "restore.create_success_title", "value": "<PERSON>ore Started"}, {"key": "restore.create_success_desc", "value": "Your restore process has been started and will be processed shortly."}, {"key": "restore.create_error_title", "value": "Restore Failed"}, {"key": "restore.file_error_title", "value": "Invalid File"}, {"key": "restore.file_error_desc", "value": "Please select a valid backup file (.gz format)"}, {"key": "restore.validation_error", "value": "Validation Error"}, {"key": "restore.source_required", "value": "Please select a backup or upload a backup file"}, {"key": "restore.confirm_title", "value": "Confirm <PERSON>ore"}, {"key": "restore.confirm_desc", "value": "Are you sure you want to start the restore process? This will modify your existing data based on the selected conflict resolution strategy."}, {"key": "restore.restore_summary", "value": "<PERSON><PERSON> Summary:"}, {"key": "restore.type", "value": "Type"}, {"key": "restore.source", "value": "Source"}, {"key": "restore.backup_id", "value": "Backup ID"}, {"key": "restore.confirm_restore", "value": "Confirm <PERSON>ore"}, {"key": "nav.grace_period", "value": "Grace period"}, {"key": "nav.subscription_expired", "value": "Subscription expired"}, {"key": "nav.expires_in", "value": "Expires in"}, {"key": "nav.renews_in", "value": "Renews in"}, {"key": "nav.renewal_due", "value": "Renewal due"}]