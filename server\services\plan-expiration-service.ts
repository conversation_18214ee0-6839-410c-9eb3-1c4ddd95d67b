import { db } from '../db';
import {
  companies,
  plans,
  Company,
  Plan
} from '@shared/schema';
import { eq, and, lte } from 'drizzle-orm';
import { storage } from '../storage';
import { logger } from '../utils/logger';

export interface PlanExpirationStatus {
  isExpired: boolean;
  isInGracePeriod: boolean;
  daysUntilExpiry: number;
  gracePeriodDaysRemaining: number;
  subscriptionStatus: string;
  canAccess: boolean;
  blockReason?: string;
  renewalRequired: boolean;
  nextBillingDate?: Date;
}

export interface CompanyAccessCheck {
  allowed: boolean;
  reason?: string;
  expirationStatus: PlanExpirationStatus;
  company: Company;
  plan: Plan | null;
}

/**
 * Service for checking plan expiration and enforcing access restrictions
 */
export class PlanExpirationService {
  
  /**
   * Check if a company's plan has expired and determine access permissions
   */
  async checkCompanyAccess(companyId: number): Promise<CompanyAccessCheck> {
    try {
      const company = await storage.getCompany(companyId);
      if (!company) {
        return {
          allowed: false,
          reason: 'Company not found',
          expirationStatus: this.getDefaultExpirationStatus(),
          company: null as any,
          plan: null
        };
      }


      if (company.name === 'PowerChat Admin' || company.slug === 'powerchat-admin') {
        return {
          allowed: true,
          expirationStatus: this.getDefaultExpirationStatus(),
          company,
          plan: null
        };
      }

      const plan = company.planId ? await storage.getPlan(company.planId) : null;
      const expirationStatus = await this.getExpirationStatus(company, plan);


      const accessAllowed = this.shouldAllowAccess(expirationStatus);

      return {
        allowed: accessAllowed,
        reason: accessAllowed ? undefined : expirationStatus.blockReason,
        expirationStatus,
        company,
        plan
      };

    } catch (error) {
      logger.error('plan-expiration-service', 'Error checking company access:', error);
      return {
        allowed: false,
        reason: 'Error checking access permissions',
        expirationStatus: this.getDefaultExpirationStatus(),
        company: null as any,
        plan: null
      };
    }
  }

  /**
   * Get detailed expiration status for a company
   */
  async getExpirationStatus(company: Company, plan: Plan | null): Promise<PlanExpirationStatus> {
    const now = new Date();
    

    if (!plan || plan.isFree) {
      return {
        isExpired: false,
        isInGracePeriod: false,
        daysUntilExpiry: Infinity,
        gracePeriodDaysRemaining: 0,
        subscriptionStatus: company.subscriptionStatus || 'active',
        canAccess: true,
        renewalRequired: false,
        nextBillingDate: undefined
      };
    }

    const subscriptionStatus = company.subscriptionStatus || 'inactive';
    const subscriptionEndDate = company.subscriptionEndDate;
    const gracePeriodEnd = company.gracePeriodEnd;
    const trialEndDate = company.trialEndDate;
    const isInTrial = company.isInTrial || false;


    if (plan.hasTrialPeriod && isInTrial && trialEndDate) {
      const trialDaysRemaining = Math.ceil((trialEndDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      const isTrialExpired = now > trialEndDate;

      if (!isTrialExpired) {

        return {
          isExpired: false,
          isInGracePeriod: false,
          daysUntilExpiry: trialDaysRemaining,
          gracePeriodDaysRemaining: 0,
          subscriptionStatus: 'trial',
          canAccess: true,
          renewalRequired: false,
          nextBillingDate: trialEndDate
        };
      } else {

        const isInGracePeriod = subscriptionStatus === 'grace_period' && gracePeriodEnd ? now <= gracePeriodEnd : false;
        
        if (isInGracePeriod) {
          const gracePeriodDaysRemaining = Math.max(0, Math.ceil((gracePeriodEnd.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)));
          return {
            isExpired: true,
            isInGracePeriod: true,
            daysUntilExpiry: 0,
            gracePeriodDaysRemaining,
            subscriptionStatus,
            canAccess: true,
            renewalRequired: true,
            blockReason: `Your trial has expired. You have ${gracePeriodDaysRemaining} day(s) remaining in your grace period.`,
            nextBillingDate: gracePeriodEnd
          };
        } else {

          return {
            isExpired: true,
            isInGracePeriod: false,
            daysUntilExpiry: 0,
            gracePeriodDaysRemaining: 0,
            subscriptionStatus,
            canAccess: false,
            renewalRequired: true,
            blockReason: 'Your trial period has expired. Please upgrade to a paid plan to continue using PowerChat.',
            nextBillingDate: undefined
          };
        }
      }
    }


    let daysUntilExpiry = Infinity;
    if (subscriptionEndDate) {
      daysUntilExpiry = Math.ceil((subscriptionEndDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    }


    let gracePeriodDaysRemaining = 0;
    if (gracePeriodEnd) {
      gracePeriodDaysRemaining = Math.max(0, Math.ceil((gracePeriodEnd.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)));
    }


    const isExpired = subscriptionEndDate ? now > subscriptionEndDate : false;
    const isInGracePeriod = subscriptionStatus === 'grace_period' && gracePeriodEnd ? now <= gracePeriodEnd : false;


    if (subscriptionStatus === 'active' && !isExpired) {
      return {
        isExpired: false,
        isInGracePeriod: false,
        daysUntilExpiry,
        gracePeriodDaysRemaining: 0,
        subscriptionStatus,
        canAccess: true,
        renewalRequired: false,
        nextBillingDate: subscriptionEndDate
      };
    }


    if (isInGracePeriod) {
      return {
        isExpired: true,
        isInGracePeriod: true,
        daysUntilExpiry: 0,
        gracePeriodDaysRemaining,
        subscriptionStatus,
        canAccess: true,
        renewalRequired: true,
        blockReason: `Your subscription has expired. You have ${gracePeriodDaysRemaining} day(s) remaining in your grace period.`,
        nextBillingDate: gracePeriodEnd
      };
    }


    let canAccess = true;
    let blockReason: string | undefined;
    let renewalRequired = false;


    if (isExpired && !isInGracePeriod) {
      canAccess = false;
      renewalRequired = true;
      
      if (subscriptionStatus === 'cancelled') {
        blockReason = 'Subscription has been cancelled. Please renew to continue using PowerChat.';
      } else if (subscriptionStatus === 'past_due' || subscriptionStatus === 'overdue') {
        blockReason = 'Payment is overdue. Please update your payment method to continue using PowerChat.';
      } else {
        blockReason = 'Your subscription has expired. Please renew to continue using PowerChat.';
      }
    }


    if (['inactive', 'cancelled'].includes(subscriptionStatus) && !isInGracePeriod) {
      canAccess = false;
      renewalRequired = true;
      blockReason = 'Your subscription is inactive. Please renew to continue using PowerChat.';
    }

    return {
      isExpired,
      isInGracePeriod,
      daysUntilExpiry,
      gracePeriodDaysRemaining,
      subscriptionStatus,
      canAccess,
      blockReason,
      renewalRequired,
      nextBillingDate: subscriptionEndDate || gracePeriodEnd
    };
  }

  /**
   * Determine if access should be allowed based on expiration status
   */
  private shouldAllowAccess(expirationStatus: PlanExpirationStatus): boolean {

    if (expirationStatus.canAccess) {
      return true;
    }


    if (['active', 'trial'].includes(expirationStatus.subscriptionStatus)) {
      return true;
    }


    if (expirationStatus.isInGracePeriod) {
      return true;
    }


    return false;
  }

  /**
   * Get default expiration status for error cases
   */
  private getDefaultExpirationStatus(): PlanExpirationStatus {
    return {
      isExpired: false,
      isInGracePeriod: false,
      daysUntilExpiry: 0,
      gracePeriodDaysRemaining: 0,
      subscriptionStatus: 'inactive',
      canAccess: false,
      blockReason: 'Unable to verify subscription status',
      renewalRequired: true
    };
  }
}


export const planExpirationService = new PlanExpirationService();
