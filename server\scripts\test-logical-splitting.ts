#!/usr/bin/env tsx

/**
 * Test script for the new logical message splitting functionality
 * This script tests the custom delimiter splitting for Mandarin conversations
 */

import whatsAppService from '../services/channels/whatsapp';

async function testLogicalSplitting() {



  const testCases = [
    {
      name: 'Mandarin with || delimiter',
      message: '欢迎光临！||今天我能为您提供什么帮助？||您可以随时提问哦',
      expectedChunks: 3
    },
    {
      name: 'English with || delimiter',
      message: 'Welcome to our service!||How can I help you today?||Feel free to ask any questions.',
      expectedChunks: 3
    },
    {
      name: 'Mixed content with || delimiter',
      message: 'Hello! 您好！||This is a test message.||这是一个测试消息。',
      expectedChunks: 3
    },
    {
      name: 'Single message without delimiter',
      message: 'This is a single message without any delimiters.',
      expectedChunks: 1
    },
    {
      name: 'Empty delimiter chunks (should be filtered)',
      message: 'Start||  ||End||',
      expectedChunks: 2
    },
    {
      name: 'Long message with delimiter',
      message: 'This is a very long message that would normally be split by character count, but since it has a logical delimiter||it should be split here instead of by character limits.',
      expectedChunks: 2
    }
  ];


  whatsAppService.configureMessageSplitting({
    logicalSplitting: {
      enabled: true,
      delimiter: '||',
      fallbackToCharacters: true
    }
  });




  let passedTests = 0;
  let totalTests = testCases.length;

  for (const testCase of testCases) {


    
    try {
      const result = whatsAppService.testMessageSplitting(testCase.message);
      const actualChunks = result.totalChunks;
      


      
      if (actualChunks === testCase.expectedChunks) {

        passedTests++;
      } else {


        result.chunks.forEach((chunk, index) => {

        });

      }
    } catch (error) {

    }
  }



  

  whatsAppService.configureMessageSplitting({
    logicalSplitting: {
      enabled: true,
      delimiter: '||',
      fallbackToCharacters: false
    }
  });

  const fallbackTest = {
    name: 'Long message without delimiter (fallback disabled)',
    message: 'This is a very long message that would normally be split by character count, but since fallback is disabled and there are no logical delimiters, it should remain as a single message.',
    expectedChunks: 1
  };



  
  try {
    const result = whatsAppService.testMessageSplitting(fallbackTest.message);
    const actualChunks = result.totalChunks;
    


    
    if (actualChunks === fallbackTest.expectedChunks) {

      passedTests++;
      totalTests++;
    } else {

      totalTests++;
    }
  } catch (error) {

    totalTests++;
  }





  
  if (passedTests === totalTests) {

  } else {

  }
}


if (require.main === module) {
  testLogicalSplitting()
    .then(() => {

      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Testing failed:', error);
      process.exit(1);
    });
}

export { testLogicalSplitting };
